import api from './api';

/**
 * Service for handling notification operations
 */
const notificationService = {
  /**
   * Get all notifications for current user
   * @param {Object} params - Query parameters
   * @returns {Promise} - Response from API
   */
  getNotifications: async (params = {}) => {
    try {
      const response = await api.get('/notifications/', { params });
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  /**
   * Get unread notifications count
   * @returns {Promise} - Response from API
   */
  getUnreadCount: async () => {
    try {
      const response = await api.get('/notifications/unread/count/');
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  /**
   * Mark notification as read
   * @param {string} notificationId - Notification ID
   * @returns {Promise} - Response from API
   */
  markAsRead: async (notificationId) => {
    try {
      const response = await api.put(`/notifications/${notificationId}/read/`);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  /**
   * Mark all notifications as read
   * @returns {Promise} - Response from API
   */
  markAllAsRead: async () => {
    try {
      const response = await api.put('/notifications/read-all/');
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  /**
   * Delete notification
   * @param {string} notificationId - Notification ID
   * @returns {Promise} - Response from API
   */
  deleteNotification: async (notificationId) => {
    try {
      const response = await api.delete(`/notifications/${notificationId}/`);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  /**
   * Delete all notifications
   * @returns {Promise} - Response from API
   */
  deleteAllNotifications: async () => {
    try {
      const response = await api.delete('/notifications/delete-all/');
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  /**
   * Get notification preferences
   * @returns {Promise} - Response from API
   */
  getPreferences: async () => {
    try {
      const response = await api.get('/notifications/preferences/');
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  /**
   * Update notification preferences
   * @param {Object} preferences - Notification preferences
   * @returns {Promise} - Response from API
   */
  updatePreferences: async (preferences) => {
    try {
      const response = await api.put('/notifications/preferences/', preferences);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  /**
   * Send notification (admin only)
   * @param {Object} notificationData - Notification data
   * @returns {Promise} - Response from API
   */
  sendNotification: async (notificationData) => {
    try {
      const response = await api.post('/notifications/send/', notificationData);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  /**
   * Send bulk notification (admin only)
   * @param {Object} notificationData - Bulk notification data
   * @returns {Promise} - Response from API
   */
  sendBulkNotification: async (notificationData) => {
    try {
      const response = await api.post('/notifications/send-bulk/', notificationData);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  /**
   * Subscribe to push notifications
   * @param {Object} subscription - Push subscription data
   * @returns {Promise} - Response from API
   */
  subscribeToPush: async (subscription) => {
    try {
      const response = await api.post('/notifications/push/subscribe/', subscription);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  /**
   * Unsubscribe from push notifications
   * @param {Object} subscription - Push subscription data
   * @returns {Promise} - Response from API
   */
  unsubscribeFromPush: async (subscription) => {
    try {
      const response = await api.post('/notifications/push/unsubscribe/', subscription);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },
};

export default notificationService;
