{"name": "jechspace", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "postinstall": "flowbite-react patch"}, "dependencies": {"@fullcalendar/daygrid": "^6.1.17", "@fullcalendar/interaction": "^6.1.17", "@fullcalendar/react": "^6.1.17", "@fullcalendar/timegrid": "^6.1.17", "@heroicons/react": "^2.2.0", "@tailwindcss/vite": "^4.1.3", "axios": "^1.9.0", "fabric": "^6.6.4", "flowbite": "^3.1.2", "flowbite-react": "^0.11.7", "framer-motion": "^12.7.4", "lucide-react": "^0.488.0", "motion": "^12.7.4", "nanoid": "^5.1.5", "prop-types": "^15.8.1", "qr.react": "^0.0.1", "qrcode.react": "^4.2.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router-dom": "^7.5.0", "tailwindcss": "^4.1.3"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.4.0", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "vite": "^6.2.0", "vite-tsconfig-paths": "^5.1.4"}}