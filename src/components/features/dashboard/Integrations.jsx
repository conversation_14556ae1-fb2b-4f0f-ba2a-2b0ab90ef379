import { useState } from 'react';
import { Search, Grid, Calendar, MessageSquare, CreditCard, Lock, Video, BarChart2, Refresh<PERSON><PERSON>, Key, Briefcase, DoorOpen, ArrowRight, Check, ExternalLink } from 'lucide-react';

const IntegrationCard = ({ title, description, icon, connected = false, popular = false }) => {
  const [isConnected, setIsConnected] = useState(connected);

  return (
    <div className="bg-white rounded-lg shadow hover:shadow-md transition-shadow p-4 border border-gray-200 relative">
      {popular && (
        <span className="absolute -top-2 -right-2 bg-blue-600 text-white text-xs px-2 py-1 rounded-full">
          Popular
        </span>
      )}
      <div className="flex items-start mb-4">
        <div className="bg-blue-100 p-2 rounded-lg mr-3">
          {icon}
        </div>
        <div>
          <h3 className="font-medium text-gray-800">{title}</h3>
          <p className="text-sm text-gray-500 mt-1">{description}</p>
        </div>
      </div>
      <button
        onClick={() => setIsConnected(!isConnected)}
        className={`w-full flex items-center justify-center py-2 px-4 rounded-md text-sm font-medium transition-colors ${
          isConnected 
            ? "bg-gray-100 text-gray-700 hover:bg-gray-200" 
            : "bg-blue-50 text-blue-700 hover:bg-blue-100"
        }`}
      >
        {isConnected ? (
          <>
            <Check size={16} className="mr-2" />
            Connected
          </>
        ) : (
          <>
            Connect
            <ArrowRight size={16} className="ml-2" />
          </>
        )}
      </button>
    </div>
  );
};

const IntegrationCategory = ({ title, description, icon, children }) => {
  return (
    <div className="mb-8">
      <div className="flex items-center mb-4">
        <div className="bg-blue-50 p-2 rounded-md mr-3">
          {icon}
        </div>
        <div>
          <h2 className="text-xl font-semibold text-gray-800">{title}</h2>
          <p className="text-sm text-gray-500">{description}</p>
        </div>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {children}
      </div>
    </div>
  );
};

const Integrations = () => {
  const [searchQuery, setSearchQuery] = useState("");
  
  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Integrations</h1>
          <p className="text-gray-600 max-w-3xl">
            Connect your workspace with your favorite tools and services to streamline your workflow and enhance productivity.
          </p>
        </div>
        
        {/* Search and filters */}
        <div className="mb-8 flex flex-col sm:flex-row gap-4">
          <div className="relative flex-grow">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search size={18} className="text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Search integrations..."
              className="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <div className="flex gap-2">
            <button className="bg-white border border-gray-300 rounded-lg px-4 py-2 text-gray-700 font-medium hover:bg-gray-50">
              All
            </button>
            <button className="bg-white border border-gray-300 rounded-lg px-4 py-2 text-gray-700 font-medium hover:bg-gray-50">
              Connected
            </button>
            <button className="bg-blue-50 text-blue-700 border border-blue-200 rounded-lg px-4 py-2 font-medium hover:bg-blue-100">
              Popular
            </button>
          </div>
        </div>

        {/* Featured integrations */}
        <div className="bg-blue-50 rounded-lg p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">Popular Integrations</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <IntegrationCard 
              title="Google Calendar" 
              description="Sync bookings with your Google Calendar" 
              icon={<Calendar size={24} className="text-blue-700" />} 
              popular={true}
            />
            <IntegrationCard 
              title="Slack" 
              description="Get notifications about new bookings" 
              icon={<MessageSquare size={24} className="text-blue-700" />} 
              popular={true}
            />
            <IntegrationCard 
              title="Stripe" 
              description="Process payments for your bookings" 
              icon={<CreditCard size={24} className="text-blue-700" />} 
              popular={true}
              connected={true}
            />
          </div>
        </div>

        {/* Integration categories */}
        <IntegrationCategory
          title="Calendar Systems"
          description="Sync bookings with your favorite calendar apps"
          icon={<Calendar size={24} className="text-blue-700" />}
        >
          <IntegrationCard 
            title="Google Calendar" 
            description="Two-way sync with Google Calendar" 
            icon={<Calendar size={20} className="text-blue-700" />} 
            popular={true}
          />
          <IntegrationCard 
            title="Microsoft Outlook" 
            description="Sync events with Outlook Calendar" 
            icon={<Calendar size={20} className="text-blue-700" />} 
          />
          <IntegrationCard 
            title="Apple Calendar" 
            description="iCalendar feed for Apple devices" 
            icon={<Calendar size={20} className="text-blue-700" />} 
          />
        </IntegrationCategory>

        <IntegrationCategory
          title="Communication Tools"
          description="Notify your team about bookings and changes"
          icon={<MessageSquare size={24} className="text-blue-700" />}
        >
          <IntegrationCard 
            title="Slack" 
            description="Real-time notifications in channels" 
            icon={<MessageSquare size={20} className="text-blue-700" />} 
            popular={true}
          />
          <IntegrationCard 
            title="Microsoft Teams" 
            description="Teams notifications and bot integration" 
            icon={<MessageSquare size={20} className="text-blue-700" />} 
          />
          <IntegrationCard 
            title="Email Service" 
            description="Custom email notifications" 
            icon={<MessageSquare size={20} className="text-blue-700" />}
            connected={true} 
          />
        </IntegrationCategory>

        <IntegrationCategory
          title="Payment Processors"
          description="Process payments for bookings and subscriptions"
          icon={<CreditCard size={24} className="text-blue-700" />}
        >
          <IntegrationCard 
            title="Stripe" 
            description="Credit card payments and subscriptions" 
            icon={<CreditCard size={20} className="text-blue-700" />}
            connected={true}
            popular={true}
          />
          <IntegrationCard 
            title="PayPal" 
            description="Accept PayPal payments" 
            icon={<CreditCard size={20} className="text-blue-700" />} 
          />
          <IntegrationCard 
            title="Square" 
            description="Point of sale integration" 
            icon={<CreditCard size={20} className="text-blue-700" />} 
          />
        </IntegrationCategory>

        <IntegrationCategory
          title="Authentication Systems"
          description="Secure access to your workspaces"
          icon={<Lock size={24} className="text-blue-700" />}
        >
          <IntegrationCard 
            title="Google SSO" 
            description="Single Sign-On with Google accounts" 
            icon={<Lock size={20} className="text-blue-700" />} 
          />
          <IntegrationCard 
            title="Microsoft Azure AD" 
            description="Enterprise identity management" 
            icon={<Lock size={20} className="text-blue-700" />} 
          />
          <IntegrationCard 
            title="SAML 2.0" 
            description="Custom SSO implementation" 
            icon={<Lock size={20} className="text-blue-700" />} 
          />
        </IntegrationCategory>

        <IntegrationCategory
          title="Video Conferencing"
          description="Integrate with virtual meeting solutions"
          icon={<Video size={24} className="text-blue-700" />}
        >
          <IntegrationCard 
            title="Zoom" 
            description="Auto-generate Zoom meetings for bookings" 
            icon={<Video size={20} className="text-blue-700" />}
            popular={true}
          />
          <IntegrationCard 
            title="Microsoft Teams" 
            description="Create Teams meetings automatically" 
            icon={<Video size={20} className="text-blue-700" />} 
          />
          <IntegrationCard 
            title="Google Meet" 
            description="Add Google Meet links to bookings" 
            icon={<Video size={20} className="text-blue-700" />} 
          />
        </IntegrationCategory>

        <IntegrationCategory
          title="CRM and Business Tools"
          description="Connect with your business management systems"
          icon={<Briefcase size={24} className="text-blue-700" />}
        >
          <IntegrationCard 
            title="Salesforce" 
            description="Sync contacts and bookings with Salesforce" 
            icon={<Briefcase size={20} className="text-blue-700" />} 
          />
          <IntegrationCard 
            title="HubSpot" 
            description="Connect customer data with HubSpot" 
            icon={<Briefcase size={20} className="text-blue-700" />} 
          />
          <IntegrationCard 
            title="QuickBooks" 
            description="Streamline accounting and invoicing" 
            icon={<Briefcase size={20} className="text-blue-700" />} 
          />
        </IntegrationCategory>

        <IntegrationCategory
          title="Access Control Systems"
          description="Manage physical access to your spaces"
          icon={<DoorOpen size={24} className="text-blue-700" />}
        >
          <IntegrationCard 
            title="Smart Locks" 
            description="Control access with smart lock systems" 
            icon={<DoorOpen size={20} className="text-blue-700" />} 
          />
          <IntegrationCard 
            title="Key Card System" 
            description="Integrate with RFID access systems" 
            icon={<DoorOpen size={20} className="text-blue-700" />} 
          />
          <IntegrationCard 
            title="Mobile Access" 
            description="Digital keys for workspace entry" 
            icon={<DoorOpen size={20} className="text-blue-700" />} 
          />
        </IntegrationCategory>

        <IntegrationCategory
          title="Analytics & Reporting"
          description="Track usage and generate insights"
          icon={<BarChart2 size={24} className="text-blue-700" />}
        >
          <IntegrationCard 
            title="Google Analytics" 
            description="Track user behavior and bookings" 
            icon={<BarChart2 size={20} className="text-blue-700" />} 
          />
          <IntegrationCard 
            title="Power BI" 
            description="Create custom reports and dashboards" 
            icon={<BarChart2 size={20} className="text-blue-700" />} 
          />
          <IntegrationCard 
            title="Custom Analytics" 
            description="Export data for custom analysis" 
            icon={<BarChart2 size={20} className="text-blue-700" />} 
          />
        </IntegrationCategory>

        <IntegrationCategory
          title="Workflow Automation"
          description="Automate tasks and processes"
          icon={<RefreshCw size={24} className="text-blue-700" />}
        >
          <IntegrationCard 
            title="Zapier" 
            description="Connect with 3,000+ apps and services" 
            icon={<RefreshCw size={20} className="text-blue-700" />}
            popular={true}
          />
          <IntegrationCard 
            title="IFTTT" 
            description="If This Then That automation" 
            icon={<RefreshCw size={20} className="text-blue-700" />} 
          />
          <IntegrationCard 
            title="Custom Webhooks" 
            description="Trigger events in external systems" 
            icon={<RefreshCw size={20} className="text-blue-700" />} 
          />
        </IntegrationCategory>

        <IntegrationCategory
          title="API Access"
          description="Developer tools and API access"
          icon={<Key size={24} className="text-blue-700" />}
        >
          <IntegrationCard 
            title="API Keys" 
            description="Generate and manage API access" 
            icon={<Key size={20} className="text-blue-700" />} 
          />
          <IntegrationCard 
            title="Webhooks" 
            description="Configure event-based notifications" 
            icon={<Key size={20} className="text-blue-700" />} 
          />
          <IntegrationCard 
            title="Developer Portal" 
            description="Access documentation and resources" 
            icon={<Key size={20} className="text-blue-700" />} 
          />
        </IntegrationCategory>

        {/* Developer resources */}
        <div className="bg-white rounded-lg border border-gray-200 p-6 mt-8">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold text-gray-800">Developer Resources</h2>
            <a href="#" className="text-blue-700 flex items-center text-sm hover:underline">
              Visit Developer Portal <ExternalLink size={14} className="ml-1" />
            </a>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="p-4 bg-blue-50 rounded-lg">
              <h3 className="font-medium text-gray-800 mb-2">API Documentation</h3>
              <p className="text-sm text-gray-600 mb-3">Learn how to integrate with our RESTful API</p>
              <a href="#" className="text-blue-700 text-sm flex items-center hover:underline">
                View Documentation <ArrowRight size={14} className="ml-1" />
              </a>
            </div>
            <div className="p-4 bg-blue-50 rounded-lg">
              <h3 className="font-medium text-gray-800 mb-2">Webhook Setup</h3>
              <p className="text-sm text-gray-600 mb-3">Configure real-time event notifications</p>
              <a href="#" className="text-blue-700 text-sm flex items-center hover:underline">
                Configure Webhooks <ArrowRight size={14} className="ml-1" />
              </a>
            </div>
            <div className="p-4 bg-blue-50 rounded-lg">
              <h3 className="font-medium text-gray-800 mb-2">Custom Integration</h3>
              <p className="text-sm text-gray-600 mb-3">Need a custom integration? Contact our team</p>
              <a href="#" className="text-blue-700 text-sm flex items-center hover:underline">
                Request Custom Integration <ArrowRight size={14} className="ml-1" />
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Integrations;