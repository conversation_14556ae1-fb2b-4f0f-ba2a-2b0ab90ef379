import { ArrowRight } from "lucide-react";
import PropTypes from "prop-types";
import PageIndicator from "./PageIndicator";
import { useNavigate } from "react-router-dom";

export default function OnboardingAdmin5({ nextStep }) {
  // Placeholder image sources
  const logoSrc = "/images/logo-blue.png";
  const onboardingImgSrc = "/images/onboarding5.png"; // Placeholder for onboarding image
  const illustrationSrc = "/api/placeholder/320/320";

  const navigate = useNavigate();

  // Function to handle the next step by calling the nextStep prop
  const handleNext = () => {
    navigate("/admin/dashboard");
  };

  return (
    <div className="flex items-center justify-center w-full min-h-screen bg-gray-50 p-4 sm:p-8 md:p-16">
      <div className="w-full max-w-6xl bg-white rounded-lg shadow-lg flex flex-col md:flex-row">
        {/* left section */}
        <div className="w-full md:w-3/5 p-6 sm:p-8 md:p-10 flex flex-col">
          {/* Logo - Image placeholder */}
          <div className="mb-6 sm:mb-8">
            <img src={logoSrc} alt="JechSpace Logo" className="h-6 sm:h-8" />
          </div>

          {/* title */}
          <div className="mb-6 sm:mb-8">
            <h1 className="text-2xl sm:text-3xl font-bold mb-2 sm:mb-3">
              You're All Set!
            </h1>

            <p className="text-gray-600 mb-6 sm:mb-8 text-lg sm:text-2xl leading-relaxed">
              Thanks for setting up your company. You're now ready to explore
              your dashboard, manage bookings, and enjoy the JechSpace
              experience.
            </p>
          </div>

          <div className="mt-auto flex items-center justify-between">
            <PageIndicator currentStep={5} totalSteps={5} />
            <button
              type="button"
              onClick={handleNext}
              className="bg-blue-500 text-white px-3 sm:px-5 py-1.5 sm:py-2 rounded-md flex items-center text-sm sm:text-base"
            >
              Dashboard
              <ArrowRight size={14} className="ml-1 sm:ml-2" />
            </button>
          </div>
        </div>
        <div className="hidden md:flex w-full md:w-2/5 bg-gray-50 items-center justify-center p-4">
          {/* Replace the src with your actual welcome image path */}
          <img
            src={onboardingImgSrc}
            alt="Welcome illustration"
            className="max-w-full max-h-full object-contain"
          />
        </div>
      </div>
    </div>
  );
}

OnboardingAdmin5.propTypes = {
  nextStep: PropTypes.func.isRequired,
};
