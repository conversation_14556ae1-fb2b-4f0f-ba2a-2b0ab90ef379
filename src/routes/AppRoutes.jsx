import { BrowserRouter, Routes, Route } from "react-router-dom";
import UserRoutes from "./UserRoutes";
import AdminRoutes from "./AdminRoutes";
import Home from "../pages/Home";
import NotFound from "../pages/NotFound";
import LoginPage from "../pages/LoginPage";
import SignupTogglePage from "../pages/SignupTogglePage";
import IndividualConfirmationPage from "../pages/IndividualConfirmationPage";
import OrganizationConfirmationPage from "../pages/OrganizationConfirmationPage";
import EmailConfirmation from "../components/dashboard/admin/EmailConfirmation";
import OnboardingPage1 from "../pages/OnboardingPage1";
import OnboardingPage2 from "../pages/OnboardingPage2";
import OnboardingPage3 from "../pages/OnboardingPage3";
import OnboardingPage4 from "../pages/OnboardingPage4";
import OnboardingPage5 from "../pages/OnboardingPage5";

import "../App.css";

function AppRoutes() {
  return (
    <BrowserRouter>
      <Routes>
        <Route path="/" element={<Home />} />
        <Route path="/login" element={<LoginPage />} />

        
        <Route
          path="/confirmation/individual"
          element={<IndividualConfirmationPage />}
        />
        <Route
          path="/confirmation/organization"
          element={<OrganizationConfirmationPage />}
        />

        <Route path="/signup" element={<SignupTogglePage />} />
        
        {/* Admin onboarding route */}
        <Route path="/admin/confirmation" element={<EmailConfirmation />} />
        <Route path="/admin/onboarding/step1" element={<OnboardingPage1 />} />
        <Route path="/admin/onboarding/step2" element={<OnboardingPage2 />} />
        <Route path="/admin/onboarding/step3" element={<OnboardingPage3 />} />
        <Route path="/admin/onboarding/step4" element={<OnboardingPage4 />} />
        <Route path="/admin/onboarding/step5" element={<OnboardingPage5 />} />

        {/* All admin routes are nested under /admin */}
        <Route path="/admin/*" element={<AdminRoutes />} />

        {/* All user routes are nested under /user */}
        <Route path="/user/*" element={<UserRoutes />} />

        {/* Catch-all route for 404 page */}
        <Route path="*" element={<NotFound />} />
      </Routes>
    </BrowserRouter>
  );
}

export default AppRoutes;
