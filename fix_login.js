import axios from "axios";
import api from "./api";

/**
 * Authentication service for handling user authentication operations
 */
const authService = {
  /**
   * Login user with email and password
   * @param {Object} credentials - User credentials
   * @param {string} credentials.email - User email
   * @param {string} credentials.password - User password
   * @returns {Promise} - Response from API
   */
  login: async (credentials) => {
    try {
      console.log("Login credentials:", credentials);

      // Try multiple approaches to handle potential API route differences
      let response;
      try {
        // First approach: Try the standard signin route
        response = await api.post("auth/signin/", credentials);
      } catch (err) {
        console.log("First signin approach failed, trying alternative routes...");
        
        try {
          // Second approach: Try login route
          response = await api.post("auth/login/", credentials);
        } catch (err2) {
          console.log("Second approach failed, trying direct URL...");
          
          // Third approach: Try with full URL
          response = await axios.post(
            "http://127.0.0.1:8000/api/v1/auth/signin/",
            credentials,
            {
              headers: {
                "Content-Type": "application/json",
              },
            }
          );
        }
      }

      console.log("Login response:", response.data);

      // Handle different response formats
      if (
        response.data.data &&
        response.data.data.auth &&
        response.data.data.auth.access_token
      ) {
        localStorage.setItem("authToken", response.data.data.auth.access_token);
      } else if (response.data.token) {
        localStorage.setItem("authToken", response.data.token);
      } else if (response.data.access_token) {
        localStorage.setItem("authToken", response.data.access_token);
      } else if (response.data.data && response.data.data.token) {
        localStorage.setItem("authToken", response.data.data.token);
      }
      
      return response.data;
    } catch (error) {
      console.error(
        "Login error details:",
        error.response ? error.response.data : error
      );
      throw error.response ? error.response.data : error;
    }
  },
