# JechSpace Frontend - Development Environment Configuration

# API Configuration
VITE_API_BASE_URL=http://localhost:8000/api/v1/
VITE_ENVIRONMENT=development

# WebSocket Configuration
VITE_WS_URL=ws://localhost:8000/ws/

# Feature Flags
VITE_ENABLE_LOGGING=true
VITE_ENABLE_DEBUG_MODE=true
VITE_ENABLE_MOCK_DATA=false

# Application Configuration
VITE_APP_NAME=JechSpace (Dev)
VITE_APP_VERSION=2.0.0-dev

# Session Configuration (in milliseconds)
VITE_SESSION_TIMEOUT=3600000
VITE_SESSION_WARNING_TIME=300000

# File Upload Configuration
VITE_MAX_FILE_UPLOAD_SIZE=10485760
VITE_ALLOWED_IMAGE_TYPES=image/jpeg,image/png,image/gif,image/webp

# Pagination
VITE_DEFAULT_PAGE_SIZE=20
VITE_MAX_PAGE_SIZE=100

# Development specific
VITE_SHOW_REDUX_DEVTOOLS=true
VITE_ENABLE_HOT_RELOAD=true
