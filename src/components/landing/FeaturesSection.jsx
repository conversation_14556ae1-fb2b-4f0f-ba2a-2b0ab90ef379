import { motion } from "framer-motion";
// import needed icons from lucide
import {
  Users,
  Calendar,
  Container,
  ChartLine,
  Clock,
  Group,
} from "lucide-react";

const FeatureCard = ({
  icon,
  iconBg,
  iconColor,
  title,
  description,
  delay,
}) => {
  return (
    <motion.div
      className="bg-white rounded-2xl p-8 shadow-md hover:shadow-xl hover:translate-y-[-10px] transition-all duration-300 relative overflow-hidden z-10"
      initial={{ opacity: 0, y: 50 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay }}
      viewport={{ once: true }}
    >
      <div
        className={`w-16 h-16 ${iconBg} rounded-xl flex items-center justify-center text-2xl ${iconColor} mb-6`}
      >
        {icon}
      </div>
      <h3 className="text-xl font-semibold mb-4">{title}</h3>
      <p className="text-gray-600 mb-6">{description}</p>
      <a
        href="#"
        className="flex items-center text-primary font-medium gap-1 group"
      >
        Learn more
        <i className="fas fa-arrow-right transition-all group-hover:translate-x-1"></i>
      </a>
    </motion.div>
  );
};

const features = [
  {
    icon: <Group size={24} />,
    iconBg: "bg-primary-50",
    iconColor: "text-primary",
    title: "Space Management",
    description:
      "Create, visualize, and manage all your workspaces effortlessly with our intuitive floor plan editor.",
  },
  {
    icon: <Users size={24} />,
    iconBg: "bg-secondary-50",
    iconColor: "text-secondary",
    title: "User Management",
    description:
      "Handle user permissions, roles, and access levels with our comprehensive user directory system.",
  },
  {
    icon: <ChartLine size={24} />,
    iconBg: "bg-success-50",
    iconColor: "text-success",
    title: "Analytics Dashboard",
    description:
      "Gain valuable insights with real-time analytics on space utilization, booking patterns, and more.",
  },
  {
    icon: <Calendar size={24} />,
    iconBg: "bg-warning-50",
    iconColor: "text-warning",
    title: "Maintenance Scheduling",
    description:
      "Keep your facilities in perfect condition with automated maintenance tracking and scheduling tools.",
  },
  {
    icon: <Clock size={24} />,
    iconBg: "bg-primary-dark-50",
    iconColor: "text-primary-dark",
    title: "Custom Reports",
    description:
      "Generate standard and custom reports with our interactive reporting tools to make data-driven decisions.",
  },
  {
    icon: <Container size={24} />,
    iconBg: "bg-primary-light-50",
    iconColor: "text-primary-light",
    title: "System Integrations",
    description:
      "Connect with your existing tools through our API and built-in integrations with popular workplace software.",
  },
];

const FeaturesSection = () => {
  return (
    <section id="features" className="py-24">
      <div className="container mx-auto px-4">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl font-bold mb-4">Powerful Features</h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Discover how our workspace management platform can transform your
            office environment with these powerful tools and capabilities.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <FeatureCard
              key={index}
              icon={feature.icon}
              iconBg={feature.iconBg}
              iconColor={feature.iconColor}
              title={feature.title}
              description={feature.description}
              delay={0.1 * (index % 3)}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default FeaturesSection;
