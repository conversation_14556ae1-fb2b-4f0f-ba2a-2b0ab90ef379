import { useState } from "react";
import { ChevronRight, ChevronDown } from "lucide-react";
import PropTypes from "prop-types";
import PageIndicator from "./PageIndicator";
import { ArrowRight } from "lucide-react";
import { useNavigate } from "react-router-dom";

export default function OnboardingAdmin3({ nextStep }) {
  const [email, setEmail] = useState("");
  const [role, setRole] = useState("Admin");
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);


  // Placeholder image sources
  const logoSrc = "/images/logo-blue.png"; // Placeholder for logo
  const onboardingImgSrc = "/images/onboarding3.png"; // Placeholder for onboarding image
  const illustrationSrc = "/api/placeholder/320/320"; // Placeholder for team illustration

  const roles = ["Admin", "Manager", "Viewer"];

  const toggleDropdown = () => {
    setIsDropdownOpen(!isDropdownOpen);
  };

  const selectRole = (selectedRole) => {
    setRole(selectedRole);
    setIsDropdownOpen(false);
  };
  const navigate = useNavigate();

  const handleNext = () => {
    navigate("/admin/onboarding/step4");
  };

  return (
    <div className="flex items-center justify-center w-full min-h-screen bg-gray-50 p-4 sm:p-8 md:p-16">
      <div className="w-full max-w-6xl bg-white rounded-lg shadow-lg flex flex-col md:flex-row">
        <div className="w-full md:w-3/5 p-6 sm:p-10 flex flex-col">
          {/* Logo - Image placeholder */}
          <div className="mb-8">
            <img src={logoSrc} alt="JechSpace Logo" className="h-8" />
          </div>

          {/* title */}
          <div className="mb-8">
            <h1 className="text-2xl sm:text-3xl font-bold mb-3">
              Manage Your Team
            </h1>

            <p className="text-gray-600 mb-8 text-sm sm:text-base">
              Add your team members to start managing spaces together. You can
              assign roles like Admin, Manager, or Viewer based on their
              responsibilities.
            </p>
          </div>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">
                Invite a team member
              </label>
              <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full px-4 py-2 bg-gray-100 rounded-md border border-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="<EMAIL>"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">
                Assign role
              </label>
              <div className="relative">
                <button
                  type="button"
                  onClick={toggleDropdown}
                  className="w-full px-4 py-2 bg-gray-100 rounded-md border border-gray-200 flex justify-between items-center focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <span className="text-gray-500">{role}</span>
                  <ChevronDown size={16} className="text-black" />
                </button>

                {isDropdownOpen && (
                  <div className="absolute w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg z-10">
                    {roles.map((r) => (
                      <div
                        key={r}
                        className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                        onClick={() => selectRole(r)}
                      >
                        {r}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>

            <button className="w-full bg-blue-500 text-white font-medium py-3 rounded-md mt-2">
              Send invitation
            </button>
          </div>

          <div className="mt-16 flex items-center justify-between">
            <PageIndicator currentStep={3} totalSteps={5} />
            <button
              type="button"
              onClick={handleNext}
              className="bg-blue-500 text-white px-5 py-2 rounded-md flex items-center"
            >
              Next <ArrowRight size={16} className="ml-1" />
            </button>
          </div>
        </div>
        <div className="hidden md:flex w-full md:w-2/5 bg-gray-50 items-center justify-center p-4">
          {/* Replace the src with your actual welcome image path */}
          <img
            src={onboardingImgSrc}
            alt="Welcome illustration"
            className="max-w-full max-h-full object-contain"
          />
        </div>
      </div>
    </div>
  );
}

OnboardingAdmin3.propTypes = {
  nextStep: PropTypes.func.isRequired,
};
