import api from './api';

/**
 * Service for handling permission and role operations
 */
const permissionService = {
  /**
   * Get all permissions
   * @param {Object} params - Query parameters
   * @returns {Promise} - Response from API
   */
  getAllPermissions: async (params = {}) => {
    try {
      const response = await api.get('/permissions/', { params });
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  /**
   * Get user permissions
   * @param {string} userId - User ID (optional, defaults to current user)
   * @returns {Promise} - Response from API
   */
  getUserPermissions: async (userId = '') => {
    try {
      const endpoint = userId ? `/permissions/user/${userId}/` : '/permissions/me/';
      const response = await api.get(endpoint);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  /**
   * Get all roles
   * @param {Object} params - Query parameters
   * @returns {Promise} - Response from API
   */
  getAllRoles: async (params = {}) => {
    try {
      const response = await api.get('/roles/', { params });
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  /**
   * Create a new role
   * @param {Object} roleData - Role data
   * @returns {Promise} - Response from API
   */
  createRole: async (roleData) => {
    try {
      const response = await api.post('/roles/', roleData);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  /**
   * Update a role
   * @param {string} roleId - Role ID
   * @param {Object} roleData - Updated role data
   * @returns {Promise} - Response from API
   */
  updateRole: async (roleId, roleData) => {
    try {
      const response = await api.put(`/roles/${roleId}/`, roleData);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  /**
   * Delete a role
   * @param {string} roleId - Role ID
   * @returns {Promise} - Response from API
   */
  deleteRole: async (roleId) => {
    try {
      const response = await api.delete(`/roles/${roleId}/`);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  /**
   * Assign role to user
   * @param {string} userId - User ID
   * @param {string} roleId - Role ID
   * @returns {Promise} - Response from API
   */
  assignRoleToUser: async (userId, roleId) => {
    try {
      const response = await api.post('/permissions/assign/', {
        user_id: userId,
        role_id: roleId
      });
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  /**
   * Remove role from user
   * @param {string} userId - User ID
   * @param {string} roleId - Role ID
   * @returns {Promise} - Response from API
   */
  removeRoleFromUser: async (userId, roleId) => {
    try {
      const response = await api.delete('/permissions/remove/', {
        data: {
          user_id: userId,
          role_id: roleId
        }
      });
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  /**
   * Check if user has permission
   * @param {string} permission - Permission name
   * @param {string} userId - User ID (optional, defaults to current user)
   * @returns {Promise} - Response from API
   */
  checkPermission: async (permission, userId = '') => {
    try {
      const endpoint = userId 
        ? `/permissions/check/${userId}/` 
        : '/permissions/check/me/';
      const response = await api.post(endpoint, { permission });
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },
};

export default permissionService;
