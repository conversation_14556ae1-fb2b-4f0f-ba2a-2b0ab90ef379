import { useState } from "react";
import { useNavigate } from "react-router-dom";

function EmployerRegistration() {
  const [formData, setFormData] = useState({
    email: "",
    password: "",
    confirmPassword: "",
  });

  const [errors, setErrors] = useState({});
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const navigate = useNavigate();

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };
  
  const validateForm = () => {
    const newErrors = {};

    // Email validation
    if (!formData.email) {
      newErrors.email = "Email is required";
    } else if (
      !/^[\w.-]+@([\w-]+\.)+[a-zA-Z]{2,}$/.test(formData.email) ||
      /(gmail|yahoo|hotmail|outlook)\.com$/.test(formData.email)
    ) {
      newErrors.email = "Please use your organization's email address";
    }

    // Password validation
    if (!formData.password) {
      newErrors.password = "Password is required";
    } else if (
      !/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[^A-Za-z\d]).{8,}$/.test(
        formData.password
      )
    ) {
      newErrors.password =
        "Password must be at least 8 characters and include uppercase, lowercase, number, and symbol";
    }

    // Confirm password validation
    if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = "Passwords do not match";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const toggleConfirmPasswordVisibility = () => {
    setShowConfirmPassword(!showConfirmPassword);
  };

  const handleSubmit = (e) => {
    e.preventDefault();

    if (validateForm()) {
      // Submit form data to API
      console.log("Form data submitted:", formData);
      // Here you would typically make an API call to register the user
    }
  };

  return (
    <div className="flex min-h-screen min-w-0">
      {/* Left Panel - Blue section with illustration */}
      <div className="hidden md:flex md:w-1/2 bg-primary p-12 flex-col justify-between relative">
        <div className="text-white text-3xl font-bold max-w-md">
          <h1 className="text-2xl text-white">
            Streamline how your organization books, manages, and utilizes
            workspaces. Enter your company email to begin setting up your smart
            workspace experience.
          </h1>
        </div>
        {/* Illustration placeholder - in a real implementation you'd import an image from assets folder */}
        <div className="absolute bottom-0 left-0 right-0 flex justify-center items-center">
          <img
            src="/images/employer-registration.png"
            alt="Illustration of people around a table"
            className="w-full h-auto"
            style={{ maxWidth: "400px", maxHeight: "400px" }}
          />
        </div>
      </div>

      {/* Right Panel - Sign up form */}
      <div className="w-full md:w-1/2 p-8 flex flex-col justify-center items-center">
        <div className="w-full max-w-md">
          {/* Logo */}

          <div className="flex justify-center mb-8">
            <img
              src="/images/logo-blue.png"
              alt="Logo"
              className="w-32 h-auto"
            />
          </div>

          <h2 className="text-2xl font-bold text-center mb-2">
            Sign up to your account.
          </h2>
          <p className="text-gray-600 text-center mb-8">
            Let's Get You Started with JechSpace
          </p>

          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label
                htmlFor="email"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Email
              </label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                placeholder="<EMAIL>"
                className={`w-full px-4 py-2 bg-gray-100 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  errors.email ? "border-red-500" : "border-gray-200"
                }`}
              />
              {errors.email && (
                <p className="text-red-500 text-xs mt-1">{errors.email}</p>
              )}
            </div>

            <div>
              <label
                htmlFor="password"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Create a new password
              </label>
              <div className="relative">
                <input
                  type={showPassword ? "text" : "password"}
                  id="password"
                  name="password"
                  value={formData.password}
                  onChange={handleInputChange}
                  placeholder="enter password"
                  className={`w-full px-4 py-2 bg-gray-100 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.password ? "border-red-500" : "border-gray-200"
                  }`}
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-500 hover:text-gray-700"
                  onClick={togglePasswordVisibility}
                  aria-label={showPassword ? "Hide password" : "Show password"}
                >
                  <span className="text-xl">
                    {showPassword ? "👁️‍🗨️" : "👁️"}
                  </span>
                </button>
              </div>
              {errors.password && (
                <p className="text-red-500 text-xs mt-1">{errors.password}</p>
              )}
            </div>

            <div>
              <label
                htmlFor="confirmPassword"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Confirm password
              </label>
              <div className="relative">
                <input
                  type={showConfirmPassword ? "text" : "password"}
                  id="confirmPassword"
                  name="confirmPassword"
                  value={formData.confirmPassword}
                  onChange={handleInputChange}
                  placeholder="enter password"
                  className={`w-full px-4 py-2 bg-gray-100 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.confirmPassword ? "border-red-500" : "border-gray-200"
                  }`}
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-500 hover:text-gray-700"
                  onClick={toggleConfirmPasswordVisibility}
                  aria-label={showConfirmPassword ? "Hide password" : "Show password"}
                >
                  <span className="text-xl">
                    {showConfirmPassword ? "👁️‍🗨️" : "👁️"}
                  </span>
                </button>
              </div>
              {errors.confirmPassword && (
                <p className="text-red-500 text-xs mt-1">
                  {errors.confirmPassword}
                </p>
              )}
            </div>

            <button
              type="button"
              className="w-full bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2" 
              onClick={() => navigate("/OnboardingAdmin1")}
            >
              Get Started
            </button>
          </form>
        </div>
      </div>
    </div>
  );
}

export default EmployerRegistration