import React, { useEffect, useState } from "react";
import { useApp } from "../context/AppContext";
import { useBooking } from "../context/BookingContext";
import { useSpace } from "../context/SpaceContext";
import { analyticsService } from "../services";
import {
  Calendar,
  Users,
  MapPin,
  TrendingUp,
  Clock,
  DollarSign,
  Activity,
  AlertCircle,
} from "lucide-react";
import { InlineLoader } from "../components/common/LoadingSpinner";

const EnhancedDashboardPage = () => {
  const { state: appState } = useApp();
  const { state: bookingState, actions: bookingActions } = useBooking();
  const { state: spaceState, actions: spaceActions } = useSpace();

  const [analytics, setAnalytics] = useState({
    totalBookings: 0,
    totalSpaces: 0,
    totalUsers: 0,
    revenue: 0,
    utilizationRate: 0,
    popularSpaces: [],
    recentBookings: [],
    peakHours: [],
  });
  const [analyticsLoading, setAnalyticsLoading] = useState(true);
  const [dateRange, setDateRange] = useState({
    start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
      .toISOString()
      .split("T")[0],
    end: new Date().toISOString().split("T")[0],
  });

  useEffect(() => {
    loadDashboardData();
  }, [dateRange]);

  const loadDashboardData = async () => {
    try {
      setAnalyticsLoading(true);

      // Load analytics data
      const analyticsResponse = await analyticsService.getDashboardAnalytics({
        start_date: dateRange.start,
        end_date: dateRange.end,
      });

      setAnalytics(analyticsResponse.data);

      // Load bookings and spaces
      await Promise.all([
        bookingActions.loadUserBookings({ limit: 10 }),
        spaceActions.loadSpaces({ limit: 20 }),
      ]);
    } catch (error) {
      console.error("Failed to load dashboard data:", error);
    } finally {
      setAnalyticsLoading(false);
    }
  };

  const StatCard = ({ title, value, icon: Icon, trend, color = "blue" }) => {
    const colorClasses = {
      blue: "bg-blue-50 text-blue-600 border-blue-200",
      green: "bg-green-50 text-green-600 border-green-200",
      yellow: "bg-yellow-50 text-yellow-600 border-yellow-200",
      red: "bg-red-50 text-red-600 border-red-200",
    };

    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600">{title}</p>
            <p className="text-2xl font-bold text-gray-900 mt-1">
              {analyticsLoading ? <InlineLoader size="sm" text="" /> : value}
            </p>
            {trend && (
              <p
                className={`text-sm mt-1 ${
                  trend > 0 ? "text-green-600" : "text-red-600"
                }`}
              >
                {trend > 0 ? "+" : ""}
                {trend}% from last period
              </p>
            )}
          </div>
          <div className={`p-3 rounded-lg border ${colorClasses[color]}`}>
            <Icon className="h-6 w-6" />
          </div>
        </div>
      </div>
    );
  };

  const QuickActions = () => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">
        Quick Actions
      </h3>
      <div className="grid grid-cols-2 gap-3">
        <button
          onClick={() => (window.location.href = "/user/findSpace")}
          className="flex items-center justify-center p-3 bg-blue-50 text-blue-700 rounded-lg hover:bg-blue-100 transition-colors"
        >
          <Calendar className="h-5 w-5 mr-2" />
          Book Space
        </button>
        <button
          onClick={() => (window.location.href = "/user/findSpace")}
          className="flex items-center justify-center p-3 bg-green-50 text-green-700 rounded-lg hover:bg-green-100 transition-colors"
        >
          <MapPin className="h-5 w-5 mr-2" />
          Find Space
        </button>
        <button
          onClick={() =>
            (window.location.href = "/admin/dashboard/user-management")
          }
          className="flex items-center justify-center p-3 bg-purple-50 text-purple-700 rounded-lg hover:bg-purple-100 transition-colors"
        >
          <Users className="h-5 w-5 mr-2" />
          Invite Team
        </button>
        <button
          onClick={() => (window.location.href = "/user/analytics")}
          className="flex items-center justify-center p-3 bg-orange-50 text-orange-700 rounded-lg hover:bg-orange-100 transition-colors"
        >
          <Activity className="h-5 w-5 mr-2" />
          View Reports
        </button>
      </div>
    </div>
  );

  const RecentBookings = () => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">Recent Bookings</h3>
        <button className="text-sm text-blue-600 hover:text-blue-800">
          View All
        </button>
      </div>

      {bookingState.loading.bookings ? (
        <InlineLoader />
      ) : bookingState.userBookings.length > 0 ? (
        <div className="space-y-3">
          {bookingState.userBookings.slice(0, 5).map((booking) => (
            <div
              key={booking.id}
              className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
            >
              <div>
                <p className="font-medium text-gray-900">
                  {booking.space_name}
                </p>
                <p className="text-sm text-gray-600">
                  {new Date(booking.start_time).toLocaleDateString()} at{" "}
                  {new Date(booking.start_time).toLocaleTimeString([], {
                    hour: "2-digit",
                    minute: "2-digit",
                  })}
                </p>
              </div>
              <span
                className={`px-2 py-1 text-xs font-medium rounded-full ${
                  booking.status === "confirmed"
                    ? "bg-green-100 text-green-800"
                    : booking.status === "pending"
                    ? "bg-yellow-100 text-yellow-800"
                    : "bg-red-100 text-red-800"
                }`}
              >
                {booking.status}
              </span>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-8">
          <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-3" />
          <p className="text-gray-600">No recent bookings</p>
          <button className="mt-2 text-blue-600 hover:text-blue-800 text-sm">
            Make your first booking
          </button>
        </div>
      )}
    </div>
  );

  const PopularSpaces = () => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">Popular Spaces</h3>
        <button className="text-sm text-blue-600 hover:text-blue-800">
          View All
        </button>
      </div>

      {analyticsLoading ? (
        <InlineLoader />
      ) : analytics.popularSpaces.length > 0 ? (
        <div className="space-y-3">
          {analytics.popularSpaces.slice(0, 5).map((space, index) => (
            <div key={space.id} className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium mr-3">
                  {index + 1}
                </div>
                <div>
                  <p className="font-medium text-gray-900">{space.name}</p>
                  <p className="text-sm text-gray-600">{space.type}</p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-sm font-medium text-gray-900">
                  {space.booking_count} bookings
                </p>
                <p className="text-xs text-gray-600">
                  {space.utilization_rate}% utilization
                </p>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-8">
          <MapPin className="h-12 w-12 text-gray-400 mx-auto mb-3" />
          <p className="text-gray-600">No space data available</p>
        </div>
      )}
    </div>
  );

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900">
          Welcome back, {appState.user?.first_name || "User"}!
        </h1>
        <p className="text-gray-600 mt-1">
          Here's what's happening with your workspace today.
        </p>
      </div>

      {/* Date Range Selector */}
      <div className="mb-6 flex items-center space-x-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            From
          </label>
          <input
            type="date"
            value={dateRange.start}
            onChange={(e) =>
              setDateRange((prev) => ({ ...prev, start: e.target.value }))
            }
            className="border border-gray-300 rounded-md px-3 py-2 text-sm"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            To
          </label>
          <input
            type="date"
            value={dateRange.end}
            onChange={(e) =>
              setDateRange((prev) => ({ ...prev, end: e.target.value }))
            }
            className="border border-gray-300 rounded-md px-3 py-2 text-sm"
          />
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <StatCard
          title="Total Bookings"
          value={analytics.totalBookings}
          icon={Calendar}
          trend={12}
          color="blue"
        />
        <StatCard
          title="Available Spaces"
          value={analytics.totalSpaces}
          icon={MapPin}
          trend={5}
          color="green"
        />
        <StatCard
          title="Utilization Rate"
          value={`${analytics.utilizationRate}%`}
          icon={TrendingUp}
          trend={-3}
          color="yellow"
        />
        <StatCard
          title="Revenue"
          value={`$${analytics.revenue}`}
          icon={DollarSign}
          trend={18}
          color="green"
        />
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column */}
        <div className="lg:col-span-2 space-y-6">
          <RecentBookings />
          <PopularSpaces />
        </div>

        {/* Right Column */}
        <div className="space-y-6">
          <QuickActions />

          {/* Alerts */}
          {appState.error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center">
                <AlertCircle className="h-5 w-5 text-red-400 mr-2" />
                <p className="text-sm text-red-800">{appState.error}</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default EnhancedDashboardPage;
