import { Bell, Search, Menu, User, Settings, LogOut } from "lucide-react";
import PropTypes from "prop-types";
import { useState } from "react";
import { useEffect } from "react";
import AdminProfileSettings from "./AdminProfileSettings";

// close the sidebar when the user clicks outside of it

function AdminHeader({ sidebarOpen, setSidebarOpen }) {
  // State to control dropdown visibility
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  const [isProfileModalOpen, setIsProfileModalOpen] = useState(false);

  // <PERSON>le click outside to close the dropdown
  useEffect(() => {
    function handleClickOutside(event) {
      if (isDropdownOpen && !event.target.closest(".user-dropdown")) {
        setIsDropdownOpen(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [isDropdownOpen]);

  // Open the profile settings modal
  const openProfileModal = () => {
    setIsDropdownOpen(false); // Close the dropdown
    setIsProfileModalOpen(true); // Open the modal
  };

  return (
    <header className="bg-white shadow-sm z-10">
      <div className="px-4 sm:px-6 lg:px-8 py-4 flex items-center justify-between">
        {/* Menu Button for Small Screens */}
        <button
          className="md:hidden text-gray-500 hover:text-gray-700"
          onClick={() => setSidebarOpen(!sidebarOpen)}
        >
          <Menu size={24} />
        </button>

        {/* Logo Section */}
        <div className="flex items-center">
          {/* <img
            src="/images/logo-blue.png"
            alt="Logo"
            className="h-8 w-auto mr-4"
          /> */}
        </div>

        {/* Right section */}
        <div className="flex items-center space-x-4">
          {/* Search */}
          <div className="hidden md:flex items-center bg-gray-100 rounded-md px-3 py-2">
            <Search size={18} className="text-gray-500" />
            <input
              type="text"
              placeholder="Search..."
              className="bg-transparent border-none shadow-none focus:ring-0 ml-2 text-sm"
            />
          </div>

          {/* Notifications */}
          <button className="relative text-gray-500 hover:text-gray-700">
            <Bell size={20} />
            <span className="absolute top-0 right-0 w-2 h-2 bg-red-500 rounded-full"></span>
          </button>

          {/* User */}
          <div className="relative user-dropdown">
            <button
              className="flex items-center space-x-2 focus:outline-none"
              onClick={() => setIsDropdownOpen(!isDropdownOpen)}
            >
              <div className="w-8 h-8 rounded-full flex items-center justify-center text-white">
                <img
                  src="/images/user-avatar.png"
                  alt="Profile"
                  className="w-full h-full rounded-full"
                />
              </div>
            </button>
            {isDropdownOpen && (
              <div className="absolute right-0 mt-2 w-48 bg-white border border-gray-200 rounded-md shadow-lg z-20">
                <ul className="py-1">
                  <li>
                    <button
                      onClick={openProfileModal}
                      className="flex items-center w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      <User size={16} className="mr-2" />
                      Profile
                    </button>
                  </li>
                  <li>
                    <a
                      href="/"
                      className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      <LogOut size={16} className="mr-2" />
                      Logout
                    </a>
                  </li>
                </ul>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Profile Settings Modal */}
      <AdminProfileSettings
        isOpen={isProfileModalOpen}
        onClose={() => setIsProfileModalOpen(false)}
      />
    </header>
  );
}

export default AdminHeader;
AdminHeader.propTypes = {
  sidebarOpen: PropTypes.bool.isRequired,
  setSidebarOpen: PropTypes.func.isRequired,
};
