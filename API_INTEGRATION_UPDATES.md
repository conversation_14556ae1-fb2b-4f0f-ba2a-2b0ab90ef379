# JechSpace Frontend API Integration Updates

This document outlines the changes made to connect the JechSpace frontend to the deployed backend API service at `api-backend.jechspace.com`.

## Overview of Changes

The frontend has been updated to use the API services created in the `src/services` directory to communicate with the backend. The following components have been updated:

1. **SpaceList Component**:
   - Now fetches spaces from the API using `spaceService.getAllSpaces()`
   - Creates new spaces using `spaceService.createSpace()`
   - Updates spaces using `spaceService.updateSpace()`
   - Deletes spaces using `spaceService.deleteSpace()`
   - Fetches bookings using `bookingService.getAllBookings()`
   - Creates bookings using `bookingService.createBooking()`
   - Cancels bookings using `bookingService.cancelBooking()`

2. **Authentication Components**:
   - `LoginForm.jsx` now uses `authService.login()`
   - `SignupToggle.jsx` now uses `authService.registerIndividual()` and `authService.registerOrganization()`
   - `OrganizationSignupForm.jsx` now uses `authService.registerOrganization()`

## API Service Structure

The API services are organized in the `src/services` directory:

- `api.js`: Base axios configuration with interceptors for authentication
- `authService.js`: Authentication-related API calls (login, register, etc.)
- `bookingService.js`: Booking-related API calls
- `organizationService.js`: Organization-related API calls
- `spaceService.js`: Workspace-related API calls
- `userService.js`: User-related API calls
- `index.js`: Exports all services

## Error Handling

All API calls now include proper error handling:

```javascript
try {
  // API call
  const response = await someService.someMethod(data);
  // Handle success
} catch (error) {
  console.error('Error:', error);
  // Display error message to user
  displayToast('error', error.message || 'An error occurred');
}
```

## Fallback Mechanism

For critical components, fallback data is provided in case the API call fails:

```javascript
try {
  const response = await spaceService.getAllSpaces();
  setSpaces(response.data);
} catch (err) {
  console.error('Error fetching spaces:', err);
  // Fallback to sample data
  setSpaces([/* sample data */]);
}
```

## Environment Configuration

The API base URL and endpoints are configured in the `.env` file:

```
VITE_API_BASE_URL=https://api-backend.jechspace.com/api/v1
VITE_AUTH_SIGNUP_URL=https://api-backend.jechspace.com/api/v1/auth/signup/
VITE_AUTH_SIGNIN_URL=https://api-backend.jechspace.com/api/v1/auth/signin/
VITE_AUTH_SIGNOUT_URL=https://api-backend.jechspace.com/api/v1/auth/signout/
```

## Data Transformation

API responses are transformed to match the expected format of the frontend components:

```javascript
const formattedSpaces = response.data.map(space => ({
  id: space.id,
  name: space.name,
  capacity: space.capacity,
  availability: space.is_active ? 'Available' : 'Maintenance',
  rate: space.hourly_rate || 0,
  features: space.amenities || [],
  description: space.description || '',
  // ...other properties
}));
```

## Next Steps

1. **Testing**: Test the integration with the deployed backend service
2. **Additional Components**: Update other components to use the API services
3. **Error Handling**: Implement more robust error handling and user feedback
4. **Loading States**: Add loading indicators during API calls
5. **Pagination**: Implement pagination for large data sets

## Conclusion

The frontend is now properly connected to the backend API service. All essential functionalities (authentication, space management, booking management) now use the API services to communicate with the backend.
