# JechSpace Frontend API Integration

This document outlines how the JechSpace frontend connects to the backend API service.

## API Configuration

The frontend is configured to connect to the deployed backend service at `api-backend.jechspace.com`. The base URL and other API-related configuration are stored in environment variables in the `.env` file.

## Environment Variables

The following environment variables are used for API configuration:

```
VITE_API_BASE_URL=https://api-backend.jechspace.com/api/v1
VITE_AUTH_SIGNUP_URL=https://api-backend.jechspace.com/api/v1/auth/signup/
VITE_AUTH_SIGNIN_URL=https://api-backend.jechspace.com/api/v1/auth/signin/
VITE_AUTH_SIGNOUT_URL=https://api-backend.jechspace.com/api/v1/auth/signout/
```

Legacy environment variables are also included for backward compatibility:

```
INDREG_API=https://api-backend.jechspace.com/api/v1/auth/signup/
USER_REG_API=https://api-backend.jechspace.com/api/v1/auth/signup/
ORGREG_API=https://api-backend.jechspace.com/api/v1/auth/signup/
```

## API Service Structure

The API services are organized in the `src/services` directory:

- `api.js`: Base axios configuration with interceptors for authentication
- `authService.js`: Authentication-related API calls (login, register, etc.)
- `bookingService.js`: Booking-related API calls
- `organizationService.js`: Organization-related API calls
- `spaceService.js`: Workspace-related API calls
- `userService.js`: User-related API calls
- `index.js`: Exports all services

## Authentication

Authentication is handled using JWT tokens. When a user logs in or registers, the token is stored in localStorage and automatically included in subsequent API requests via the axios interceptor.

## API Endpoints

The backend API follows RESTful conventions with the following main endpoints:

- `/api/v1/auth/`: Authentication endpoints (signup, signin, signout)
- `/api/v1/organizations/`: Organization management
- `/api/v1/spaces/`: Workspace management
- `/api/v1/bookings/`: Booking management
- `/api/v1/users/`: User management
- `/api/v1/permissions/`: Permission management
- `/api/v1/amenities/`: Amenities management

## Error Handling

API errors are handled consistently across all services. Each service method catches errors and formats them appropriately before passing them back to the components.

## Usage Example

```javascript
import { authService } from '../services';

// Login
try {
  const credentials = { email: '<EMAIL>', password: 'password' };
  const response = await authService.login(credentials);
  // Handle successful login
} catch (error) {
  // Handle error
}

// Get user profile
try {
  const userProfile = await userService.getCurrentUser();
  // Use user profile data
} catch (error) {
  // Handle error
}
```

## Development

When developing locally, you can override the API base URL by setting the `VITE_API_BASE_URL` environment variable in a `.env.local` file, which is not committed to the repository.
