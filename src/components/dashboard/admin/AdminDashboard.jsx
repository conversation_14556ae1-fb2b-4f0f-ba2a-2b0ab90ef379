import {
  Calendar,
  Users,
  Container,
  Clock,
  TrendingUp,
  Alert<PERSON>riangle,
} from "lucide-react";

function AdminDashboard() {
  // Sample data - replace with real data from your API
  const stats = [
    {
      title: "Total Users",
      value: "1,254",
      icon: <Users />,
      color: "bg-blue-500",
    },
    {
      title: "Active Bookings",
      value: "45",
      icon: <Calendar />,
      color: "bg-green-500",
    },
    {
      title: "Total Spaces",
      value: "540",
      icon: <Container />,
      color: "bg-purple-500",
    },
    {
      title: "Avg. Usage Time",
      value: "2.3 hrs",
      icon: <Clock />,
      color: "bg-orange-500",
    },
  ];

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-800">Dashboard Overview</h1>
        <div>
          <select className="bg-white border border-gray-300 rounded-md shadow-sm px-4 py-2">
            <option>Last 7 days</option>
            <option>Last 30 days</option>
            <option>Last 90 days</option>
          </select>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <div key={index} className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">
                  {stat.title}
                </p>
                <p className="text-2xl font-bold">{stat.value}</p>
              </div>
              <div className={`p-3 rounded-full ${stat.color} text-white`}>
                {stat.icon}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-medium">Booking Trends</h2>
            <TrendingUp className="text-green-500" />
          </div>
          <div className="h-64 w-full bg-gray-100 rounded flex items-center justify-center">
            {/* Replace with actual chart component */}
            <div className="w-full h-full relative">
            <p className="text-gray-500">Booking Trends Chart</p>
            <iframe 
        title="ALX Booking Analysis" 
        className="absolute top-0 left-0 w-full h-full border-0"
        src="https://app.powerbi.com/view?r=eyJrIjoiOWY2YTQ3MGItYTEwNi00ODU3LThjMmYtZmRmNTAxZjQ2ZDhjIiwidCI6ImQwNmVjYjYzLTkyY2YtNDNkYy05YmNjLTE0ZWE2MTBmZmZjMiJ9" 
        allowFullScreen={true}
      />
      </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-medium">Space Utilization</h2>
            <TrendingUp className="text-blue-500" />
          </div>
          <div className="h-64 w-full bg-gray-100 rounded flex items-center justify-center">
            {/* Replace with actual chart component */}
            <div className="w-full h-full relative">
            <iframe 
        title="ALX Booking Analysis" 
        className="absolute top-0 left-0 w-full h-full border-0"
        src="https://app.powerbi.com/view?r=eyJrIjoiOWY2YTQ3MGItYTEwNi00ODU3LThjMmYtZmRmNTAxZjQ2ZDhjIiwidCI6ImQwNmVjYjYzLTkyY2YtNDNkYy05YmNjLTE0ZWE2MTBmZmZjMiJ9" 
        allowFullScreen={true}
      />
      </div>
          </div>
        </div>
      </div>

      {/* Recent Activities */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h2 className="text-lg font-medium mb-4">Recent Activities</h2>

        <div className="space-y-4">
          {[1, 2, 3, 4].map((item) => (
            <div
              key={item}
              className="flex items-center p-3 hover:bg-gray-50 rounded-md"
            >
              <div className="bg-blue-100 p-2 rounded-md mr-3">
                <Users className="text-blue-500" size={20} />
              </div>
              <div className="flex-1">
                <p className="font-medium">New user registered</p>
                <p className="text-sm text-gray-500">
                  John Doe created an account
                </p>
              </div>
              <p className="text-sm text-gray-500">2 hours ago</p>
            </div>
          ))}
        </div>
      </div>

      {/* Alerts */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-medium">Alerts</h2>
          <AlertTriangle className="text-yellow-500" />
        </div>

        <div className="space-y-3">
          <div className="bg-red-50 border-l-4 border-red-500 p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <AlertTriangle className="text-red-500" />
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-700">
                  Payment system is down. Scheduled maintenance in progress.
                </p>
              </div>
            </div>
          </div>

          <div className="bg-yellow-50 border-l-4 border-yellow-500 p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <AlertTriangle className="text-yellow-500" />
              </div>
              <div className="ml-3">
                <p className="text-sm text-yellow-700">
                  3 users have pending verification requests.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default AdminDashboard;
