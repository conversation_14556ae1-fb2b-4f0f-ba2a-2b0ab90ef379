import axios from "axios";

// Create axios instance with base configuration
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL, //  || "http://127.0.0.1:8000/api/v1/"  Make sure to include the API version
  headers: {
    "Content-Type": "application/json",
  },
});

// Enable logging of requests and responses for debugging
api.interceptors.request.use((request) => {
  // Add auth token to request headers if available
  const token = localStorage.getItem("authToken");
  if (token) {
    request.headers["Authorization"] = `Bearer ${token}`;
  }

  console.log("API Request:", {
    url: request.url,
    method: request.method,
    data: request.data,
    headers: request.headers,
  });
  return request;
});

// No duplicate interceptor needed as we already added the token in the logging interceptor

// Response interceptor for handling common errors
api.interceptors.response.use(
  (response) => {
    console.log("API Response:", {
      url: response.config.url,
      status: response.status,
      data: response.data,
    });
    return response;
  },
  (error) => {
    console.error("API Error:", {
      url: error.config?.url,
      status: error.response?.status,
      data: error.response?.data,
      message: error.message,
    });

    // Handle token expiration
    if (error.response && error.response.status === 401) {
      // Clear local storage and redirect to login if token is invalid/expired
      localStorage.removeItem("authToken");
      // Optional: Redirect to login page
      // window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

export default api;
