import axios from "axios";
import {
  getApiConfig,
  getFeatures,
  getEnvironmentInfo,
} from "../config/environment";

// Get API configuration based on environment
const apiConfig = getApiConfig();
const features = getFeatures();

// Create axios instance with base configuration
const api = axios.create({
  baseURL: apiConfig.baseURL,
  headers: {
    "Content-Type": "application/json",
  },
  timeout: apiConfig.timeout,
});

// Log the current API configuration
if (features.enableLogging) {
  console.log("🚀 JechSpace API Configuration:", getEnvironmentInfo());
}

// Enable logging of requests and responses for debugging
api.interceptors.request.use((request) => {
  // Add auth token to request headers if available
  const token = localStorage.getItem("authToken");
  if (token) {
    request.headers["Authorization"] = `Bearer ${token}`;
  }

  // Only log if logging is enabled
  if (features.enableLogging) {
    console.log("📤 API Request:", {
      url: `${request.baseURL}${request.url}`,
      method: request.method.toUpperCase(),
      data: request.data,
      headers: request.headers,
    });
  }
  return request;
});

// No duplicate interceptor needed as we already added the token in the logging interceptor

// Response interceptor for handling common errors
api.interceptors.response.use(
  (response) => {
    // Only log if logging is enabled
    if (features.enableLogging) {
      console.log("📥 API Response:", {
        url: response.config.url,
        status: response.status,
        statusText: response.statusText,
        data: response.data,
      });
    }
    return response;
  },
  (error) => {
    // Enhanced error logging
    const errorInfo = {
      url: error.config?.url,
      method: error.config?.method?.toUpperCase(),
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      message: error.message,
      baseURL: error.config?.baseURL,
    };

    console.error("❌ API Error:", errorInfo);

    // Handle specific error cases
    if (error.response) {
      switch (error.response.status) {
        case 401:
          // Unauthorized - clear token and redirect to login
          localStorage.removeItem("authToken");
          if (window.location.pathname !== "/login") {
            console.warn(
              "🔒 Token expired or invalid, redirecting to login..."
            );
            window.location.href = "/login";
          }
          break;
        case 403:
          // Forbidden - user doesn't have permission
          console.warn("🚫 Access forbidden - insufficient permissions");
          break;
        case 404:
          // Not found
          console.warn("🔍 Resource not found");
          break;
        case 500:
          // Server error
          console.error("🔥 Server error - please try again later");
          break;
        case 503:
          // Service unavailable
          console.error("⚠️ Service temporarily unavailable");
          break;
        default:
          console.error(
            `🔴 HTTP ${error.response.status}: ${error.response.statusText}`
          );
      }
    } else if (error.request) {
      // Network error
      console.error(
        "🌐 Network error - check your connection and API server status"
      );
    }

    return Promise.reject(error);
  }
);

export default api;
