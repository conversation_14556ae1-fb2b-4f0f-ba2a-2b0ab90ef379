import api from "./api";
import {
  getApiEndpointUrl,
  formatApiError,
  retryApiCall,
} from "../utils/apiUtils";

/**
 * Service for handling amenity operations with environment-aware endpoints
 */
const amenityService = {
  /**
   * Get all amenities
   * @param {Object} params - Query parameters
   * @returns {Promise} - Response from API
   */
  getAllAmenities: async (params = {}) => {
    try {
      const response = await api.get(getApiEndpointUrl("amenities", "list"), {
        params,
      });
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  /**
   * Get amenity by ID
   * @param {string} id - Amenity ID
   * @returns {Promise} - Response from API
   */
  getAmenityById: async (id) => {
    try {
      const response = await api.get(`/amenities/${id}/`);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  /**
   * Create a new amenity
   * @param {Object} amenityData - Amenity data
   * @returns {Promise} - Response from API
   */
  createAmenity: async (amenityData) => {
    try {
      const response = await api.post("/amenities/", amenityData);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  /**
   * Update an amenity
   * @param {string} id - Amenity ID
   * @param {Object} amenityData - Updated amenity data
   * @returns {Promise} - Response from API
   */
  updateAmenity: async (id, amenityData) => {
    try {
      const response = await api.put(`/amenities/${id}/`, amenityData);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  /**
   * Delete an amenity
   * @param {string} id - Amenity ID
   * @returns {Promise} - Response from API
   */
  deleteAmenity: async (id) => {
    try {
      const response = await api.delete(`/amenities/${id}/`);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  /**
   * Get amenities by category
   * @param {string} category - Amenity category
   * @returns {Promise} - Response from API
   */
  getAmenitiesByCategory: async (category) => {
    try {
      const response = await api.get("/amenities/", {
        params: { category },
      });
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  /**
   * Get popular amenities
   * @returns {Promise} - Response from API
   */
  getPopularAmenities: async () => {
    try {
      const response = await api.get("/amenities/popular/");
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },
};

export default amenityService;
