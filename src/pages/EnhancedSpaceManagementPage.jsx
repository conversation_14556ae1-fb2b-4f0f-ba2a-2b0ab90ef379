import React, { useState, useEffect } from 'react';
import { useSpace } from '../context/SpaceContext';
import { useToast } from '../components/common/NotificationContainer';
import { 
  Plus, 
  Search, 
  Filter, 
  Grid, 
  List, 
  Edit, 
  Trash2, 
  Eye,
  MapPin,
  Users,
  DollarSign,
  Settings,
  Image as ImageIcon,
  X
} from 'lucide-react';
import { <PERSON>lineLoader, ButtonLoader, CardLoader } from '../components/common/LoadingSpinner';

const EnhancedSpaceManagementPage = () => {
  const { state: spaceState, actions: spaceActions } = useSpace();
  const toast = useToast();
  
  const [showSpaceModal, setShowSpaceModal] = useState(false);
  const [editingSpace, setEditingSpace] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [showFilters, setShowFilters] = useState(false);

  useEffect(() => {
    spaceActions.loadSpaces();
    spaceActions.loadAmenities();
  }, []);

  const filteredSpaces = spaceState.spaces.filter(space => {
    const matchesSearch = space.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         space.description.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesType = spaceState.filters.type === 'all' || space.type === spaceState.filters.type;
    const matchesCapacity = spaceState.filters.capacity === 'all' || 
                           space.capacity >= parseInt(spaceState.filters.capacity);
    const matchesAvailability = spaceState.filters.availability === 'all' || 
                               (spaceState.filters.availability === 'Available' ? space.is_active : !space.is_active);
    
    return matchesSearch && matchesType && matchesCapacity && matchesAvailability;
  });

  const handleCreateSpace = () => {
    setEditingSpace(null);
    spaceActions.resetSpaceForm();
    setShowSpaceModal(true);
  };

  const handleEditSpace = (space) => {
    setEditingSpace(space);
    spaceActions.updateSpaceForm(space);
    setShowSpaceModal(true);
  };

  const handleDeleteSpace = async (spaceId) => {
    if (window.confirm('Are you sure you want to delete this space?')) {
      try {
        await spaceActions.deleteSpace(spaceId);
        toast.success('Space Deleted', 'The space has been deleted successfully.');
      } catch (error) {
        toast.error('Delete Failed', error.message || 'Failed to delete space');
      }
    }
  };

  const SpaceModal = () => {
    const handleSubmit = async (e) => {
      e.preventDefault();
      
      try {
        if (editingSpace) {
          await spaceActions.updateSpace(editingSpace.id, spaceState.spaceForm);
          toast.success('Space Updated', 'The space has been updated successfully.');
        } else {
          await spaceActions.createSpace(spaceState.spaceForm);
          toast.success('Space Created', 'The space has been created successfully.');
        }
        setShowSpaceModal(false);
      } catch (error) {
        toast.error('Operation Failed', error.message || 'Failed to save space');
      }
    };

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
          <div className="flex items-center justify-between p-6 border-b">
            <h2 className="text-xl font-semibold text-gray-900">
              {editingSpace ? 'Edit Space' : 'Create New Space'}
            </h2>
            <button
              onClick={() => setShowSpaceModal(false)}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="h-6 w-6" />
            </button>
          </div>
          
          <form onSubmit={handleSubmit} className="p-6 space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Space Name *
                </label>
                <input
                  type="text"
                  value={spaceState.spaceForm.name}
                  onChange={(e) => spaceActions.updateSpaceForm({ name: e.target.value })}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Space Type *
                </label>
                <select
                  value={spaceState.spaceForm.type}
                  onChange={(e) => spaceActions.updateSpaceForm({ type: e.target.value })}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                >
                  <option value="meeting_room">Meeting Room</option>
                  <option value="conference_room">Conference Room</option>
                  <option value="hot_desk">Hot Desk</option>
                  <option value="private_office">Private Office</option>
                  <option value="phone_booth">Phone Booth</option>
                  <option value="event_space">Event Space</option>
                </select>
              </div>
            </div>

            {/* Location and Capacity */}
            <div className="grid grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Floor
                </label>
                <input
                  type="text"
                  value={spaceState.spaceForm.floor}
                  onChange={(e) => spaceActions.updateSpaceForm({ floor: e.target.value })}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Room Number
                </label>
                <input
                  type="text"
                  value={spaceState.spaceForm.room}
                  onChange={(e) => spaceActions.updateSpaceForm({ room: e.target.value })}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Capacity *
                </label>
                <input
                  type="number"
                  min="1"
                  value={spaceState.spaceForm.capacity}
                  onChange={(e) => spaceActions.updateSpaceForm({ capacity: parseInt(e.target.value) })}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>
            </div>

            {/* Description */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Description
              </label>
              <textarea
                value={spaceState.spaceForm.description}
                onChange={(e) => spaceActions.updateSpaceForm({ description: e.target.value })}
                rows={3}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Describe the space and its features..."
              />
            </div>

            {/* Pricing and Status */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Hourly Rate ($)
                </label>
                <input
                  type="number"
                  min="0"
                  step="0.01"
                  value={spaceState.spaceForm.hourly_rate}
                  onChange={(e) => spaceActions.updateSpaceForm({ hourly_rate: parseFloat(e.target.value) })}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Status
                </label>
                <select
                  value={spaceState.spaceForm.is_active}
                  onChange={(e) => spaceActions.updateSpaceForm({ is_active: e.target.value === 'true' })}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="true">Active</option>
                  <option value="false">Inactive</option>
                </select>
              </div>
            </div>

            {/* Amenities */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Amenities
              </label>
              <div className="grid grid-cols-3 gap-2 max-h-32 overflow-y-auto border border-gray-200 rounded-md p-3">
                {spaceState.amenities.map(amenity => (
                  <label key={amenity.id} className="flex items-center">
                    <input
                      type="checkbox"
                      checked={spaceState.spaceForm.amenities.includes(amenity.id)}
                      onChange={(e) => {
                        const amenities = e.target.checked
                          ? [...spaceState.spaceForm.amenities, amenity.id]
                          : spaceState.spaceForm.amenities.filter(id => id !== amenity.id);
                        spaceActions.updateSpaceForm({ amenities });
                      }}
                      className="mr-2"
                    />
                    <span className="text-sm">{amenity.name}</span>
                  </label>
                ))}
              </div>
            </div>

            {/* Actions */}
            <div className="flex justify-end space-x-3 pt-4 border-t">
              <button
                type="button"
                onClick={() => setShowSpaceModal(false)}
                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
              >
                Cancel
              </button>
              <ButtonLoader
                loading={spaceState.loading.creating || spaceState.loading.updating}
                type="submit"
                className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                {editingSpace ? 'Update Space' : 'Create Space'}
              </ButtonLoader>
            </div>
          </form>
        </div>
      </div>
    );
  };

  const SpaceCard = ({ space }) => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
      <div className="h-48 bg-gray-200 relative">
        <div className="absolute inset-0 flex items-center justify-center">
          <ImageIcon size={48} className="text-gray-400" />
        </div>
        <div className="absolute top-2 right-2">
          <span className={`px-2 py-1 text-xs font-medium rounded-full ${
            space.is_active 
              ? 'bg-green-100 text-green-800'
              : 'bg-red-100 text-red-800'
          }`}>
            {space.is_active ? 'Active' : 'Inactive'}
          </span>
        </div>
      </div>
      
      <div className="p-4">
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-lg font-semibold text-gray-900">{space.name}</h3>
          <span className="text-sm text-gray-500 capitalize">
            {space.type.replace('_', ' ')}
          </span>
        </div>
        
        <p className="text-gray-600 text-sm mb-3 line-clamp-2">
          {space.description || 'No description available'}
        </p>
        
        <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
          <div className="flex items-center">
            <Users className="h-4 w-4 mr-1" />
            <span>{space.capacity} people</span>
          </div>
          <div className="flex items-center">
            <MapPin className="h-4 w-4 mr-1" />
            <span>{space.floor ? `Floor ${space.floor}` : 'No floor'}</span>
          </div>
          <div className="flex items-center">
            <DollarSign className="h-4 w-4 mr-1" />
            <span>${space.hourly_rate || 0}/hr</span>
          </div>
        </div>
        
        <div className="flex justify-between">
          <button
            onClick={() => handleEditSpace(space)}
            className="flex items-center px-3 py-1 text-blue-600 hover:bg-blue-50 rounded-md transition-colors"
          >
            <Edit className="h-4 w-4 mr-1" />
            Edit
          </button>
          <button
            onClick={() => handleDeleteSpace(space.id)}
            className="flex items-center px-3 py-1 text-red-600 hover:bg-red-50 rounded-md transition-colors"
          >
            <Trash2 className="h-4 w-4 mr-1" />
            Delete
          </button>
        </div>
      </div>
    </div>
  );

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Space Management</h1>
          <p className="text-gray-600 mt-1">
            Manage your workspace inventory and settings
          </p>
        </div>
        <button
          onClick={handleCreateSpace}
          className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Space
        </button>
      </div>

      {/* Search and Filters */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-4 flex-1">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="text"
                placeholder="Search spaces..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center px-3 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
            >
              <Filter className="h-4 w-4 mr-2" />
              Filters
            </button>
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={() => spaceActions.setViewMode('grid')}
              className={`p-2 rounded-md ${
                spaceState.viewMode === 'grid' 
                  ? 'bg-blue-100 text-blue-600' 
                  : 'text-gray-400 hover:text-gray-600'
              }`}
            >
              <Grid className="h-4 w-4" />
            </button>
            <button
              onClick={() => spaceActions.setViewMode('list')}
              className={`p-2 rounded-md ${
                spaceState.viewMode === 'list' 
                  ? 'bg-blue-100 text-blue-600' 
                  : 'text-gray-400 hover:text-gray-600'
              }`}
            >
              <List className="h-4 w-4" />
            </button>
          </div>
        </div>

        {/* Filter Panel */}
        {showFilters && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 pt-4 border-t">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Type
              </label>
              <select
                value={spaceState.filters.type}
                onChange={(e) => spaceActions.setFilters({ type: e.target.value })}
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
              >
                <option value="all">All Types</option>
                <option value="meeting_room">Meeting Room</option>
                <option value="conference_room">Conference Room</option>
                <option value="hot_desk">Hot Desk</option>
                <option value="private_office">Private Office</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Min Capacity
              </label>
              <select
                value={spaceState.filters.capacity}
                onChange={(e) => spaceActions.setFilters({ capacity: e.target.value })}
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
              >
                <option value="all">Any Size</option>
                <option value="2">2+ People</option>
                <option value="5">5+ People</option>
                <option value="10">10+ People</option>
                <option value="20">20+ People</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Status
              </label>
              <select
                value={spaceState.filters.availability}
                onChange={(e) => spaceActions.setFilters({ availability: e.target.value })}
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
              >
                <option value="all">All Status</option>
                <option value="Available">Active</option>
                <option value="Maintenance">Inactive</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Floor
              </label>
              <select
                value={spaceState.filters.floor}
                onChange={(e) => spaceActions.setFilters({ floor: e.target.value })}
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
              >
                <option value="all">All Floors</option>
                <option value="1">Floor 1</option>
                <option value="2">Floor 2</option>
                <option value="3">Floor 3</option>
              </select>
            </div>
          </div>
        )}
      </div>

      {/* Spaces Grid/List */}
      {spaceState.loading.spaces ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {Array.from({ length: 8 }).map((_, index) => (
            <CardLoader key={index} className="h-64" />
          ))}
        </div>
      ) : filteredSpaces.length > 0 ? (
        <div className={
          spaceState.viewMode === 'grid'
            ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
            : "space-y-4"
        }>
          {filteredSpaces.map(space => (
            <SpaceCard key={space.id} space={space} />
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <MapPin className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No spaces found</h3>
          <p className="text-gray-600 mb-4">
            {searchTerm ? 'Try adjusting your search or filters' : 'Get started by creating your first space'}
          </p>
          <button
            onClick={handleCreateSpace}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            <Plus className="h-4 w-4 mr-2" />
            Create Space
          </button>
        </div>
      )}

      {/* Space Modal */}
      {showSpaceModal && <SpaceModal />}
    </div>
  );
};

export default EnhancedSpaceManagementPage;
