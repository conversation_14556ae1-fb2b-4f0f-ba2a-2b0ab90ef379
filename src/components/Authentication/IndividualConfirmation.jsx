import React from "react";
import { useNavigate } from "react-router-dom";

const IndividualConfirmation = () => {
  const navigate = useNavigate();
  
  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-white p-4">
      <div className="max-w-md w-full text-center">
        <div className="mb-8 flex justify-center">
          <img
            src="/images/logo-blue.png"
            alt="JechSpace Logo"
            className="h-10 object-contain"
          />
        </div>
        
        <div className="bg-blue-50 rounded-2xl p-8 mb-8 flex flex-col items-center">
          <div className="bg-blue-500 rounded-full p-4 mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          
          <h1 className="text-2xl font-bold text-gray-800 mb-2">Verification Email Sent</h1>
          <p className="text-gray-600 mb-4">
            We've sent a confirmation link to your email address. Please check your inbox and click the link to verify your account.
          </p>
        </div>

        <div className="text-sm text-gray-600 mb-8">
          <p className="mb-1">Didn't receive the email?</p>
          <p>
            Check your spam folder or{" "}
            <button className="text-blue-500 hover:underline">
              resend verification link
            </button>
          </p>
        </div>
        
        <button 
          onClick={() => navigate("/user/dashboard")} 
          className="bg-blue-500 hover:bg-blue-600 text-white py-2 px-6 rounded-md transition-all duration-200"
        >
          Continue to Dashboard
        </button>
      </div>
    </div>
  );
};

export default IndividualConfirmation;