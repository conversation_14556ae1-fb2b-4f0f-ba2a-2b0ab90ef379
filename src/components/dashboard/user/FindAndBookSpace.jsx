// Create a functional Find & Book Space page inside the user dashboard with a simple calendar, list view, filters, and a booking modal.

import { useState, useEffect } from "react";
import FullCalendar from "@fullcalendar/react";
import dayGridPlugin from "@fullcalendar/daygrid";
import timeGridPlugin from "@fullcalendar/timegrid";
import interactionPlugin from "@fullcalendar/interaction";
import BookingModal from "./BookingModal";
import UserDashboardLayout from "./UserDashboardLayout";
import { Calendar, List, Filter, Search } from "lucide-react";

const FindAndBookSpace = () => {
  const [viewMode, setViewMode] = useState("calendar"); // calendar | list
  const [showModal, setShowModal] = useState(false);
  const [selectedSpace, setSelectedSpace] = useState(null);
  const [filters, setFilters] = useState({
    date: new Date().toISOString().split("T")[0],
    spaceType: "",
    location: "",
  });
  const [showFilters, setShowFilters] = useState(false);
  const [bookedEvents, setBookedEvents] = useState([]); // Track booked events

  // Sample spaces data (replace with API call)
  const spaces = [
    {
      id: 1,
      name: "Desk #32",
      type: "desk",
      location: "lagos",
      capacity: 1,
      availableFrom: "08:00",
      availableTo: "18:00",
    },
    {
      id: 2,
      name: "Meeting Room Alpha",
      type: "meeting-room",
      location: "lagos",
      capacity: 8,
      availableFrom: "09:00",
      availableTo: "17:00",
    },
    {
      id: 3,
      name: "Private Office Gamma",
      type: "private-office",
      location: "abuja",
      capacity: 4,
      availableFrom: "08:00",
      availableTo: "20:00",
    },
  ];

  const handleFilterChange = (e) => {
    const { name, value } = e.target;
    setFilters({
      ...filters,
      [name]: value,
    });
  };

  const handleBookNow = (space) => {
    setSelectedSpace(space);
    setShowModal(true);
  };

  const handleBookingConfirm = (space) => {
    const newEvent = {
      title: space.name,
      date: `${filters.date}T${space.availableFrom}:00`,
      end: `${filters.date}T${space.availableTo}:00`,
      extendedProps: { ...space },
    };
    setBookedEvents([...bookedEvents, newEvent]);
    setShowModal(false);
  };

  // Convert spaces to calendar events
  const events = [
    ...spaces.map((space) => ({
      title: space.name,
      date: `${filters.date}T${space.availableFrom}:00`,
      end: `${filters.date}T${space.availableTo}:00`,
      extendedProps: { ...space },
    })),
    ...bookedEvents, // Include dynamically booked events
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto p-4 sm:p-6">
        {/* Header and Title */}
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-800">
            Find & Book Space
          </h1>
          <p className="text-gray-600">Find and book your ideal workspace</p>
        </div>

        {/* Controls Bar */}
        <div className="bg-white rounded-lg shadow-sm mb-6 p-4">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
            {/* View Toggle Buttons */}
            <div className="flex items-center space-x-2 bg-gray-100 p-1 rounded-md">
              <button
                onClick={() => setViewMode("calendar")}
                className={`flex items-center px-3 py-2 rounded ${
                  viewMode === "calendar"
                    ? "bg-primary text-white"
                    : "text-gray-700 hover:bg-gray-200"
                }`}
              >
                <Calendar className="mr-2" /> Calendar
              </button>
              <button
                onClick={() => setViewMode("list")}
                className={`flex items-center px-3 py-2 rounded ${
                  viewMode === "list"
                    ? "bg-primary text-white"
                    : "text-gray-700 hover:bg-gray-200"
                }`}
              >
                <List className="mr-2" /> List
              </button>
            </div>

            {/* Filter Toggle Button */}
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200"
            >
              <Filter className="mr-2" />
              {showFilters ? "Hide Filters" : "Show Filters"}
            </button>
          </div>

          {/* Expandable Filters */}
          {showFilters && (
            <div className="mt-4 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Date Picker */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Date
                </label>
                <input
                  type="date"
                  name="date"
                  value={filters.date}
                  onChange={handleFilterChange}
                  className="w-full border border-gray-300 rounded-md p-2 focus:ring-primary focus:border-primary"
                />
              </div>

              {/* Space Type */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Space Type
                </label>
                <select
                  name="spaceType"
                  value={filters.spaceType}
                  onChange={handleFilterChange}
                  className="w-full border border-gray-300 rounded-md p-2 focus:ring-primary focus:border-primary"
                >
                  <option value="">All Types</option>
                  <option value="desk">Desk</option>
                  <option value="meeting-room">Meeting Room</option>
                  <option value="private-office">Private Office</option>
                </select>
              </div>

              {/* Location */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Location
                </label>
                <select
                  name="location"
                  value={filters.location}
                  onChange={handleFilterChange}
                  className="w-full border border-gray-300 rounded-md p-2 focus:ring-primary focus:border-primary"
                >
                  <option value="">All Locations</option>
                  <option value="lagos">Lagos</option>
                  <option value="abuja">Abuja</option>
                  <option value="port-harcourt">Port Harcourt</option>
                </select>
              </div>

              {/* Apply Filters Button */}
              <div className="self-end">
                <button className="w-full bg-primary text-white py-2 px-4 rounded-md hover:bg-primary-dark transition-colors flex items-center justify-center">
                  <Search className="mr-2" /> Search Spaces
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Calendar or List View */}
        <div className="bg-white rounded-lg shadow-sm p-4">
          {viewMode === "calendar" ? (
            <div className="calendar-container">
              <FullCalendar
                plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin]}
                initialView="timeGridDay"
                height="650px"
                headerToolbar={{
                  left: "prev,next today",
                  center: "title",
                  right: "timeGridDay,timeGridWeek",
                }}
                slotMinTime="08:00:00"
                slotMaxTime="20:00:00"
                selectable={true}
                nowIndicator={true}
                dateClick={(info) => {
                  console.log(info.dateStr);
                  setShowModal(true);
                }}
                events={events}
                eventClick={(info) => {
                  handleBookNow(info.event.extendedProps);
                }}
                eventClassNames="cursor-pointer"
              />
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {spaces
                .filter((space) => {
                  if (filters.spaceType && space.type !== filters.spaceType)
                    return false;
                  if (filters.location && space.location !== filters.location)
                    return false;
                  return true;
                })
                .map((space) => (
                  <div
                    key={space.id}
                    className="border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition-shadow"
                  >
                    <div className="bg-gray-50 p-3 border-b">
                      <h3 className="font-semibold text-lg">{space.name}</h3>
                      <div className="flex items-center justify-between mt-1 text-sm text-gray-500">
                        <span>
                          {space.type
                            .replace("-", " ")
                            .replace(/\b\w/g, (l) => l.toUpperCase())}
                        </span>
                        <span>
                          {space.location.charAt(0).toUpperCase() +
                            space.location.slice(1)}
                        </span>
                      </div>
                    </div>
                    <div className="p-4">
                      <div className="flex justify-between mb-3">
                        <span className="text-gray-600">Capacity:</span>
                        <span className="font-medium">
                          {space.capacity}{" "}
                          {space.type === "desk" ? "person" : "people"}
                        </span>
                      </div>
                      <div className="flex justify-between mb-4">
                        <span className="text-gray-600">Available Hours:</span>
                        <span className="font-medium">
                          {space.availableFrom} - {space.availableTo}
                        </span>
                      </div>
                      <button
                        onClick={() => handleBookNow(space)}
                        className="w-full mt-2 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors"
                      >
                        Book Now
                      </button>
                    </div>
                  </div>
                ))}

              {spaces.filter((space) => {
                if (filters.spaceType && space.type !== filters.spaceType)
                  return false;
                if (filters.location && space.location !== filters.location)
                  return false;
                return true;
              }).length === 0 && (
                <div className="col-span-full py-12 flex flex-col items-center justify-center text-gray-500">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-16 w-16 mb-4"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={1}
                      d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"
                    />
                  </svg>
                  <h3 className="text-xl font-medium mb-2">No spaces found</h3>
                  <p>Try adjusting your filters to see more results</p>
                </div>
              )}
            </div>
          )}
        </div>

        {/* add a back to dashboard botton */}
        <div className="mt-6">
          <button
            onClick={() => window.history.back()}
            className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors"
          >
            Back to Dashboard
          </button>
        </div>

        {/* Booking Modal */}
        <BookingModal
          isOpen={showModal}
          onClose={() => setShowModal(false)}
          space={selectedSpace}
          date={filters.date}
          onConfirm={handleBookingConfirm}
        />
      </div>
    </div>
  );
};

export default FindAndBookSpace;
