# JechSpace - Workspace Management Solution

A modern platform for managing workspace bookings, availability, and utilization to efficiently allocate spaces, streamline the booking process, and optimize workspace usage

check out our Figma File here: [Figma Prototype Presentation](https://www.figma.com/proto/bqGkXQpCrUvzE71yYqyAjn/ALX-Hackathon--workspace-booking-system-?node-id=177-72&p=f&t=ZEwn9EyX0SaOdvZD-1&scaling=scale-down&content-scaling=fixed&page-id=2%3A3560&starting-point-node-id=177%3A72&show-proto-sidebar=1)

## Getting Started

### Prerequisites

- Node.js (v16.0.0 or higher)
- npm or yarn

### Installation

1. Clone the repository

```bash
git clone https://github.com/Jechies/jechies-frontend.git
cd jechspace
```

2. Install dependencies

```bash
npm install
# or
yarn install
```

3. Start the development server

```bash
npm run dev
# or
yarn dev
```

4. Open [http://localhost:5173](http://localhost:5173) to view the application in your browser

## Tech Stack

- **Framework**: React + Vite
- **Styling**: Tailwind CSS / Plain CSS
- **State Management**: Zustand (planned)
- **UI Components**: shadcn/ui (planned)
- **Form Handling**: React Hook Form (planned)
- **Data Fetching**: React Query (planned)
- **Calendar Interface**: FullCalendar (planned)
- **Authentication**: Firebase Auth (planned)

## Workflow & Contribution Guidelines

### Branch Strategy

- `main`: Production-ready code
- `development`: Integration branch for features
- `feature/[feature-name]`: Individual feature branches

### How to Contribute

1. Ensure you're working on the latest version of the codebase

```bash
git checkout staging
git pull origin staging
```

2. Create a new branch for your feature or bugfix

```bash
git checkout -b feature/your-feature-name
# or
git checkout -b bugfix/your-bugfix-name
```

3. Make your changes and commit them using descriptive commit messages

```bash
git add .
git commit -m "feat: add workspace booking calendar component"
```

4. Push your branch to the remote repository

```bash
git push origin feature/your-feature-name
```

5. Create a Pull Request to merge your changes into the `staging` branch

### Code Style Guidelines

- Use functional components with hooks
- Create reusable components when possible
- Use Tailwind CSS for styling following the utility-first approach
- Keep components small and focused on a single responsibility

## 📋 Features

### Core (Must-Have)

- User Authentication
- Workspace Booking System
- Booking Management (cancel, reschedule, extend)
- Email Notifications & Reminders
- Admin Reporting & Analytics
- User Role Management

### Extended (Nice-to-Have)

- Interactive Floor Plans
- Calendar Integration
- SMS Notifications
- Payment Processing
- Mobile Optimization
- Third-party Tool Integrations

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 👥 Team

- Abayomi - Frontend Developer / Product Designer
- Pelumi - Frontend Developer

<!-- # React + Vite

This template provides a minimal setup to get React working in Vite with HMR and some ESLint rules.

Currently, two official plugins are available:

- [@vitejs/plugin-react](https://github.com/vitejs/vite-plugin-react/blob/main/packages/plugin-react/README.md) uses [Babel](https://babeljs.io/) for Fast Refresh
- [@vitejs/plugin-react-swc](https://github.com/vitejs/vite-plugin-react-swc) uses [SWC](https://swc.rs/) for Fast Refresh

## Expanding the ESLint configuration

If you are developing a production application, we recommend using TypeScript and enable type-aware lint rules. Check out the [TS template](https://github.com/vitejs/vite/tree/main/packages/create-vite/template-react-ts) to integrate TypeScript and [`typescript-eslint`](https://typescript-eslint.io) in your project. -->
