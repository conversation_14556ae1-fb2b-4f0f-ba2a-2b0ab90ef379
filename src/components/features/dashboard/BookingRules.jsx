import { useState } from 'react';
import { 
  Plus, 
  Settings, 
  Trash2, 
  Clock, 
  Calendar, 
  Users, 
  DollarSign, 
  ChevronDown,
  ChevronUp,
  Copy,
  Save
} from 'lucide-react';

const BookingRules = () => {
  const [rules, setRules] = useState([
    {
      id: 1,
      name: 'Weekend Booking Limit',
      type: 'time',
      description: 'Restricts bookings on weekends to business hours only',
      conditions: ['Saturday', 'Sunday'],
      timeRange: '9:00 AM - 5:00 PM',
      isActive: true,
      isExpanded: false
    },
    {
      id: 2,
      name: 'Conference Room Capacity',
      type: 'capacity',
      description: 'Sets maximum occupancy for conference rooms',
      capacity: 25,
      spaces: ['Main Conference Room', 'Meeting Room A'],
      isActive: true,
      isExpanded: false
    },
    {
      id: 3,
      name: 'Advanced Booking Requirement',
      type: 'advance',
      description: 'Requires bookings to be made at least 24 hours in advance',
      hours: 24,
      isActive: false,
      isExpanded: false
    }
  ]);

  const [showNewRuleForm, setShowNewRuleForm] = useState(false);
  const [selectedRuleType, setSelectedRuleType] = useState('time');
  const [newRule, setNewRule] = useState({
    name: '',
    description: '',
    type: 'time',
    conditions: [],
    timeRange: '9:00 AM - 5:00 PM',
    capacity: 10,
    spaces: [],
    hours: 24,
    isActive: true
  });

  const ruleTypes = [
    { id: 'time', name: 'Time Restriction', icon: <Clock size={18} /> },
    { id: 'capacity', name: 'Capacity Limit', icon: <Users size={18} /> },
    { id: 'advance', name: 'Advance Notice', icon: <Calendar size={18} /> },
    { id: 'pricing', name: 'Pricing Rule', icon: <DollarSign size={18} /> }
  ];

  const toggleRuleExpansion = (id) => {
    setRules(rules.map(rule => 
      rule.id === id ? { ...rule, isExpanded: !rule.isExpanded } : rule
    ));
  };

  const toggleRuleStatus = (id) => {
    setRules(rules.map(rule => 
      rule.id === id ? { ...rule, isActive: !rule.isActive } : rule
    ));
  };

  const deleteRule = (id) => {
    setRules(rules.filter(rule => rule.id !== id));
  };

  const duplicateRule = (ruleId) => {
    const ruleToDuplicate = rules.find(rule => rule.id === ruleId);
    if (ruleToDuplicate) {
      const newRuleCopy = {
        ...ruleToDuplicate,
        id: Math.max(...rules.map(r => r.id)) + 1,
        name: `${ruleToDuplicate.name} (Copy)`,
        isExpanded: false
      };
      setRules([...rules, newRuleCopy]);
    }
  };

  const handleAddNewRule = () => {
    const newRuleWithId = {
      ...newRule,
      id: Math.max(...rules.map(r => r.id), 0) + 1,
      isExpanded: false
    };
    setRules([...rules, newRuleWithId]);
    setShowNewRuleForm(false);
    setNewRule({
      name: '',
      description: '',
      type: 'time',
      conditions: [],
      timeRange: '9:00 AM - 5:00 PM',
      capacity: 10,
      spaces: [],
      hours: 24,
      isActive: true
    });
  };

  const handleNewRuleChange = (e) => {
    const { name, value } = e.target;
    setNewRule({ ...newRule, [name]: value });
  };

  const renderRuleTypeSelector = () => (
    <div className="mt-4">
      <label className="block text-sm font-medium text-blue-700 mb-2">Rule Type</label>
      <div className="grid grid-cols-2 gap-2 md:grid-cols-4">
        {ruleTypes.map(type => (
          <button
            key={type.id}
            className={`flex items-center p-3 rounded-lg border transition-all ${
              selectedRuleType === type.id
                ? 'bg-blue-100 border-blue-500 shadow-sm'
                : 'bg-white border-gray-200 hover:bg-blue-50'
            }`}
            onClick={() => {
              setSelectedRuleType(type.id);
              setNewRule({ ...newRule, type: type.id });
            }}
          >
            <div className="mr-2 text-blue-600">{type.icon}</div>
            <span className="text-sm font-medium">{type.name}</span>
          </button>
        ))}
      </div>
    </div>
  );

  const renderNewRuleForm = () => {
    if (!showNewRuleForm) return null;
    
    return (
      <div className="bg-white rounded-lg shadow-lg p-6 mb-6 border border-blue-100">
        <h3 className="text-lg font-semibold text-blue-800 mb-4">Create New Booking Rule</h3>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-blue-700 mb-1">Rule Name</label>
            <input
              type="text"
              name="name"
              value={newRule.name}
              onChange={handleNewRuleChange}
              className="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Enter rule name"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-blue-700 mb-1">Description</label>
            <textarea
              name="description"
              value={newRule.description}
              onChange={handleNewRuleChange}
              className="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Briefly describe the rule"
              rows="2"
            />
          </div>
          
          {renderRuleTypeSelector()}
          
          {selectedRuleType === 'time' && (
            <div className="bg-blue-50 p-4 rounded-lg">
              <label className="block text-sm font-medium text-blue-700 mb-1">Time Range</label>
              <input
                type="text"
                name="timeRange"
                value={newRule.timeRange}
                onChange={handleNewRuleChange}
                className="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="e.g. 9:00 AM - 5:00 PM"
              />
              
              <div className="mt-3">
                <label className="block text-sm font-medium text-blue-700 mb-2">Applicable Days</label>
                <div className="flex flex-wrap gap-2">
                  {['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'].map((day) => (
                    <button
                      key={day}
                      className={`px-3 py-1 text-sm rounded-full transition-colors ${
                        newRule.conditions.includes(day)
                          ? 'bg-blue-600 text-white'
                          : 'bg-white border border-blue-300 text-blue-700 hover:bg-blue-100'
                      }`}
                      onClick={() => {
                        const updatedConditions = newRule.conditions.includes(day)
                          ? newRule.conditions.filter(d => d !== day)
                          : [...newRule.conditions, day];
                        setNewRule({ ...newRule, conditions: updatedConditions });
                      }}
                    >
                      {day}
                    </button>
                  ))}
                </div>
              </div>
            </div>
          )}
          
          {selectedRuleType === 'capacity' && (
            <div className="bg-blue-50 p-4 rounded-lg">
              <label className="block text-sm font-medium text-blue-700 mb-1">Maximum Capacity</label>
              <input
                type="number"
                name="capacity"
                value={newRule.capacity}
                onChange={handleNewRuleChange}
                className="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                min="1"
              />
              
              <div className="mt-3">
                <label className="block text-sm font-medium text-blue-700 mb-1">Apply to Spaces</label>
                <select
                  className="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  multiple
                  size="3"
                  onChange={(e) => {
                    const selected = Array.from(e.target.selectedOptions, option => option.value);
                    setNewRule({ ...newRule, spaces: selected });
                  }}
                >
                  <option value="Main Conference Room">Main Conference Room</option>
                  <option value="Meeting Room A">Meeting Room A</option>
                  <option value="Meeting Room B">Meeting Room B</option>
                  <option value="Creative Space">Creative Space</option>
                  <option value="Quiet Zone">Quiet Zone</option>
                </select>
              </div>
            </div>
          )}
          
          {selectedRuleType === 'advance' && (
            <div className="bg-blue-50 p-4 rounded-lg">
              <label className="block text-sm font-medium text-blue-700 mb-1">Hours in Advance</label>
              <input
                type="number"
                name="hours"
                value={newRule.hours}
                onChange={handleNewRuleChange}
                className="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                min="1"
              />
            </div>
          )}
          
          {selectedRuleType === 'pricing' && (
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="text-sm text-blue-600 italic mb-2">
                Pricing rules can be configured in the pricing section
              </div>
              <a href="#" className="inline-block px-4 py-2 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 transition-colors">
                Go to Pricing Settings
              </a>
            </div>
          )}
          
          <div className="flex justify-end gap-3 pt-4">
            <button
              className="px-4 py-2 bg-white border border-gray-300 rounded-md hover:bg-gray-50 text-gray-700 text-sm font-medium transition-colors"
              onClick={() => setShowNewRuleForm(false)}
            >
              Cancel
            </button>
            <button
              className="px-4 py-2 bg-blue-600 rounded-md hover:bg-blue-700 text-white text-sm font-medium transition-colors flex items-center"
              onClick={handleAddNewRule}
              disabled={!newRule.name}
            >
              <Save size={16} className="mr-1" />
              Save Rule
            </button>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-blue-50 p-6">
      <div className="max-w-5xl mx-auto">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-blue-700">Booking Rules</h1>
          <button
            className="px-4 py-2 bg-blue-600 rounded-md hover:bg-blue-700 text-white text-sm font-medium transition-colors flex items-center"
            onClick={() => setShowNewRuleForm(!showNewRuleForm)}
          >
            <Plus size={20} className="mr-1" />
            {showNewRuleForm ? 'Cancel' : 'Add New Rule'}
          </button>
        </div>
        
        <div className="bg-white rounded-lg shadow-md p-4 mb-6">
          <div className="flex items-start space-x-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Settings size={24} className="text-blue-600" />
            </div>
            <div>
              <h2 className="text-lg font-medium text-blue-800">Booking Rules Configuration</h2>
              <p className="text-sm text-gray-600 mt-1">
                Create and manage rules that control how users can book your spaces. 
                Rules can restrict booking times, set capacity limits, require advance notice, and more.
              </p>
            </div>
          </div>
        </div>
        
        {renderNewRuleForm()}
        
        <div className="space-y-4">
          {rules.map(rule => (
            <div key={rule.id} className={`bg-white rounded-lg shadow-md overflow-hidden border-l-4 ${rule.isActive ? 'border-l-green-500' : 'border-l-gray-300'}`}>
              <div className="p-4 flex justify-between items-center cursor-pointer" onClick={() => toggleRuleExpansion(rule.id)}>
                <div className="flex items-center">
                  <div className={`p-2 rounded-lg mr-3 ${rule.isActive ? 'bg-green-100' : 'bg-gray-100'}`}>
                    {rule.type === 'time' && <Clock size={20} className={rule.isActive ? 'text-green-600' : 'text-gray-500'} />}
                    {rule.type === 'capacity' && <Users size={20} className={rule.isActive ? 'text-green-600' : 'text-gray-500'} />}
                    {rule.type === 'advance' && <Calendar size={20} className={rule.isActive ? 'text-green-600' : 'text-gray-500'} />}
                    {rule.type === 'pricing' && <DollarSign size={20} className={rule.isActive ? 'text-green-600' : 'text-gray-500'} />}
                  </div>
                  <div>
                    <h3 className="font-medium text-blue-700">{rule.name}</h3>
                    <p className="text-sm text-gray-600">{rule.description}</p>
                  </div>
                </div>
                <div className="flex items-center">
                  <span className={`text-xs px-2 py-1 rounded-full mr-3 ${
                    rule.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'
                  }`}>
                    {rule.isActive ? 'Active' : 'Inactive'}
                  </span>
                  {rule.isExpanded ? <ChevronUp size={20} className="text-blue-600" /> : <ChevronDown size={20} className="text-blue-600" />}
                </div>
              </div>
              
              {rule.isExpanded && (
                <div className="p-4 border-t border-gray-100 bg-blue-50">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="text-sm font-medium text-blue-700 mb-2">Rule Details</h4>
                      
                      {rule.type === 'time' && (
                        <div className="space-y-2">
                          <div className="flex items-center">
                            <span className="text-sm font-medium text-gray-500 w-32">Time Range:</span>
                            <span className="text-sm text-blue-700">{rule.timeRange}</span>
                          </div>
                          <div className="flex items-start">
                            <span className="text-sm font-medium text-gray-500 w-32">Applies to:</span>
                            <div className="flex flex-wrap gap-1">
                              {rule.conditions.map(day => (
                                <span key={day} className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full">
                                  {day}
                                </span>
                              ))}
                            </div>
                          </div>
                        </div>
                      )}
                      
                      {rule.type === 'capacity' && (
                        <div className="space-y-2">
                          <div className="flex items-center">
                            <span className="text-sm font-medium text-gray-500 w-32">Max Capacity:</span>
                            <span className="text-sm text-blue-700">{rule.capacity} people</span>
                          </div>
                          <div className="flex items-start">
                            <span className="text-sm font-medium text-gray-500 w-32">Applies to:</span>
                            <div className="flex flex-wrap gap-1">
                              {rule.spaces.map(space => (
                                <span key={space} className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full">
                                  {space}
                                </span>
                              ))}
                            </div>
                          </div>
                        </div>
                      )}
                      
                      {rule.type === 'advance' && (
                        <div className="flex items-center">
                          <span className="text-sm font-medium text-gray-500 w-32">Advance Notice:</span>
                          <span className="text-sm text-blue-700">{rule.hours} hours</span>
                        </div>
                      )}
                    </div>
                    
                    <div className="flex flex-col justify-end">
                      <div className="flex justify-end gap-2">
                        <button 
                          className="p-2 rounded-md hover:bg-blue-100 text-blue-600 transition-colors"
                          onClick={() => duplicateRule(rule.id)}
                          title="Duplicate Rule"
                        >
                          <Copy size={18} />
                        </button>
                        <button 
                          className="p-2 rounded-md hover:bg-red-100 text-red-500 transition-colors"
                          onClick={() => deleteRule(rule.id)}
                          title="Delete Rule"
                        >
                          <Trash2 size={18} />
                        </button>
                        <button 
                          className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                            rule.isActive 
                              ? 'bg-gray-100 text-gray-700 hover:bg-gray-200' 
                              : 'bg-green-600 text-white hover:bg-green-700'
                          }`}
                          onClick={(e) => {
                            e.stopPropagation();
                            toggleRuleStatus(rule.id);
                          }}
                        >
                          {rule.isActive ? 'Deactivate' : 'Activate'}
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default BookingRules;