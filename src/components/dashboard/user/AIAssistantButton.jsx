/* import { motion } from "framer-motion";
import { BotIcon, XIcon } from "lucide-react";
import PropTypes from "prop-types";
import { Link } from "react-router-dom";
import AIAssistantWidget from "./AIAssistantWidget";

const AIAssistantButton = ({ onClick, isOpen }) => {

  // Function to handle button click
  const handleClick = () => {
    onClick();
  };
  // Function to handle link click
  const handleLinkClick = () => {
    onClick();
  };
  // Function to handle button click
  
  return (
    <>
      <motion.button
        onClick={onClick}
        // link to AI Assistant page
        component={Link({ to: <AIAssistantWidget /> })}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        className="fixed bottom-6 right-6 w-14 h-14 rounded-full bg-gradient-to-r from-primary to-blue-700 hover:translate-y-[-3px] text-white shadow-lg flex items-center justify-center z-40"
        aria-label={isOpen ? "Close AI Assistant" : "Open AI Assistant"}
      >
        {isOpen ? (
          <XIcon className="text-xl" />
        ) : (
          <BotIcon className="text-xl" />
        )}
      </motion.button>
    </>
  );
};

AIAssistantButton.propTypes = {
  onClick: PropTypes.func.isRequired,
  isOpen: PropTypes.bool.isRequired,
};

export default AIAssistantButton;
 */

import { motion } from "framer-motion";
import { BotIcon, XIcon } from "lucide-react";
import PropTypes from "prop-types";

const AIAssistantButton = ({ onClick, isOpen }) => {
  return (
    <motion.button
      onClick={onClick}
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      className="fixed bottom-6 right-6 w-14 h-14 rounded-full bg-gradient-to-r from-primary to-blue-700 hover:translate-y-[-3px] text-white shadow-lg flex items-center justify-center z-40"
      aria-label={isOpen ? "Close AI Assistant" : "Open AI Assistant"}
    >
      {isOpen ? <XIcon className="text-xl" /> : <BotIcon className="text-xl" />}
    </motion.button>
  );
};

AIAssistantButton.propTypes = {
  onClick: PropTypes.func.isRequired,
  isOpen: PropTypes.bool.isRequired,
};

export default AIAssistantButton;
