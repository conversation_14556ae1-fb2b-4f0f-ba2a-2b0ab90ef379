import { motion } from "framer-motion";
import { useState } from "react";
import { Star, ArrowLeft, ArrowRight } from "lucide-react";

const testimonials = [
  {
    content:
      "JechSpace has completely transformed how we manage our office spaces. The analytics and reporting features have helped us optimize our workspace utilization by over 30%, leading to significant cost savings.",
    author: "<PERSON> Daniel",
    title: "Facilities Manager, TechCorp Inc.",
    rating: 5,
  },
  {
    content:
      "The user management system is incredibly intuitive. We've been able to roll this out to our entire organization with minimal training, and the feedback has been overwhelmingly positive.",
    author: "<PERSON>",
    title: "Operations Director, Globe Enterprises",
    rating: 5,
  },
  {
    content:
      "I was skeptical at first, but JechSpace has exceeded all our expectations. The maintenance scheduling feature alone has saved us countless hours of manual work and improved our response times dramatically.",
    author: "<PERSON>",
    title: "Workplace Experience Manager, Innovate Co.",
    rating: 4,
  },
];

const TestimonialsSection = () => {
  const [currentIndex, setCurrentIndex] = useState(0);

  const nextTestimonial = () => {
    setCurrentIndex((prevIndex) => (prevIndex + 1) % testimonials.length);
  };

  const prevTestimonial = () => {
    setCurrentIndex(
      (prevIndex) => (prevIndex - 1 + testimonials.length) % testimonials.length
    );
  };

  //   add image dynamically
  const imageSrc =
    testimonials[currentIndex].image ||
    "/images/testimonial-" + currentIndex + ".png";

  return (
    <section id="testimonials" className="py-24 bg-gray-50">
      <div className="container mx-auto px-4">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl font-bold mb-4">What Our Clients Say</h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Don't just take our word for it - see what our clients have to say
            about their experience with JechSpace.
          </p>
        </motion.div>

        <div className="max-w-4xl mx-auto relative">
          <motion.div
            key={currentIndex}
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -50 }}
            transition={{ duration: 0.5 }}
            className="bg-white rounded-2xl p-8 md:p-12 shadow-lg"
          >
            <div className="flex text-yellow-400 mb-4">
              {[...Array(5)].map((_, i) => (
                <Star
                  key={i}
                  size={20}
                  className={
                    i < testimonials[currentIndex].rating
                      ? "text-yellow-400"
                      : "text-gray-200"
                  }
                  fill={
                    i < testimonials[currentIndex].rating
                      ? "currentColor"
                      : "none"
                  }
                />
              ))}
            </div>

            <p className="text-lg md:text-xl mb-8 relative">
              {testimonials[currentIndex].content}
            </p>

            <div className="flex items-center gap-4">
              <div className="w-16 h-16 bg-gray-200 rounded-full overflow-hidden">
                <img
                  src={imageSrc}
                  alt={testimonials[currentIndex].author}
                  className="w-full h-full object-cover"
                />
              </div>
              <div>
                <h4 className="font-semibold">
                  {testimonials[currentIndex].author}
                </h4>
                <p className="text-gray-500 text-sm">
                  {testimonials[currentIndex].title}
                </p>
              </div>
            </div>
          </motion.div>

          {/* Navigation arrows */}
          <div className="flex justify-between mt-8">
            <button
              onClick={prevTestimonial}
              className="w-12 h-12 rounded-full bg-white shadow-md flex items-center justify-center text-primary hover:bg-primary hover:text-white transition-all"
            >
              <ArrowLeft size={18} />
            </button>

            <div className="flex gap-2">
              {testimonials.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentIndex(index)}
                  className={`w-3 h-3 rounded-full transition-all ${
                    currentIndex === index
                      ? "bg-primary scale-125"
                      : "bg-gray-300"
                  }`}
                  aria-label={`Go to testimonial ${index + 1}`}
                ></button>
              ))}
            </div>

            <button
              onClick={nextTestimonial}
              className="w-12 h-12 rounded-full bg-white shadow-md flex items-center justify-center text-primary hover:bg-primary hover:text-white transition-all"
            >
              <ArrowRight size={18} />
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default TestimonialsSection;
