// src/components/dashboard/Announcements.jsx
const Announcements = () => {
  const announcements = [
    {
      id: 1,
      title: "Office maintainance",
      message: "First floor unavailable on April 12",
    },
    {
      id: 2,
      title: "New collaboration space",
      message: "Now available on 2nd floor",
    },
  ];

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm">
      <h2 className="text-lg font-medium mb-4">Announcement</h2>
      <div className="space-y-4">
        {announcements.map((announcement) => (
          <div key={announcement.id} className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-medium">{announcement.title}</h3>
            <p className="text-gray-500 text-sm">{announcement.message}</p>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Announcements;
