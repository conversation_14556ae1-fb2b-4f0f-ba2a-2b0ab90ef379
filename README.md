# 🏢 JechSpace - Modern Workspace Management Platform

[![React](https://img.shields.io/badge/React-18.0+-blue.svg)](https://reactjs.org/)
[![Django](https://img.shields.io/badge/Django-REST-green.svg)](https://www.django-rest-framework.org/)
[![Tailwind CSS](https://img.shields.io/badge/Tailwind-CSS-38B2AC.svg)](https://tailwindcss.com/)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

**JechSpace** is a comprehensive, AI-powered workspace management platform that revolutionizes how organizations and individuals book, manage, and optimize shared workspaces. Built with cutting-edge technology, JechSpace provides intelligent space allocation, seamless booking experiences, and powerful analytics to maximize workspace utilization.

## 🌟 **Key Features**

✅ **Smart Booking System** - Interactive calendar with drag & drop booking
✅ **AI-Powered Assistant** - Natural language booking and recommendations
✅ **Real-time Analytics** - Comprehensive usage insights and reporting
✅ **Multi-tenant Architecture** - Supports individuals to enterprise organizations
✅ **Environment-Aware API** - Automatic dev/prod configuration
✅ **Modern UI/UX** - Responsive design with accessibility features
✅ **Advanced Security** - JWT authentication with role-based access control

## 🎯 **Live Demo & Design**

- **Figma Prototype**: [Interactive Design Preview](https://www.figma.com/proto/bqGkXQpCrUvzE71yYqyAjn/ALX-Hackathon--workspace-booking-system-?node-id=177-72&p=f&t=ZEwn9EyX0SaOdvZD-1&scaling=scale-down&content-scaling=fixed&page-id=2%3A3560&starting-point-node-id=177%3A72&show-proto-sidebar=1)
- **Development**: `http://localhost:5173` (auto-detects backend at `localhost:8000`)
- **Production**: Deployed with automatic API configuration

## 🚀 **Quick Start**

### **Prerequisites**

- **Node.js** (v18.0.0 or higher) - [Download here](https://nodejs.org/)
- **npm** or **yarn** package manager
- **Backend Server** - [JechSpace Backend](https://github.com/dohoudaniel/jechspace-frontend) running on port 8000

### **🔧 Installation & Setup**

#### **1. Clone the Repository**

```bash
git clone https://github.com/dohoudaniel/jechspace-frontend.git
cd jechspace-frontend
```

#### **2. Install Dependencies**

```bash
npm install
# or
yarn install
```

#### **3. Environment Configuration**

```bash
# Copy environment template
cp .env.example .env.local

# Edit .env.local with your settings (optional - auto-detects localhost)
VITE_API_BASE_URL=http://localhost:8000/api/v1/
VITE_ENABLE_LOGGING=true
```

#### **4. Start Development Server**

```bash
npm run dev
# or
yarn dev
```

#### **5. Access the Application**

- **Frontend**: [http://localhost:5173](http://localhost:5173)
- **Backend API**: [http://localhost:8000/api/v1/](http://localhost:8000/api/v1/)
- **API Health**: [http://localhost:8000/health/](http://localhost:8000/health/)

### **🔍 Verification**

Open browser console and look for:

```
🚀 JechSpace API Configuration: {
  environment: "development",
  apiBaseURL: "http://localhost:8000/api/v1/"
}
```

## 🛠️ **Technology Stack**

### **Frontend (React Application)**

- **Framework**: React 18 with modern hooks and context API
- **Build Tool**: Vite for fast development and optimized builds
- **Styling**: Tailwind CSS for responsive, modern UI design
- **State Management**: React Context API with custom providers
- **Routing**: React Router v6 with protected routes and lazy loading
- **Calendar**: FullCalendar.js for interactive booking management
- **Icons**: Lucide React for consistent iconography
- **HTTP Client**: Axios with interceptors and retry logic

### **Backend Integration**

- **API Framework**: Django REST Framework
- **Authentication**: JWT-based with automatic token refresh
- **Database**: PostgreSQL (production), SQLite (development)
- **Environment Detection**: Automatic localhost/production configuration
- **Error Handling**: Comprehensive error management with contextual messages

### **Development & Deployment**

- **Environment Management**: Automatic dev/prod detection
- **API Configuration**: Environment-aware endpoint management
- **Code Quality**: ESLint, Prettier, and modern best practices
- **Performance**: Code splitting, lazy loading, and optimization
- **Security**: HTTPS enforcement, input validation, CORS handling

## 🔄 **Development Workflow**

### **🌿 Branch Strategy**

- **`main`**: Production-ready code (protected)
- **`staging`**: Integration branch for testing features
- **`daniel-vibe-code-fe`**: Current development branch
- **`feature/[name]`**: Individual feature development
- **`bugfix/[name]`**: Bug fixes and patches

### **🤝 How to Contribute**

#### **1. Setup Development Environment**

```bash
# Clone and setup
git clone https://github.com/dohoudaniel/jechspace-frontend.git
cd jechspace-frontend
npm install

# Start backend (required)
cd ~/GitHub/JechSpace/FullStack/Jechspace-backend
python manage.py runserver 8000
```

#### **2. Create Feature Branch**

```bash
git checkout staging
git pull origin staging
git checkout -b feature/your-feature-name
```

#### **3. Development Best Practices**

```bash
# Make changes with descriptive commits
git add .
git commit -m "feat: add AI-powered booking recommendations"

# Push and create PR
git push origin feature/your-feature-name
```

#### **4. Code Quality Standards**

- ✅ **Functional Components**: Use React hooks and modern patterns
- ✅ **TypeScript Ready**: Prepared for TypeScript migration
- ✅ **Responsive Design**: Mobile-first Tailwind CSS approach
- ✅ **Accessibility**: WCAG-compliant components
- ✅ **Performance**: Lazy loading and code splitting
- ✅ **Testing**: Component and integration tests (planned)

### **📋 Pull Request Guidelines**

- **Clear Description**: Explain what and why
- **Screenshots**: Include UI changes visuals
- **Testing**: Verify functionality works
- **Documentation**: Update relevant docs
- **Review**: Request review from team members

## 🎯 **Feature Overview**

### **✅ Implemented Features**

#### **🔐 Authentication & User Management**

- ✅ **Multi-step Registration**: Individual and organization account types
- ✅ **Secure Login System**: JWT authentication with account lockout protection
- ✅ **Profile Management**: User profiles, avatar upload, preferences
- ✅ **Role-based Access**: Individual users, organization admins, system administrators
- ✅ **Password Security**: Strength validation, reset functionality

#### **📅 Smart Booking System**

- ✅ **Interactive Calendar**: FullCalendar.js with drag & drop booking
- ✅ **Real-time Availability**: Live space availability checking
- ✅ **Advanced Filtering**: Space type, capacity, amenities, location
- ✅ **Booking Management**: Create, edit, cancel, extend bookings
- ✅ **Conflict Detection**: Automatic overlap prevention
- ✅ **Business Hours**: Configurable working hours and availability

#### **🏢 Comprehensive Space Management**

- ✅ **Space CRUD Operations**: Create, read, update, delete spaces
- ✅ **Multiple Space Types**: Meeting rooms, hot desks, conference rooms, phone booths
- ✅ **Amenity Management**: Projectors, whiteboards, video conferencing equipment
- ✅ **Image Gallery**: Photo uploads and space visualization
- ✅ **Pricing Configuration**: Hourly rates and pricing tiers
- ✅ **Location Management**: Floor plans and specific location details

#### **🤖 AI-Powered Assistant**

- ✅ **Natural Language Processing**: Plain English booking requests
- ✅ **Voice Input Support**: Speech recognition for hands-free interaction
- ✅ **Smart Recommendations**: Personalized space suggestions
- ✅ **Context-Aware Responses**: Intelligent assistance based on user history
- ✅ **Quick Actions**: Pre-defined buttons for common tasks

#### **📊 Advanced Analytics & Reporting**

- ✅ **User Dashboard**: Personal booking statistics and usage patterns
- ✅ **Admin Analytics**: System overview, utilization rates, revenue tracking
- ✅ **Real-time Metrics**: Live data updates and refresh capabilities
- ✅ **Export Functionality**: PDF and CSV report generation
- ✅ **Usage Insights**: Peak hours analysis and space optimization

#### **🔔 Notification System**

- ✅ **Multi-channel Notifications**: Toast, email, push notifications
- ✅ **Booking Confirmations**: Automatic confirmation messages
- ✅ **Reminder System**: Upcoming booking alerts
- ✅ **Preference Management**: Customizable notification settings
- ✅ **Real-time Updates**: Live notification delivery

#### **🛠️ Technical Excellence**

- ✅ **Environment-Aware API**: Automatic localhost/production detection
- ✅ **Responsive Design**: Mobile-first, accessible interface
- ✅ **Performance Optimization**: Code splitting, lazy loading
- ✅ **Error Handling**: Comprehensive error management with retry logic
- ✅ **Security Features**: Input validation, HTTPS enforcement, CORS handling

### **🚧 In Development**

#### **📱 Mobile Applications**

- 🔄 **Native iOS App**: React Native implementation
- 🔄 **Native Android App**: React Native implementation
- 🔄 **Progressive Web App**: Enhanced mobile web experience

#### **🔗 Integrations**

- 🔄 **Calendar Sync**: Google Calendar and Outlook integration
- 🔄 **SSO Support**: Single sign-on with popular providers
- 🔄 **Payment Processing**: Online payment and billing system
- 🔄 **Video Conferencing**: Integrated meeting solutions

### **🔮 Planned Features**

#### **🏗️ Advanced Functionality**

- 📋 **Resource Management**: Equipment and catering booking
- 📋 **IoT Integration**: Smart building and sensor connectivity
- 📋 **Predictive Analytics**: AI-powered usage forecasting
- 📋 **Sustainability Tracking**: Environmental impact monitoring
- 📋 **Multi-language Support**: Internationalization and localization

#### **🌐 Enterprise Features**

- 📋 **Custom Branding**: Organization-specific theming
- 📋 **Advanced Reporting**: Custom report builder
- 📋 **API Marketplace**: Third-party service integrations
- 📋 **Workflow Automation**: Advanced booking workflows
- 📋 **Compliance Tools**: GDPR, SOC2, and security compliance

## 📁 **Project Structure**

```
jechspace-frontend/
├── 📁 src/
│   ├── 📁 components/          # Reusable UI components
│   │   ├── 📁 ui/             # Base UI components
│   │   ├── 📁 layout/         # Layout components
│   │   └── 📁 forms/          # Form components
│   ├── 📁 pages/              # Page components
│   │   ├── 📁 auth/           # Authentication pages
│   │   ├── 📁 dashboard/      # Dashboard pages
│   │   └── 📁 booking/        # Booking pages
│   ├── 📁 services/           # API services
│   │   ├── api.js             # Main API configuration
│   │   ├── authService.js     # Authentication service
│   │   ├── bookingService.js  # Booking management
│   │   └── spaceService.js    # Space management
│   ├── 📁 config/             # Configuration files
│   │   └── environment.js     # Environment configuration
│   ├── 📁 utils/              # Utility functions
│   │   └── apiUtils.js        # API utilities
│   ├── 📁 context/            # React context providers
│   ├── 📁 hooks/              # Custom React hooks
│   └── 📁 assets/             # Static assets
├── 📁 public/                 # Public assets
├── 📄 .env.example            # Environment template
├── 📄 .env.development        # Development config
├── 📄 .env.production         # Production config
├── 📄 PRODUCT_DOCS.md         # Complete product documentation
├── 📄 API_CONFIGURATION_GUIDE.md  # API setup guide
└── 📄 README.md               # This file
```

## 🔧 **Environment Configuration**

### **Automatic Environment Detection**

JechSpace automatically detects your environment and configures API endpoints:

- **Development**: `localhost`, `127.0.0.1`, `192.168.x.x` → `http://localhost:8000/api/v1/`
- **Production**: Any other hostname → `https://api-staging.jechspace.com/api/v1/`

### **Custom Configuration**

Override defaults with environment variables:

```bash
# .env.local
VITE_API_BASE_URL=http://localhost:8000/api/v1/
VITE_ENABLE_LOGGING=true
VITE_SESSION_TIMEOUT=3600000
```

### **Available Scripts**

```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run preview      # Preview production build
npm run lint         # Run ESLint
npm run lint:fix     # Fix ESLint issues
```

## 🐛 **Troubleshooting**

### **Common Issues**

#### **Backend Connection Issues**

```bash
❌ Network error - check your connection and API server status
```

**Solution**: Start the Django backend server

```bash
cd ~/GitHub/JechSpace/FullStack/Jechspace-backend
python manage.py runserver 8000
```

#### **Environment Detection Issues**

```bash
# Check current configuration in browser console
🚀 JechSpace API Configuration: { environment: "development", apiBaseURL: "..." }
```

#### **CORS Issues**

Configure Django CORS settings in the backend `settings.py`

### **Debug Mode**

Enable detailed logging:

```bash
# Add to .env.local
VITE_ENABLE_LOGGING=true
VITE_ENABLE_DEBUG_MODE=true
```

## 📚 **Documentation**

- **[Product Documentation](PRODUCT_DOCS.md)** - Complete feature overview and business details
- **[API Configuration Guide](API_CONFIGURATION_GUIDE.md)** - Detailed API setup and troubleshooting
- **[API Updates Summary](API_UPDATES_SUMMARY.md)** - Recent API configuration changes
- **[Figma Design](https://www.figma.com/proto/bqGkXQpCrUvzE71yYqyAjn/ALX-Hackathon--workspace-booking-system-?node-id=177-72)** - Interactive design prototype

## 🤝 **Contributing**

We welcome contributions! Please see our [Development Workflow](#-development-workflow) section for guidelines.

### **Code of Conduct**

- Be respectful and inclusive
- Follow coding standards and best practices
- Write clear commit messages
- Test your changes thoroughly
- Update documentation when needed

## 👥 **Team**

### **Core Development Team**

- **[Dohou Daniel](https://github.com/dohoudaniel)** - Lead Developer & Project Maintainer
- **Abayomi** - Frontend Developer & Product Designer
- **Pelumi** - Frontend Developer

### **Contributors**

We appreciate all contributors who help make JechSpace better! 🙏

## 🔗 **Related Projects**

- **[JechSpace Backend](https://github.com/dohoudaniel/jechspace-backend)** - Django REST API backend
- **[JechSpace Mobile](https://github.com/dohoudaniel/jechspace-mobile)** - React Native mobile apps (planned)

## 📞 **Support & Contact**

- **Issues**: [GitHub Issues](https://github.com/dohoudaniel/jechspace-frontend/issues)
- **Discussions**: [GitHub Discussions](https://github.com/dohoudaniel/jechspace-frontend/discussions)
- **Email**: <EMAIL>

## 📄 **License**

This project is licensed under the **MIT License** - see the [LICENSE](LICENSE) file for details.

## 🌟 **Acknowledgments**

- **React Team** for the amazing framework
- **Tailwind CSS** for the utility-first CSS framework
- **FullCalendar** for the interactive calendar component
- **Lucide** for the beautiful icon library
- **Vite** for the fast build tool
- **Open Source Community** for inspiration and tools

---

**Built with ❤️ by the JechSpace Team**

_JechSpace - Revolutionizing workspace management through intelligent technology and exceptional user experience._
