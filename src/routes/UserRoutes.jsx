// user routes configuration
import { Routes, Route } from "react-router-dom";

import BookingModal from "../components/dashboard/user/BookingModal";
import FindAndBookSpace from "../components/dashboard/user/FindAndBookSpace";

import UserDashboardLayout from "../components/dashboard/user/UserDashboardLayout";
import UserDashboardPage from "../components/dashboard/user/UserDashboardPage";

import AIAssistantPage from "../components/dashboard/user/AIAssistantPage";

import NotFound from "../pages/NotFound";

import "../App.css";

function UserRoutes() {
  return (
    <Routes>
      {/* route for user */}

      <Route path="dashboard" element={<UserDashboardLayout />}>
        <Route index element={<UserDashboardPage />} />
        <Route path="dashboardPage" element={<UserDashboardPage />} />
        <Route path="analytics" element={<BookingModal />} />

        <Route path="assistant" element={<AIAssistantPage />} />
        <Route path="findSpace" element={<FindAndBookSpace />} />
        <Route path="myBookings" element={<BookingModal />} />
      </Route>

      {/* 404 page when page is not found */}
      <Route path="*" element={<NotFound />} />
    </Routes>
  );
}
export default UserRoutes;
