# 🔧 API Configuration Guide - JechSpace Frontend

## 🌍 Environment-Aware API Configuration

The JechSpace frontend now supports automatic environment detection and configuration for seamless development and production deployment.

## 🎯 Supported Environments

### **Development Environment**
- **Detection**: `localhost`, `127.0.0.1`, or `192.168.x.x`
- **Default API URL**: `http://localhost:8000/api/v1/`
- **Backend Location**: `~/GitHub/JechSpace/FullStack/Jechspace-backend`
- **Features**: Enhanced logging, debug mode, extended session timeout

### **Production Environment**
- **Detection**: Any other hostname
- **Default API URL**: `https://api-staging.jechspace.com/api/v1/`
- **Features**: Optimized performance, reduced logging, security hardening

## 🔧 Configuration Methods

### **1. Automatic Detection (Recommended)**
The system automatically detects the environment based on hostname:

```javascript
// Automatically uses:
// - http://localhost:8000/api/v1/ for development
// - https://api-staging.jechspace.com/api/v1/ for production
```

### **2. Environment Variables**
Override default settings using `.env` files:

```bash
# .env.local (for local development)
VITE_API_BASE_URL=http://localhost:8000/api/v1/
VITE_ENVIRONMENT=development
VITE_ENABLE_LOGGING=true

# .env.production (for production builds)
VITE_API_BASE_URL=https://api-staging.jechspace.com/api/v1/
VITE_ENVIRONMENT=production
VITE_ENABLE_LOGGING=false
```

### **3. Custom Configuration**
For specific setups, modify `src/config/environment.js`:

```javascript
// Custom API endpoints
export const API_CONFIG = {
  development: {
    baseURL: 'http://your-custom-backend:8000/api/v1/',
    timeout: 30000,
  },
  production: {
    baseURL: 'https://your-production-api.com/api/v1/',
    timeout: 30000,
  }
};
```

## 🚀 Quick Setup

### **For Local Development**

1. **Start Backend Server**
   ```bash
   cd ~/GitHub/JechSpace/FullStack/Jechspace-backend
   python manage.py runserver 8000
   ```

2. **Start Frontend**
   ```bash
   cd ~/GitHub/JechSpace/FullStack/jechspace-frontend
   npm start
   ```

3. **Verify Configuration**
   - Open browser console
   - Look for: `🚀 JechSpace API Configuration`
   - Confirm API URL shows `http://localhost:8000/api/v1/`

### **For Production Deployment**

1. **Set Environment Variables**
   ```bash
   export VITE_API_BASE_URL=https://api-staging.jechspace.com/api/v1/
   export VITE_ENVIRONMENT=production
   ```

2. **Build Application**
   ```bash
   npm run build
   ```

3. **Deploy Built Files**
   - Upload `dist/` folder to your web server
   - Configure server to serve `index.html` for all routes

## 📋 Environment Variables Reference

### **Core Configuration**
```bash
# API Configuration
VITE_API_BASE_URL=http://localhost:8000/api/v1/
VITE_ENVIRONMENT=development
VITE_API_TIMEOUT=30000

# WebSocket Configuration
VITE_WS_URL=ws://localhost:8000/ws/
VITE_WS_RECONNECT_INTERVAL=5000
VITE_WS_MAX_RECONNECT_ATTEMPTS=5

# Feature Flags
VITE_ENABLE_LOGGING=true
VITE_ENABLE_DEBUG_MODE=true
VITE_ENABLE_MOCK_DATA=false
VITE_ENABLE_SERVICE_WORKER=false

# Application Settings
VITE_APP_NAME=JechSpace
VITE_APP_VERSION=2.0.0
VITE_SUPPORT_EMAIL=<EMAIL>

# Session Configuration (milliseconds)
VITE_SESSION_TIMEOUT=3600000
VITE_SESSION_WARNING_TIME=300000

# File Upload Settings
VITE_MAX_FILE_UPLOAD_SIZE=10485760
VITE_ALLOWED_IMAGE_TYPES=image/jpeg,image/png,image/gif,image/webp

# Pagination
VITE_DEFAULT_PAGE_SIZE=20
VITE_MAX_PAGE_SIZE=100
```

## 🔍 Debugging API Issues

### **Check Current Configuration**
Open browser console and look for:
```
🚀 JechSpace API Configuration: {
  environment: "development",
  hostname: "localhost",
  apiBaseURL: "http://localhost:8000/api/v1/",
  features: { enableLogging: true, ... }
}
```

### **Common Issues & Solutions**

#### **1. Backend Not Running**
```
❌ API Error: Network error - check your connection and API server status
```
**Solution**: Start the Django backend server
```bash
cd ~/GitHub/JechSpace/FullStack/Jechspace-backend
python manage.py runserver 8000
```

#### **2. Wrong API URL**
```
❌ API Error: 404 - Resource not found
```
**Solution**: Verify API URL in console logs or set custom URL:
```bash
export VITE_API_BASE_URL=http://localhost:8000/api/v1/
```

#### **3. CORS Issues**
```
❌ API Error: CORS policy blocked
```
**Solution**: Configure Django CORS settings in backend

#### **4. Authentication Errors**
```
❌ API Error: 401 - Authentication required
```
**Solution**: Check token storage and backend authentication setup

### **Enable Debug Logging**
```bash
# Add to .env.local
VITE_ENABLE_LOGGING=true
VITE_ENABLE_DEBUG_MODE=true
```

## 🔄 API Endpoint Mapping

All API calls now use centralized endpoint configuration:

```javascript
// Old way (hardcoded)
api.get('/auth/login/')

// New way (environment-aware)
api.get(getApiEndpointUrl('auth', 'login'))
```

### **Available Endpoints**
- **Authentication**: `auth.login`, `auth.register`, `auth.logout`
- **Users**: `users.profile`, `users.list`, `users.update`
- **Spaces**: `spaces.list`, `spaces.create`, `spaces.available`
- **Bookings**: `bookings.list`, `bookings.create`, `bookings.userBookings`
- **Analytics**: `analytics.dashboard`, `analytics.revenue`
- **Notifications**: `notifications.list`, `notifications.preferences`

## 🛠️ Advanced Configuration

### **Custom Backend Port**
```bash
# If your backend runs on a different port
VITE_API_BASE_URL=http://localhost:3001/api/v1/
```

### **Multiple API Services**
```bash
# Different services on different ports
VITE_AUTH_API_URL=http://localhost:8001/auth/
VITE_BOOKING_API_URL=http://localhost:8002/bookings/
```

### **Development with Remote Backend**
```bash
# Connect to remote development server
VITE_API_BASE_URL=https://dev-api.jechspace.com/api/v1/
```

## 🔐 Security Considerations

### **Environment Variables**
- Never commit `.env.local` files
- Use `.env.example` as template
- Sensitive data should be server-side only

### **API Keys & Tokens**
- JWT tokens are stored in localStorage
- Automatic token refresh on expiration
- Secure logout clears all stored data

### **HTTPS in Production**
- Always use HTTPS for production APIs
- Configure proper SSL certificates
- Enable HSTS headers

## 📊 Monitoring & Health Checks

### **Backend Health Check**
```javascript
// Automatic health check
const isHealthy = await checkServerHealth();
```

### **API Response Monitoring**
- Automatic retry on network failures
- Exponential backoff for rate limits
- Contextual error messages

## 🎯 Best Practices

1. **Use Environment Detection**: Let the system auto-detect environment
2. **Override When Needed**: Use environment variables for custom setups
3. **Monitor Console**: Check browser console for configuration logs
4. **Test Both Environments**: Verify functionality in dev and prod
5. **Handle Errors Gracefully**: Use provided error formatting utilities

## 📞 Troubleshooting Support

If you encounter issues:

1. **Check Console Logs**: Look for API configuration and error messages
2. **Verify Backend**: Ensure Django server is running and accessible
3. **Test Health Check**: Visit `http://localhost:8000/health/` directly
4. **Check Environment**: Confirm correct environment detection
5. **Review Network**: Use browser dev tools to inspect API calls

## 🎉 Success Indicators

When everything is working correctly, you should see:

✅ **Console Log**: `🚀 JechSpace API Configuration`  
✅ **API Calls**: `📤 API Request` and `📥 API Response` logs  
✅ **Authentication**: Successful login/logout  
✅ **Data Loading**: Dashboards and pages load without errors  
✅ **Real-time Updates**: Notifications and live data work  

The enhanced API configuration system ensures seamless development and production deployment with automatic environment detection and comprehensive error handling!
