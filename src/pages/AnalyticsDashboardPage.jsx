import React, { useState, useEffect } from 'react';
import { analyticsService } from '../services';
import { useToast } from '../components/common/NotificationContainer';
import { 
  TrendingUp, 
  TrendingDown, 
  Calendar, 
  Users, 
  MapPin, 
  DollarSign,
  Clock,
  BarChart3,
  <PERSON><PERSON><PERSON>,
  Download,
  Filter,
  RefreshCw
} from 'lucide-react';
import { InlineLoader, ButtonLoader } from '../components/common/LoadingSpinner';

const AnalyticsDashboardPage = () => {
  const toast = useToast();
  
  const [analytics, setAnalytics] = useState({
    overview: {
      totalBookings: 0,
      totalRevenue: 0,
      averageUtilization: 0,
      activeUsers: 0,
    },
    bookingTrends: [],
    spaceUtilization: [],
    peakHours: [],
    popularSpaces: [],
    userActivity: [],
    revenueBreakdown: [],
  });
  
  const [loading, setLoading] = useState(true);
  const [dateRange, setDateRange] = useState({
    start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    end: new Date().toISOString().split('T')[0],
  });
  const [selectedMetric, setSelectedMetric] = useState('bookings');
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadAnalytics();
  }, [dateRange]);

  const loadAnalytics = async () => {
    try {
      setLoading(true);
      
      const [
        dashboardData,
        bookingData,
        utilizationData,
        peakHoursData,
        popularSpacesData,
        userActivityData,
        revenueData,
      ] = await Promise.all([
        analyticsService.getDashboardAnalytics({
          start_date: dateRange.start,
          end_date: dateRange.end,
        }),
        analyticsService.getBookingAnalytics({
          start_date: dateRange.start,
          end_date: dateRange.end,
        }),
        analyticsService.getSpaceUtilization({
          start_date: dateRange.start,
          end_date: dateRange.end,
        }),
        analyticsService.getPeakHours({
          start_date: dateRange.start,
          end_date: dateRange.end,
        }),
        analyticsService.getPopularSpaces({
          start_date: dateRange.start,
          end_date: dateRange.end,
        }),
        analyticsService.getUserActivity({
          start_date: dateRange.start,
          end_date: dateRange.end,
        }),
        analyticsService.getRevenueAnalytics({
          start_date: dateRange.start,
          end_date: dateRange.end,
        }),
      ]);

      setAnalytics({
        overview: dashboardData.data,
        bookingTrends: bookingData.data.trends || [],
        spaceUtilization: utilizationData.data.spaces || [],
        peakHours: peakHoursData.data.hours || [],
        popularSpaces: popularSpacesData.data.spaces || [],
        userActivity: userActivityData.data.activity || [],
        revenueBreakdown: revenueData.data.breakdown || [],
      });
    } catch (error) {
      console.error('Failed to load analytics:', error);
      toast.error('Analytics Error', 'Failed to load analytics data');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadAnalytics();
    setRefreshing(false);
    toast.success('Data Refreshed', 'Analytics data has been updated');
  };

  const handleExport = async () => {
    try {
      const blob = await analyticsService.exportReport({
        start_date: dateRange.start,
        end_date: dateRange.end,
        format: 'pdf',
      });
      
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `analytics-report-${dateRange.start}-to-${dateRange.end}.pdf`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      
      toast.success('Export Complete', 'Report has been downloaded');
    } catch (error) {
      toast.error('Export Failed', 'Failed to export report');
    }
  };

  const MetricCard = ({ title, value, change, icon: Icon, color = 'blue' }) => {
    const colorClasses = {
      blue: 'bg-blue-50 text-blue-600 border-blue-200',
      green: 'bg-green-50 text-green-600 border-green-200',
      yellow: 'bg-yellow-50 text-yellow-600 border-yellow-200',
      red: 'bg-red-50 text-red-600 border-red-200',
    };

    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600">{title}</p>
            <p className="text-2xl font-bold text-gray-900 mt-1">
              {loading ? <InlineLoader size="sm" text="" /> : value}
            </p>
            {change !== undefined && (
              <div className="flex items-center mt-1">
                {change >= 0 ? (
                  <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
                ) : (
                  <TrendingDown className="h-4 w-4 text-red-500 mr-1" />
                )}
                <span className={`text-sm ${change >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {Math.abs(change)}% from last period
                </span>
              </div>
            )}
          </div>
          <div className={`p-3 rounded-lg border ${colorClasses[color]}`}>
            <Icon className="h-6 w-6" />
          </div>
        </div>
      </div>
    );
  };

  const ChartContainer = ({ title, children, className = '' }) => (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-6 ${className}`}>
      <h3 className="text-lg font-semibold text-gray-900 mb-4">{title}</h3>
      {children}
    </div>
  );

  const SimpleBarChart = ({ data, xKey, yKey, title }) => (
    <div className="space-y-3">
      {data.slice(0, 10).map((item, index) => (
        <div key={index} className="flex items-center justify-between">
          <span className="text-sm text-gray-600 truncate flex-1 mr-4">
            {item[xKey]}
          </span>
          <div className="flex items-center flex-1">
            <div className="w-full bg-gray-200 rounded-full h-2 mr-3">
              <div
                className="bg-blue-600 h-2 rounded-full"
                style={{
                  width: `${(item[yKey] / Math.max(...data.map(d => d[yKey]))) * 100}%`
                }}
              />
            </div>
            <span className="text-sm font-medium text-gray-900 min-w-[3rem] text-right">
              {item[yKey]}
            </span>
          </div>
        </div>
      ))}
    </div>
  );

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Analytics Dashboard</h1>
          <p className="text-gray-600 mt-1">
            Insights and metrics for your workspace usage
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <ButtonLoader
            loading={refreshing}
            onClick={handleRefresh}
            className="flex items-center px-3 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </ButtonLoader>
          <button
            onClick={handleExport}
            className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            <Download className="h-4 w-4 mr-2" />
            Export Report
          </button>
        </div>
      </div>

      {/* Date Range Selector */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
        <div className="flex items-center space-x-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              From
            </label>
            <input
              type="date"
              value={dateRange.start}
              onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}
              className="border border-gray-300 rounded-md px-3 py-2 text-sm"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              To
            </label>
            <input
              type="date"
              value={dateRange.end}
              onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}
              className="border border-gray-300 rounded-md px-3 py-2 text-sm"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Metric
            </label>
            <select
              value={selectedMetric}
              onChange={(e) => setSelectedMetric(e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 text-sm"
            >
              <option value="bookings">Bookings</option>
              <option value="revenue">Revenue</option>
              <option value="utilization">Utilization</option>
              <option value="users">Users</option>
            </select>
          </div>
        </div>
      </div>

      {/* Overview Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <MetricCard
          title="Total Bookings"
          value={analytics.overview.totalBookings}
          change={12}
          icon={Calendar}
          color="blue"
        />
        <MetricCard
          title="Total Revenue"
          value={`$${analytics.overview.totalRevenue?.toLocaleString() || 0}`}
          change={8}
          icon={DollarSign}
          color="green"
        />
        <MetricCard
          title="Avg Utilization"
          value={`${analytics.overview.averageUtilization || 0}%`}
          change={-2}
          icon={TrendingUp}
          color="yellow"
        />
        <MetricCard
          title="Active Users"
          value={analytics.overview.activeUsers}
          change={15}
          icon={Users}
          color="blue"
        />
      </div>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <ChartContainer title="Popular Spaces">
          {loading ? (
            <InlineLoader />
          ) : analytics.popularSpaces.length > 0 ? (
            <SimpleBarChart
              data={analytics.popularSpaces}
              xKey="name"
              yKey="booking_count"
              title="Bookings"
            />
          ) : (
            <div className="text-center py-8 text-gray-500">
              No data available
            </div>
          )}
        </ChartContainer>

        <ChartContainer title="Space Utilization">
          {loading ? (
            <InlineLoader />
          ) : analytics.spaceUtilization.length > 0 ? (
            <SimpleBarChart
              data={analytics.spaceUtilization}
              xKey="name"
              yKey="utilization_rate"
              title="Utilization %"
            />
          ) : (
            <div className="text-center py-8 text-gray-500">
              No data available
            </div>
          )}
        </ChartContainer>
      </div>

      {/* Peak Hours and User Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <ChartContainer title="Peak Hours">
          {loading ? (
            <InlineLoader />
          ) : analytics.peakHours.length > 0 ? (
            <div className="space-y-3">
              {analytics.peakHours.map((hour, index) => (
                <div key={index} className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">
                    {hour.hour}:00 - {hour.hour + 1}:00
                  </span>
                  <div className="flex items-center">
                    <div className="w-32 bg-gray-200 rounded-full h-2 mr-3">
                      <div
                        className="bg-blue-600 h-2 rounded-full"
                        style={{
                          width: `${(hour.booking_count / Math.max(...analytics.peakHours.map(h => h.booking_count))) * 100}%`
                        }}
                      />
                    </div>
                    <span className="text-sm font-medium text-gray-900">
                      {hour.booking_count}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              No data available
            </div>
          )}
        </ChartContainer>

        <ChartContainer title="Revenue Breakdown">
          {loading ? (
            <InlineLoader />
          ) : analytics.revenueBreakdown.length > 0 ? (
            <SimpleBarChart
              data={analytics.revenueBreakdown}
              xKey="space_type"
              yKey="revenue"
              title="Revenue ($)"
            />
          ) : (
            <div className="text-center py-8 text-gray-500">
              No data available
            </div>
          )}
        </ChartContainer>
      </div>
    </div>
  );
};

export default AnalyticsDashboardPage;
