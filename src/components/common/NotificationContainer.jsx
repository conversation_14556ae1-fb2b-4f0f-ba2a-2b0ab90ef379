import React, { useEffect } from 'react';
import { useApp } from '../../context/AppContext';
import { X, CheckCircle, AlertCircle, Info, AlertTriangle } from 'lucide-react';

const NotificationContainer = () => {
  const { state, actions } = useApp();

  const getNotificationIcon = (type) => {
    switch (type) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-400" />;
      case 'error':
        return <AlertCircle className="h-5 w-5 text-red-400" />;
      case 'warning':
        return <AlertTriangle className="h-5 w-5 text-yellow-400" />;
      case 'info':
      default:
        return <Info className="h-5 w-5 text-blue-400" />;
    }
  };

  const getNotificationStyles = (type) => {
    switch (type) {
      case 'success':
        return 'bg-green-50 border-green-200';
      case 'error':
        return 'bg-red-50 border-red-200';
      case 'warning':
        return 'bg-yellow-50 border-yellow-200';
      case 'info':
      default:
        return 'bg-blue-50 border-blue-200';
    }
  };

  const handleRemoveNotification = (id) => {
    actions.removeNotification(id);
  };

  // Auto-remove notifications after 5 seconds
  useEffect(() => {
    const timers = state.notifications.map((notification) => {
      if (notification.autoRemove !== false) {
        return setTimeout(() => {
          handleRemoveNotification(notification.id);
        }, notification.duration || 5000);
      }
      return null;
    });

    return () => {
      timers.forEach((timer) => {
        if (timer) clearTimeout(timer);
      });
    };
  }, [state.notifications]);

  if (state.notifications.length === 0) {
    return null;
  }

  return (
    <div className="fixed top-4 right-4 z-50 space-y-2 max-w-sm w-full">
      {state.notifications.map((notification) => (
        <div
          key={notification.id}
          className={`
            ${getNotificationStyles(notification.type)}
            border rounded-lg shadow-lg p-4 transition-all duration-300 ease-in-out
            transform translate-x-0 opacity-100
          `}
        >
          <div className="flex items-start">
            <div className="flex-shrink-0">
              {getNotificationIcon(notification.type)}
            </div>
            <div className="ml-3 flex-1">
              {notification.title && (
                <h4 className="text-sm font-medium text-gray-900">
                  {notification.title}
                </h4>
              )}
              <p className="text-sm text-gray-700 mt-1">
                {notification.message}
              </p>
              {notification.action && (
                <div className="mt-2">
                  <button
                    onClick={notification.action.onClick}
                    className="text-sm font-medium text-blue-600 hover:text-blue-500"
                  >
                    {notification.action.label}
                  </button>
                </div>
              )}
            </div>
            <div className="ml-4 flex-shrink-0">
              <button
                onClick={() => handleRemoveNotification(notification.id)}
                className="inline-flex text-gray-400 hover:text-gray-600 focus:outline-none focus:text-gray-600"
              >
                <X className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

// Toast notification helper function
export const showToast = (type, title, message, options = {}) => {
  const notification = {
    id: Date.now() + Math.random(),
    type,
    title,
    message,
    timestamp: new Date().toISOString(),
    ...options,
  };

  // This would typically be called from a context or hook
  // For now, we'll create a custom event
  window.dispatchEvent(
    new CustomEvent('showToast', { detail: notification })
  );
};

// Hook for showing toast notifications
export const useToast = () => {
  const { actions } = useApp();

  return {
    success: (title, message, options) => {
      actions.addNotification({
        id: Date.now() + Math.random(),
        type: 'success',
        title,
        message,
        timestamp: new Date().toISOString(),
        ...options,
      });
    },
    error: (title, message, options) => {
      actions.addNotification({
        id: Date.now() + Math.random(),
        type: 'error',
        title,
        message,
        timestamp: new Date().toISOString(),
        ...options,
      });
    },
    warning: (title, message, options) => {
      actions.addNotification({
        id: Date.now() + Math.random(),
        type: 'warning',
        title,
        message,
        timestamp: new Date().toISOString(),
        ...options,
      });
    },
    info: (title, message, options) => {
      actions.addNotification({
        id: Date.now() + Math.random(),
        type: 'info',
        title,
        message,
        timestamp: new Date().toISOString(),
        ...options,
      });
    },
  };
};

export default NotificationContainer;
