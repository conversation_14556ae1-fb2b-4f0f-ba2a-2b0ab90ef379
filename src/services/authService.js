import axios from "axios";
import api from "./api";

/**
 * Authentication service for handling user authentication operations
 */
const authService = {
  /**
   * Login user with email and password
   * @param {Object} credentials - User credentials
   * @param {string} credentials.email - User email
   * @param {string} credentials.password - User password
   * @returns {Promise} - Response from API
   */
  login: async (credentials) => {
    try {
      console.log("Login attempt with email:", credentials.email);

      // Try multiple approaches to handle potential API route differences
      let response;
      try {
        // First approach: Try the standard signin route
        response = await api.post("auth/signin/", credentials);
      } catch (err) {
        console.log("First signin approach failed, trying alternative routes...");
        
        try {
          // Second approach: Try login route
          response = await api.post("auth/signin/", credentials);
        } catch (err2) {
          console.log("Second approach failed, trying direct URL...");
          
          // Third approach: Try with full URL
          response = await axios.post(
            "https://api-staging.jechspace.com/api/v1/auth/signin/",
            credentials,
            {
              headers: {
                "Content-Type": "application/json",
              },
            }
          );
        }
      }

      console.log("Login response:", response.data);

      // Handle different response formats
      if (
        response.data.data &&
        response.data.data.auth &&
        response.data.data.auth.access_token
      ) {
        localStorage.setItem("authToken", response.data.data.auth.access_token);
      } else if (response.data.token) {
        localStorage.setItem("authToken", response.data.token);
      } else if (response.data.access_token) {
        localStorage.setItem("authToken", response.data.access_token);
      } else if (response.data.data && response.data.data.token) {
        localStorage.setItem("authToken", response.data.data.token);
      }
      
      return response.data;
    } catch (error) {
      console.error(
        "Login error details:",
        error.response ? error.response.data : error
      );
      throw error.response ? error.response.data : error;
    }
  },

  /**
   * Register individual user
   * @param {Object} userData - User registration data
   * @returns {Promise} - Response from API
   */
  // Helper function to generate a unique email
  _generateUniqueEmail: (baseEmail) => {
    const timestamp = new Date().getTime();
    const randomNum = Math.floor(Math.random() * 1000);
    const [username, domain] = baseEmail.split("@");
    return `${username}+${timestamp}${randomNum}@${domain}`;
  },

  registerIndividual: async (userData) => {
    try {
      // Create a request with all required fields based on backend validation
      const requestData = {
        email: userData.email,
        password: userData.password,
        confirm_password: userData.password,
        first_name: userData.first_name || userData.firstName || "User",
        last_name: userData.last_name || userData.lastName || "Name",
        profession: userData.profession || userData.workType || "Professional",
        phone_number: userData.phone_number || "",
        user_type: "individual",
      };

      // Ensure password meets complexity requirements (at least 8 characters)
      if (requestData.password.length < 8) {
        requestData.password = "Password123!"; // Default strong password
        requestData.confirm_password = requestData.password;
      }

      console.log("Sending registration data:", requestData);

      // Try different approaches to find what works
      let response;
      try {
        // Approach 1: With query parameter
        response = await api.post("auth/signup/?type=individual", requestData);
      } catch (err) {
        console.log("First approach failed, trying alternative...");

        // Check if the error is due to email already existing
        if (
          err.response &&
          err.response.data &&
          err.response.data.errors &&
          err.response.data.errors.email &&
          err.response.data.errors.email.code === "unique"
        ) {
          console.log("Email already exists, trying with a unique email...");

          // Generate a unique email and try again
          const uniqueEmail = authService._generateUniqueEmail(
            requestData.email
          );
          requestData.email = uniqueEmail;

          try {
            response = await api.post(
              "auth/signup/?type=individual",
              requestData
            );
          } catch (uniqueEmailErr) {
            console.log(
              "Unique email approach failed, trying without query parameter..."
            );
            try {
              // Approach 2: Without query parameter
              response = await api.post("auth/signup/", requestData);
            } catch (err2) {
              console.log(
                "Second approach failed, trying with different content type..."
              );
              // Approach 3: With different content type
              const formData = new FormData();
              Object.entries(requestData).forEach(([key, value]) => {
                formData.append(key, value);
              });

              const signupUrl = process.env.SIGNUP_URL;
              response = await axios.post(
                signupUrl,
                formData,
                {
                  headers: {
                    "Content-Type": "multipart/form-data",
                  },
                }
              );
            }
          }
        } else {
          try {
            // Approach 2: Without query parameter
            response = await api.post("auth/signup/", requestData);
          } catch (err2) {
            console.log(
              "Second approach failed, trying with different content type..."
            );
            // Approach 3: With different content type
            const formData = new FormData();
            Object.entries(requestData).forEach(([key, value]) => {
              formData.append(key, value);
            });

            response = await axios.post(
              "https://api-staging.jechspace.com/api/v1/auth/signup/",
              formData,
              {
                headers: {
                  "Content-Type": "multipart/form-data",
                },
              }
            );
          }
        }
      }

      console.log("Registration response:", response.data);

      if (response.data.data && response.data.data.token) {
        localStorage.setItem("authToken", response.data.data.token);
      }
      return response.data;
    } catch (error) {
      console.error(
        "Registration error details:",
        error.response ? error.response.data : error
      );
      throw error.response ? error.response.data : error;
    }
  },

  /**
   * Register organization user
   * @param {Object} userData - Organization registration data
   * @returns {Promise} - Response from API
   */
  registerOrganization: async (userData) => {
    try {
      // Create a request with all required fields based on backend validation
      const requestData = {
        email: userData.email,
        password: userData.password,
        confirm_password: userData.password,
        first_name: userData.first_name || userData.firstName || "Organization",
        last_name: userData.last_name || userData.lastName || "Admin",
        profession: userData.profession || "Business Manager",
        phone_number: userData.phone_number || "",
        user_type: "organization_owner", // Must match the exact value expected by the backend
      };

      // Ensure password meets complexity requirements (at least 8 characters)
      if (requestData.password.length < 8) {
        requestData.password = "Password123!"; // Default strong password
        requestData.confirm_password = requestData.password;
      }

      console.log("Sending organization registration data:", requestData);

      // Try different approaches to find what works
      let response;
      try {
        // Approach 1: With query parameter
        response = await api.post(
          "auth/signup/?type=organization",
          requestData
        );
      } catch (err) {
        console.log("First approach failed, trying alternative...");

        // Check if the error is due to email already existing
        if (
          err.response &&
          err.response.data &&
          err.response.data.errors &&
          err.response.data.errors.email &&
          err.response.data.errors.email.code === "unique"
        ) {
          console.log("Email already exists, trying with a unique email...");

          // Generate a unique email and try again
          const uniqueEmail = authService._generateUniqueEmail(
            requestData.email
          );
          requestData.email = uniqueEmail;

          try {
            response = await api.post(
              "auth/signup/?type=organization",
              requestData
            );
          } catch (uniqueEmailErr) {
            console.log(
              "Unique email approach failed, trying without query parameter..."
            );
            try {
              // Approach 2: Without query parameter
              response = await api.post("auth/signup/", requestData);
            } catch (err2) {
              console.log(
                "Second approach failed, trying with different content type..."
              );
              // Approach 3: With different content type
              const formData = new FormData();
              Object.entries(requestData).forEach(([key, value]) => {
                formData.append(key, value);
              });

              response = await axios.post(
                "https://api-staging.jechspace.com/api/v1/auth/signup/",
                formData,
                {
                  headers: {
                    "Content-Type": "multipart/form-data",
                  },
                }
              );
            }
          }
        } else {
          try {
            // Approach 2: Without query parameter
            response = await api.post("auth/signup/", requestData);
          } catch (err2) {
            console.log(
              "Second approach failed, trying with different content type..."
            );
            // Approach 3: With different content type
            const formData = new FormData();
            Object.entries(requestData).forEach(([key, value]) => {
              formData.append(key, value);
            });

            response = await axios.post(
              "https://api-staging.jechspace.com/api/v1/auth/signup/",
              formData,
              {
                headers: {
                  "Content-Type": "multipart/form-data",
                },
              }
            );
          }
        }
      }

      console.log("Organization registration response:", response.data);

      if (response.data.data && response.data.data.token) {
        localStorage.setItem("authToken", response.data.data.token);
      }
      return response.data;
    } catch (error) {
      console.error(
        "Organization registration error details:",
        error.response ? error.response.data : error
      );
      throw error.response ? error.response.data : error;
    }
  },

  /**
   * Logout user
   * @returns {Promise} - Response from API
   */
  logout: async () => {
    try {
      console.log("Logging out user");

      const response = await api.post("auth/signout/");
      console.log("Logout response:", response.data);

      localStorage.removeItem("authToken");
      return response.data;
    } catch (error) {
      console.error(
        "Logout error details:",
        error.response ? error.response.data : error
      );
      // Still remove token even if API call fails
      localStorage.removeItem("authToken");
      throw error.response ? error.response.data : error;
    }
  },

  /**
   * Request password reset
   * @param {Object} data - Password reset data
   * @param {string} data.email - User email
   * @returns {Promise} - Response from API
   */
  requestPasswordReset: async (data) => {
    try {
      const response = await api.post("/auth/password/reset/", data);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  /**
   * Verify email
   * @param {Object} data - Verification data
   * @param {string} data.token - Verification token
   * @returns {Promise} - Response from API
   */
  verifyEmail: async (data) => {
    try {
      const response = await api.post("/auth/email/verification/", data);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  /**
   * Check if user is authenticated
   * @returns {boolean} - True if user is authenticated
   */
  isAuthenticated: () => {
    return !!localStorage.getItem("authToken");
  },
};

export default authService;
