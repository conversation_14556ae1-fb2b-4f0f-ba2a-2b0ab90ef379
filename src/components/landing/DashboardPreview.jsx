import { motion } from "framer-motion";

const DashboardPreview = () => {
  return (
    <section className="py-32 relative overflow-hidden">
      {/* Background decorations */}
      <div className="absolute w-[500px] h-[500px] rounded-full bg-gradient-to-br from-primary-50 to-secondary-50 filter blur-[50px] top-[-200px] right-[-100px] z-0"></div>
      <div className="absolute w-[500px] h-[500px] rounded-full bg-gradient-to-br from-primary-50 to-secondary-50 filter blur-[50px] bottom-[-200px] left-[-100px] z-0"></div>

      <div className="container mx-auto px-4">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl font-bold mb-4">Powerful Admin Dashboard</h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Get a complete overview of your workspace with our intuitive and
            feature-rich admin dashboard designed for maximum efficiency.
          </p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          viewport={{ once: true }}
          className="relative z-10"
        >
          <motion.img
            src="/images/DashboardPreview.png"
            alt="JechSpace Dashboard"
            className="w-full rounded-xl shadow-2xl transform perspective-1000 rotateX-5 hover:rotateX-0 transition-all duration-500"
            whileHover={{
              rotateX: 0,
              boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25)",
            }}
          />
        </motion.div>
      </div>
    </section>
  );
};

export default DashboardPreview;
