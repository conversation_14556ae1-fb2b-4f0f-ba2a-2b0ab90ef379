// src/components/dashboard/BookingsList.jsx
const upcomingBookings = () => {
  const bookings = [
    { id: 1, room: "Meeting Room A", time: "Today, 09:00 - 14:00" },
    { id: 2, room: "Meeting Room A", time: "Today, 09:00 - 14:00" },
    { id: 3, room: "Meeting Room A", time: "Today, 09:00 - 14:00" },
  ];

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm">
      <h2 className="text-lg font-medium mb-4">Quick actions</h2>
      <div className="space-y-4">
        {bookings.map((booking) => (
          <div key={booking.id} className="flex justify-between items-center">
            <div>
              <h3 className="font-medium">{booking.room}</h3>
              <p className="text-gray-500 text-sm">{booking.time}</p>
            </div>
            <button className="bg-white text-blue-600 px-4 py-1 rounded-lg border border-blue-200 hover:bg-blue-50">
              Modify
            </button>
          </div>
        ))}
      </div>
    </div>
  );
};

export default upcomingBookings;
