import React from "react";
import "./App.css";
import AppRoutes from "./routes/AppRoutes";
import { AppProvider } from "./context/AppContext";
import { BookingProvider } from "./context/BookingContext";
import { SpaceProvider } from "./context/SpaceContext";
import ErrorBoundary from "./components/common/ErrorBoundary";
import LoadingSpinner from "./components/common/LoadingSpinner";
import NotificationContainer from "./components/common/NotificationContainer";

function App() {
  return (
    <ErrorBoundary>
      <AppProvider>
        <SpaceProvider>
          <BookingProvider>
            <div className="App">
              <AppRoutes />
              <NotificationContainer />
              <LoadingSpinner />
            </div>
          </BookingProvider>
        </SpaceProvider>
      </AppProvider>
    </ErrorBoundary>
  );
}

export default App;
