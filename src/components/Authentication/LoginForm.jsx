import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { authService } from "../../services";

const LoginForm = () => {
  const navigate = useNavigate();
  const [userType, setUserType] = useState("individual");
  const [formData, setFormData] = useState({
    email: "",
    password: "",
    rememberMe: false,
  });
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === "checkbox" ? checked : value,
    });
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.email) {
      newErrors.email = "Email is required";
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = "Email is invalid";
    }

    if (!formData.password) {
      newErrors.password = "Password is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (validateForm()) {
      try {
        setIsSubmitting(true);

        const credentials = {
          email: formData.email,
          password: formData.password,
        };

        const data = await authService.login(credentials);
        console.log("Login successful:", data);

        // Redirect based on user type
        if (userType === "individual") {
          navigate("/dashboard");
        } else {
          navigate("/admin/dashboard");
        }
      } catch (error) {
        console.error("Login error:", error);
        alert(error.message || "An error occurred during login");
      } finally {
        setIsSubmitting(false);
      }
    }
  };

  return (
    <div className="flex min-h-screen w-full">
      {/* Left side - Login Form */}
      <div className="w-full md:w-1/2 p-8 flex flex-col overflow-y-auto">
        <div className="mb-6">
          {/* Logo Image */}
          <img
            src="/images/logo-blue.png"
            alt="JechSpace Logo"
            className="h-8"
          />
        </div>

        <div className="max-w-md mx-auto w-full pb-8">
          <h1 className="text-2xl font-semibold mb-6">
            Log in to <span className="text-blue-500">JechSpace</span>
          </h1>

          {/* Toggle Selection - Similar to signup toggle */}
          <div className="mb-6">
            <div className="flex p-1 bg-gray-100 rounded-full">
              <button
                type="button"
                className={`flex-1 py-3 px-4 rounded-full transition-all duration-300 ${
                  userType === "individual"
                    ? "bg-blue-500 text-white shadow-md"
                    : "text-gray-700 hover:bg-gray-200"
                }`}
                onClick={() => setUserType("individual")}
              >
                Individual
              </button>
              <button
                type="button"
                className={`flex-1 py-3 px-4 rounded-full transition-all duration-300 ${
                  userType === "organization"
                    ? "bg-blue-500 text-white shadow-md"
                    : "text-gray-700 hover:bg-gray-200"
                }`}
                onClick={() => setUserType("organization")}
              >
                Organization
              </button>
            </div>
          </div>

          <form onSubmit={handleSubmit}>
            <div className="mb-5">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {userType === "individual" ? "Email" : "Work Email"}
              </label>
              <input
                type="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                className={`w-full px-3 py-2 bg-gray-100 rounded border ${
                  errors.email ? "border-red-500" : "border-gray-200"
                }`}
                placeholder={
                  userType === "individual"
                    ? "<EMAIL>"
                    : "<EMAIL>"
                }
              />
              {errors.email && (
                <p className="text-red-500 text-xs mt-1">{errors.email}</p>
              )}
            </div>

            <div className="mb-5">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Password
              </label>
              <input
                type="password"
                name="password"
                value={formData.password}
                onChange={handleChange}
                className={`w-full px-3 py-2 bg-gray-100 rounded border ${
                  errors.password ? "border-red-500" : "border-gray-200"
                }`}
                placeholder="••••••••••"
              />
              {errors.password && (
                <p className="text-red-500 text-xs mt-1">{errors.password}</p>
              )}
            </div>

            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="rememberMe"
                  name="rememberMe"
                  checked={formData.rememberMe}
                  onChange={handleChange}
                  className="mr-2"
                />
                <label htmlFor="rememberMe" className="text-sm text-gray-700">
                  Remember me
                </label>
              </div>
              <a href="#" className="text-sm text-blue-500 hover:text-blue-700">
                Forgot password?
              </a>
            </div>

            <button
              type="submit"
              disabled={isSubmitting}
              className={`w-full bg-blue-500 hover:bg-blue-600 text-white py-3 rounded-lg font-medium mb-4 transition-all flex items-center justify-center ${
                isSubmitting ? "opacity-70 cursor-not-allowed" : ""
              }`}
            >
              {isSubmitting
                ? "Logging in..."
                : `Log in as ${
                    userType === "individual" ? "Individual" : "Organization"
                  }`}
            </button>

            <div className="relative flex items-center justify-center mb-6">
              <hr className="w-full border-gray-300" />
              <span className="absolute bg-white px-4 text-sm text-gray-500">
                Or
              </span>
            </div>

            <button
              type="button"
              className="w-full border text-gray-700 border-gray-300 py-2.5 rounded-lg flex items-center justify-center font-medium mb-6 hover:bg-gray-50 transition-all"
            >
              <svg viewBox="0 0 24 24" width="18" height="18" className="mr-2">
                <path
                  fill="#4285F4"
                  d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                />
                <path
                  fill="#34A853"
                  d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                />
                <path
                  fill="#FBBC05"
                  d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                />
                <path
                  fill="#EA4335"
                  d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                />
              </svg>
              Continue with Google
            </button>

            <div className="text-center text-sm">
              <span className="text-gray-600">Don't have an account? </span>
              <a href="/signup" className="text-blue-500 hover:text-blue-700">
                Sign up
              </a>
            </div>
          </form>
        </div>
      </div>

      {/* Right side - Blue background and illustration */}
      <div className="hidden md:flex md:w-1/2 bg-blue-500 text-white flex-col items-center justify-center p-8 sticky top-0 h-screen">
        <div className="max-w-md">
          <h2 className="text-2xl font-semibold mb-4">
            {userType === "individual"
              ? "Welcome Back to Your Workspace"
              : "Welcome Back to Your Organization"}
          </h2>
          <p className="mb-10 text-white">
            {userType === "individual"
              ? "Access your bookings, find available spaces, and make the most of your productive day."
              : "Manage your organization's workspace, track team bookings, and optimize your office utilization."}
          </p>

          {/* Illustration - Different based on userType */}
          <div className="flex justify-center">
            <img
              src={
                userType === "individual"
                  ? "/images/individual-login.png"
                  : "/images/org-login.png"
              }
              alt="Workspace Illustration"
              className="w-full rounded-lg shadow-sm brightness-110"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginForm;
