import { useState } from "react";
import { motion } from "framer-motion";
import { Link } from "react-router-dom";

const AIAssistantPage = () => {
  const [activeTab, setActiveTab] = useState("general");

  const features = [
    {
      id: "booking",
      icon: "fa-calendar-check",
      title: "Booking Assistant",
      description:
        "Get help finding and booking the perfect workspace for your needs",
    },
    {
      id: "navigation",
      icon: "fa-map-marker-alt",
      title: "Navigation Guide",
      description:
        "Find your way around the building and locate amenities with ease",
    },
    {
      id: "support",
      icon: "fa-life-ring",
      title: "24/7 Support",
      description:
        "Get instant answers to your questions and resolve issues quickly",
    },
    {
      id: "insights",
      icon: "fa-lightbulb",
      title: "Smart Suggestions",
      description:
        "Receive personalized workspace recommendations based on your preferences",
    },
  ];

  const tabContent = {
    general: {
      title: "Your Personal Workspace Assistant",
      description:
        "WorkSpace Assistant uses AI to help you make the most of your office experience. From booking desks to finding meeting rooms, our assistant is here to help you every step of the way.",
    },
    capabilities: {
      title: "What I Can Do",
      description:
        "I can help you book workspaces, check availability, find amenities, navigate the building, answer FAQs about policies, and connect you with human support when needed.",
    },
    privacy: {
      title: "Your Privacy Matters",
      description:
        "WorkSpace Assistant respects your privacy. Your conversations are processed securely and not stored longer than necessary. You can delete your chat history at any time.",
    },
  };

  return (
    <div className="min-h-screen bg-gray-50 pt-20 pb-12">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold mb-2">AI Workspace Assistant</h1>
            <p className="text-gray-600">
              Your intelligent guide to a better workplace experience
            </p>
          </div>
          <Link
            to="/user/dashboard"
            className="flex items-center gap-2 text-primary hover:underline"
          >
            <i className="fas fa-arrow-left"></i>
            Back to Dashboard
          </Link>
        </div>

        {/* Main content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left column */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-xl shadow-md overflow-hidden mb-8">
              {/* Tabs */}
              <div className="flex border-b">
                {["general", "capabilities", "privacy"].map((tab) => (
                  <button
                    key={tab}
                    onClick={() => setActiveTab(tab)}
                    className={`px-6 py-4 text-sm font-medium ${
                      activeTab === tab
                        ? "text-primary border-b-2 border-primary"
                        : "text-gray-500 hover:text-gray-700"
                    }`}
                  >
                    {tab.charAt(0).toUpperCase() + tab.slice(1)}
                  </button>
                ))}
              </div>

              {/* Active tab content */}
              <div className="p-6">
                <div className="flex flex-col md:flex-row gap-8 items-center">
                  <div className="md:w-1/2">
                    <h2 className="text-2xl font-bold mb-4">
                      {tabContent[activeTab].title}
                    </h2>
                    <p className="text-gray-600 mb-6">
                      {tabContent[activeTab].description}
                    </p>
                    <button className="bg-primary text-white px-6 py-3 rounded-lg hover:bg-primary-dark transition-colors">
                      Try Assistant Now
                    </button>
                  </div>
                  <div className="md:w-1/2">
                    <img
                      src={tabContent[activeTab].image}
                      alt={tabContent[activeTab].title}
                      className="rounded-lg shadow-md w-full"
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Features grid */}
            <h2 className="text-2xl font-bold mb-6">
              How WorkSpace Assistant Helps You
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
              {features.map((feature) => (
                <motion.div
                  key={feature.id}
                  whileHover={{ y: -5 }}
                  className="bg-white p-6 rounded-xl shadow-md"
                >
                  <div
                    className={`w-12 h-12 rounded-lg bg-primary-50 flex items-center justify-center text-primary mb-4`}
                  >
                    <i className={`fas ${feature.icon} text-xl`}></i>
                  </div>
                  <h3 className="text-lg font-semibold mb-2">
                    {feature.title}
                  </h3>
                  <p className="text-gray-600">{feature.description}</p>
                </motion.div>
              ))}
            </div>

            {/* Recent questions */}
            <div className="bg-white rounded-xl shadow-md p-6">
              <h2 className="text-xl font-bold mb-4">
                Frequently Asked Questions
              </h2>
              <div className="space-y-4">
                {[
                  {
                    q: "How do I book a desk for tomorrow?",
                    a: "You can book a desk by going to the Booking section, selecting tomorrow's date, and choosing from available spaces.",
                  },
                  {
                    q: "Can I cancel my reservation?",
                    a: "Yes, you can cancel your reservation up to 1 hour before the scheduled time without any penalty.",
                  },
                  {
                    q: "Where can I find quiet spaces to work?",
                    a: 'The 3rd floor has designated quiet zones, or you can filter for "Focus Spaces" in the booking system.',
                  },
                ].map((item, index) => (
                  <div
                    key={index}
                    className="border-b border-gray-100 pb-4 last:border-0 last:pb-0"
                  >
                    <h3 className="font-medium text-gray-800 mb-1">{item.q}</h3>
                    <p className="text-gray-600 text-sm">{item.a}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Right column */}
          <div>
            {/* Live chat demo */}
            <div className="bg-white rounded-xl shadow-md p-6 mb-8">
              <h2 className="text-xl font-bold mb-4">Try a Live Demo</h2>
              <p className="text-gray-600 mb-6">
                Experience how our AI assistant can help you with workspace
                questions and bookings.
              </p>

              <div className="border rounded-lg p-4 bg-gray-50 mb-6 h-64 overflow-y-auto">
                <div className="mb-4">
                  <div className="bg-primary-50 text-gray-700 rounded-lg p-3 max-w-[80%]">
                    Hello! I'm WorkSpace Assistant. How can I help you today?
                  </div>
                </div>
                <div className="flex justify-end mb-4">
                  <div className="bg-primary text-white rounded-lg p-3 max-w-[80%]">
                    I need to book a meeting room for tomorrow
                  </div>
                </div>
                <div className="mb-4">
                  <div className="bg-primary-50 text-gray-700 rounded-lg p-3 max-w-[80%]">
                    I'd be happy to help you book a meeting room! What time
                    would you need it, and how many people will be attending?
                  </div>
                </div>
              </div>

              <div className="relative">
                <input
                  type="text"
                  placeholder="Type your question..."
                  className="w-full border border-gray-300 rounded-lg px-4 py-3 pr-12 focus:outline-none focus:ring-2 focus:ring-primary"
                  disabled
                />
                <button className="absolute right-2 top-1/2 transform -translate-y-1/2 text-primary">
                  <i className="fas fa-paper-plane"></i>
                </button>
              </div>
              <p className="text-xs text-gray-500 mt-2 text-center">
                This is a demo. For full functionality, please use the assistant
                in your dashboard.
              </p>
            </div>

            {/* Help card */}
            <div className="bg-gradient-to-r from-primary to-primary-dark rounded-xl shadow-md p-6 text-white">
              <h2 className="text-xl font-bold mb-4">Need More Help?</h2>
              <p className="mb-6">
                Our support team is available 24/7 to assist with any questions
                or issues you may have.
              </p>
              <div className="flex flex-col space-y-3">
                <button className="bg-white text-primary px-4 py-2 rounded-lg font-medium hover:bg-gray-100 transition-colors flex items-center justify-center gap-2">
                  <i className="fas fa-headset"></i>
                  Contact Support
                </button>
                <button className="bg-white/20 text-white px-4 py-2 rounded-lg font-medium hover:bg-white/30 transition-colors flex items-center justify-center gap-2">
                  <i className="fas fa-book"></i>
                  View Documentation
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
export default AIAssistantPage;
