import React from "react";


function EmailConfirmation() {
  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-white p-4">
      <div className="max-w-md w-full text-center">
        <div className="mb-12 flex justify-center">
          <div className="h-10 w-40 flex justify-center items-center">
            <img
              src="/images/logo-blue.png"
              alt="JechSpace Logo"
              className="h-10 object-contain"
            />
          </div>
        </div>

        <div className="mb-8">
          <h2 className="text-gray-800 text-base font-normal mb-6">
            We've sent a confirmation link to your email. Please check your
            inbox and click the link to verify your address.
          </h2>
        </div>

        <div className="text-sm text-gray-600">
          <p className="mb-1">Didn't receive the email?</p>
          <p>
            Check your spam folder or{" "}
            <a href="#" className="text-blue-500 hover:underline">
              resend verification link
            </a>
            .
          </p>
        </div>
      </div>
    </div>
  );
}

export default EmailConfirmation;
