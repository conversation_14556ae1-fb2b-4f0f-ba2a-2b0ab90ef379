import { motion } from "framer-motion";

const Hero = () => {
  return (
    <section
      id="hero"
      className="min-h-screen flex items-center relative overflow-hidden pt-20"
    >
      {/* Background Blobs */}
      <div className="absolute w-[600px] h-[600px] rounded-full bg-primary/15 filter blur-[80px] top-[-200px] right-[-100px] z-0"></div>
      <div className="absolute w-[600px] h-[600px] rounded-full bg-secondary/15 filter blur-[80px] bottom-[-350px] left-[-150px] z-0"></div>

      <div className="container mx-auto px-4 z-10">
        <div className="flex flex-col lg:flex-row items-center">
          <motion.div
            className="w-full lg:w-1/2 text-center lg:text-left mb-16 lg:mb-0"
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
          >
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-extrabold leading-tight mb-5 bg-gradient-to-r from-primary to-primary-dark bg-clip-text text-transparent">
              Transform Your Workspace Management
            </h1>
            <p className="text-lg text-gray-600 mb-8 max-w-xl mx-auto lg:mx-0">
              A comprehensive solution to optimize your office space, improve
              productivity, and enhance the workplace experience with smart
              management tools.
            </p>
            <div className="flex flex-wrap gap-4 justify-center lg:justify-start mb-10">
              <a
                href="#pricing"
                className="inline-flex items-center justify-center px-7 py-3 rounded-lg font-semibold text-white bg-gradient-to-r from-blue-500 to-blue-700 hover:translate-y-[-3px] hover:shadow-lg transition-all"
              >
                Get Started Free
              </a>
              <a
                href="#contact"
                className="inline-flex items-center justify-center px-8 py-4 rounded-lg font-semibold text-primary border-2 border-primary transition-all hover:bg-primary-50"
              >
                Schedule Demo
              </a>
            </div>
            <div className="flex flex-wrap gap-10 justify-center lg:justify-start">
              {[
                { number: "500+", label: "Companies" },
                { number: "95%", label: "Satisfaction" },
                { number: "24/7", label: "Support" },
              ].map((stat, index) => (
                <div key={index} className="flex flex-col">
                  <span className="text-3xl font-bold text-primary-dark">
                    {stat.number}
                  </span>
                  <span className="text-sm text-gray-500">{stat.label}</span>
                </div>
              ))}
            </div>
          </motion.div>

          <motion.div
            className="w-full lg:w-1/2"
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <img
              src="/images/hero.png"
              alt="WorkSpace Pro Dashboard"
              className="w-full rounded-xl shadow-2xl"
            />
          </motion.div>
        </div>
      </div>

      {/* Floating elements */}
      <motion.div
        className="absolute text-primary opacity-50 text-3xl top-1/5 left-1/10"
        animate={{ y: [-20, 0, -20], rotate: [0, 10, 0] }}
        transition={{ repeat: Infinity, duration: 8, ease: "easeInOut" }}
      >
        <i className="fas fa-laptop-code"></i>
      </motion.div>

      <motion.div
        className="absolute text-secondary opacity-50 text-2xl top-1/2 right-1/10"
        animate={{ y: [20, 0, 20], rotate: [0, -10, 0] }}
        transition={{ repeat: Infinity, duration: 10, ease: "easeInOut" }}
      >
        <i className="fas fa-calendar-check"></i>
      </motion.div>

      <motion.div
        className="absolute text-primary-light opacity-50 text-2xl bottom-1/5 left-1/5"
        animate={{ y: [-15, 0, -15], rotate: [0, 5, 0] }}
        transition={{ repeat: Infinity, duration: 12, ease: "easeInOut" }}
      >
        <i className="fas fa-chart-line"></i>
      </motion.div>
    </section>
  );
};

export default Hero;
