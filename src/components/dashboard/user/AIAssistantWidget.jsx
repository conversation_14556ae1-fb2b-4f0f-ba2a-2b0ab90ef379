import { useState, useRef, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Bot as BotIcon, Send as LucideSend, X } from "lucide-react";
import PropTypes from "prop-types";

const AIAssistantWidget = ({ isOpen, onClose }) => {
  const [messages, setMessages] = useState([
    {
      id: 1,
      type: "bot",
      text: "Hello! I'm JechSpace Assistant. How can I help you today?",
    },
  ]);
  const [inputValue, setInputValue] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef(null);

  // Sample quick questions
  const quickQuestions = [
    "How do I book a workspace?",
    "What's available tomorrow?",
    "Cancel my reservation",
    "Workspace amenities",
  ];

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  const handleInputChange = (e) => {
    setInputValue(e.target.value);
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (inputValue.trim() === "") return;

    // Add user message
    const userMessage = {
      id: messages.length + 1,
      type: "user",
      text: inputValue,
    };
    setMessages((prev) => [...prev, userMessage]);
    setInputValue("");

    // Simulate bot thinking
    setIsTyping(true);

    // Simulate bot response after a short delay
    setTimeout(() => {
      processUserInput(userMessage.text);
      setIsTyping(false);
    }, 1000);
  };

  const handleQuickQuestion = (question) => {
    // Add user message
    const userMessage = {
      id: messages.length + 1,
      type: "user",
      text: question,
    };
    setMessages((prev) => [...prev, userMessage]);

    // Simulate bot thinking
    setIsTyping(true);

    // Simulate bot response after a short delay
    setTimeout(() => {
      processUserInput(question);
      setIsTyping(false);
    }, 1000);
  };

  const processUserInput = (input) => {
    let botResponse = "";
    const lowerInput = input.toLowerCase();

    // Simple rule-based responses
    if (lowerInput.includes("book") || lowerInput.includes("reserve")) {
      botResponse =
        "To book a workspace, go to the 'Booking' tab, select your preferred date and time, then choose an available space on the floor plan. Click 'Confirm Booking' to complete your reservation.";
    } else if (
      lowerInput.includes("available") ||
      lowerInput.includes("tomorrow")
    ) {
      botResponse =
        "I can check availability for you! Please visit the 'Availability' section where you can view all available spaces with a calendar view. You can filter by date, time slot, and workspace amenities.";
    } else if (lowerInput.includes("cancel")) {
      botResponse =
        "To cancel a reservation, go to 'My Bookings', find the booking you want to cancel, and click the 'Cancel' button. Please note our cancellation policy may apply depending on how far in advance you're cancelling.";
    } else if (lowerInput.includes("amenities")) {
      botResponse =
        "Our workspaces come with various amenities including high-speed WiFi, ergonomic chairs, adjustable desks, power outlets, and access to meeting rooms. Premium spaces may include additional features like monitors, whiteboards, and privacy pods.";
    } else {
      botResponse =
        "Thanks for your question. While I'm still learning, I can help with booking workspaces, checking availability, managing reservations, and finding information about our facilities. Could you try rephrasing your question?";
    }

    // Add bot response
    setMessages((prev) => [
      ...prev,
      {
        id: prev.length + 1,
        type: "bot",
        text: botResponse,
      },
    ]);
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 20 }}
          className="fixed bottom-24 right-6 w-80 md:w-96 bg-white rounded-xl shadow-2xl overflow-hidden flex flex-col z-[1000]"
          style={{ height: "500px", maxHeight: "70vh" }}
        >
          {/* Header */}
          <div className="bg-gradient-to-r from-primary to-primary-dark p-4 text-white flex-shrink-0">
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <BotIcon size={20} />
                <h3 className="font-semibold">WorkSpace Assistant</h3>
              </div>
              <button
                onClick={onClose}
                className="hover:bg-white/20 p-1 rounded-full transition-all"
                aria-label="Close assistant"
              >
                <X size={18} />
              </button>
            </div>
          </div>

          {/* Chat Messages */}
          <div className="flex-1 p-4 overflow-y-auto bg-gray-50 flex flex-col">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`mb-4 flex ${
                  message.type === "user" ? "justify-end" : "justify-start"
                }`}
              >
                <div
                  className={`rounded-lg py-2 px-4 max-w-[80%] break-words ${
                    message.type === "user"
                      ? "bg-primary text-white"
                      : "bg-white border border-gray-200 text-gray-700"
                  }`}
                >
                  {message.text}
                </div>
              </div>
            ))}

            {isTyping && (
              <div className="flex justify-start mb-4">
                <div className="bg-white border border-gray-200 rounded-lg py-2 px-4 text-gray-700">
                  <div className="flex gap-1">
                    <span
                      className="inline-block w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                      style={{ animationDelay: "0s" }}
                    ></span>
                    <span
                      className="inline-block w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                      style={{ animationDelay: "0.2s" }}
                    ></span>
                    <span
                      className="inline-block w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                      style={{ animationDelay: "0.4s" }}
                    ></span>
                  </div>
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>

          {/* Quick Questions */}
          <div className="px-4 py-2 border-t border-gray-200 bg-white flex-shrink-0">
            <p className="text-xs text-gray-500 mb-2">Quick questions:</p>
            <div className="flex flex-wrap gap-2">
              {quickQuestions.map((question, index) => (
                <button
                  key={index}
                  onClick={() => handleQuickQuestion(question)}
                  className="bg-gray-100 text-gray-700 text-xs rounded-full px-3 py-1 hover:bg-gray-200 transition-colors"
                  type="button"
                >
                  {question}
                </button>
              ))}
            </div>
          </div>

          {/* Input Area */}
          <form
            onSubmit={handleSubmit}
            className="border-t border-gray-200 p-3 bg-white flex-shrink-0"
          >
            <div className="flex gap-2">
              <input
                type="text"
                value={inputValue}
                onChange={handleInputChange}
                placeholder="Type your question here..."
                className="flex-1 border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary"
              />
              <button
                type="submit"
                className="bg-primary text-white rounded-lg px-4 py-2 hover:bg-primary-dark transition-colors flex items-center gap-1"
              >
                <LucideSend size={16} />
                <span>Send</span>
              </button>
            </div>
          </form>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

AIAssistantWidget.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
};
export default AIAssistantWidget;