import { motion } from "framer-motion";

const steps = [
  {
    number: 1,
    title: "Sign Up",
    description:
      "Create your account and set up your organization profile in minutes.",
  },
  {
    number: 2,
    title: "Map Your Space",
    description:
      "Use our intuitive floor plan editor to create a digital map of your workspace.",
  },
  {
    number: 3,
    title: "Invite Users",
    description:
      "Add team members and assign appropriate roles and permissions.",
  },
  {
    number: 4,
    title: "Start Managing",
    description:
      "Begin optimizing your workspace with our powerful management tools.",
  },
];

const HowItWorksSection = () => {
  return (
    <section id="how-it-works" className="py-24 bg-gray-50">
      <div className="container mx-auto px-4">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl font-bold mb-4">How It Works</h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Get started with JechSpace in just a few simple steps and transform
            your workspace management experience.
          </p>
        </motion.div>

        <motion.div
          className="relative mt-20"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          {/* Line connecting the steps */}
          <div className="hidden md:block absolute top-16 left-1/2 transform -translate-x-1/2 w-4/5 h-0.5 bg-gray-200"></div>

          <div className="flex flex-col md:flex-row justify-between gap-8">
            {steps.map((step, index) => (
              <motion.div
                key={index}
                className="flex flex-col items-center text-center md:w-1/4"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <div className="relative w-16 h-16 bg-white rounded-full flex items-center justify-center text-2xl font-bold text-primary shadow-md mb-6 z-10">
                  <div className="absolute inset-[5px] rounded-full border-2 border-dotted border-primary-light"></div>
                  {step.number}
                </div>
                <h3 className="text-lg font-semibold mb-2">{step.title}</h3>
                <p className="text-gray-600 text-sm">{step.description}</p>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default HowItWorksSection;
