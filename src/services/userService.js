import api from './api';

/**
 * Service for handling user operations
 */
const userService = {
  /**
   * Get current user profile
   * @returns {Promise} - Response from API
   */
  getCurrentUser: async () => {
    try {
      const response = await api.get('/users/me/');
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  /**
   * Update user profile
   * @param {Object} userData - User data to update
   * @returns {Promise} - Response from API
   */
  updateProfile: async (userData) => {
    try {
      const response = await api.put('/users/me/', userData);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  /**
   * Change user password
   * @param {Object} passwordData - Password data
   * @param {string} passwordData.old_password - Current password
   * @param {string} passwordData.new_password - New password
   * @returns {Promise} - Response from API
   */
  changePassword: async (passwordData) => {
    try {
      const response = await api.post('/auth/password/', passwordData);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  /**
   * Get user notifications
   * @param {Object} params - Query parameters
   * @returns {Promise} - Response from API
   */
  getNotifications: async (params = {}) => {
    try {
      const response = await api.get('/users/notifications/', { params });
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  /**
   * Mark notification as read
   * @param {string} notificationId - Notification ID
   * @returns {Promise} - Response from API
   */
  markNotificationAsRead: async (notificationId) => {
    try {
      const response = await api.put(`/users/notifications/${notificationId}/read/`);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  /**
   * Update user preferences
   * @param {Object} preferences - User preferences
   * @returns {Promise} - Response from API
   */
  updatePreferences: async (preferences) => {
    try {
      const response = await api.put('/users/preferences/', preferences);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },
};

export default userService;
