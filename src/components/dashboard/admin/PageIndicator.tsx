import React from "react";
import PropTypes from "prop-types";

const PageIndicator = ({ currentStep, totalSteps }) => {
  return (
    <div className="flex space-x-2">
      {Array.from({ length: totalSteps }).map((_, index) => (
        <div
          key={index}
          className={`${
            index === currentStep - 1 ? "bg-blue-500" : "bg-gray-300"
          } rounded-full ${window.innerWidth < 640 ? "w-4 h-1" : "w-8 h-2"}`}
        ></div>
      ))}
    </div>
  );
};

PageIndicator.propTypes = {
  currentStep: PropTypes.number.isRequired,
  totalSteps: PropTypes.number.isRequired,
};

export default PageIndicator;
