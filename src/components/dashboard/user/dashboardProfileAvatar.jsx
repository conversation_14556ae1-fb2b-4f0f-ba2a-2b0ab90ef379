// craeete a dashboard profile avatar component
import React from "react";
import { Link } from "react-router-dom";
import { useState } from "react";

const DashboardProfileAvatar = ({ user }) => {
  const [isOpen, setIsOpen] = useState(false);

  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  return (
    <div className="relative">
      <button
        onClick={toggleDropdown}
        className="flex items-center p-2 rounded-full hover:bg-gray-200 focus:outline-none"
      >
        <img
          src={user.profilePicture || "https://via.placeholder.com/40"}
          alt="User Avatar"
          className="h-8 w-8 rounded-full"
        />
      </button>
      {isOpen && (
        <div className="absolute right-0 mt-2 w-48 bg-white border border-gray-300 rounded-lg shadow-lg z-10">
          <Link
            to="/profile"
            className="block px-4 py-2 text-gray-700 hover:bg-gray-100"
          >
            View Profile
          </Link>
          <Link
            to="/settings"
            className="block px-4 py-2 text-gray-700 hover:bg-gray-100"
          >
            Settings
          </Link>
          <Link
            to="/logout"
            className="block px-4 py-2 text-gray-700 hover:bg-gray-100"
          >
            Logout
          </Link>
        </div>
      )}
    </div>
  );
};

// PropTypes for type checking
DashboardProfileAvatar.propTypes = {
  user: PropTypes.shape({
    profilePicture: PropTypes.string,
  }),
};

// Default props
DashboardProfileAvatar.defaultProps = {
  user: {
    profilePicture: "https://via.placeholder.com/40",
  },
};
export default DashboardProfileAvatar;
