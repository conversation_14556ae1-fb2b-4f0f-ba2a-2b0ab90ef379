import { NavLink } from "react-router-dom";
import PropTypes from "prop-types";
import { X } from "lucide-react";
import { LayoutDashboard, Search, CalendarDays, Bot, Menu } from "lucide-react";

function UserSidebar({ isOpen, setIsOpen }) {
  const navItems = [
    {
      path: "/user/dashboard/dashboardPage",
      icon: <LayoutDashboard size={20} />,
      label: "Dashboard",
    },
    {
      path: "/user/dashboard/findSpace",
      icon: <Search size={20} />,
      label: "Find & Book Space",
    },
    {
      path: "/user/dashboard/myBookings",
      icon: <CalendarDays size={20} />,
      label: "My Bookings",
    },
    {
      path: "/user/dashboard/assistant",
      icon: <Bot size={20} />,
      label: "AI Assistant",
    },
    {
      path: "/user/dashboard/analytics",
      icon: <Search size={20} />, // Replaced ChartSplineIcon with Search as it seems to be undefined
      label: "Analytics",
    },
  ];

  return (
    <>
      {/* Sidebar with proper z-index and transition */}
      <div
        className={`bg-white border-r border-gray-200 w-64 fixed inset-y-0 left-0 z-50 transition-transform duration-300 transform ${
          isOpen ? "translate-x-0" : "-translate-x-full"
        } md:translate-x-0 flex flex-col`}
      >
        {/* Logo and Close Button */}
        <div className="px-6 pt-8 pb-6 flex items-center justify-between shrink-0">
          <img
            src="/images/logo-blue.png"
            alt="JechAdmin Logo"
            className="h-10 w-auto"
          />
          <button
            onClick={() => setIsOpen(false)}
            className="md:hidden text-gray-500 hover:text-gray-700"
          >
            <X size={24} />
          </button>
        </div>

        {/* Navigation - with proper overflow handling */}
        <div className="px-4 overflow-y-auto flex-1 pb-8">
          <div className="space-y-1">
            {navItems.map((item) => (
              <NavLink
                key={item.path}
                to={item.path}
                onClick={() => setIsOpen(false)} // Close sidebar when clicking a link on mobile
                className={({ isActive }) =>
                  `flex items-center px-4 py-3 text-gray-700 rounded-lg hover:bg-blue-50 hover:text-blue-700 transition-colors ${
                    isActive ? "bg-blue-50 text-blue-700" : ""
                  }`
                }
              >
                <span className="mr-3">{item.icon}</span>
                <span className="font-medium">{item.label}</span>
              </NavLink>
            ))}
          </div>
        </div>
      </div>

      {/* Overlay to close sidebar on mobile when clicking outside */}
      {isOpen && (
        <div
          className="md:hidden fixed inset-0 bg-black bg-opacity-50 z-40"
          onClick={() => setIsOpen(false)}
        ></div>
      )}
    </>
  );
}

export default UserSidebar;
UserSidebar.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  setIsOpen: PropTypes.func.isRequired,
};
