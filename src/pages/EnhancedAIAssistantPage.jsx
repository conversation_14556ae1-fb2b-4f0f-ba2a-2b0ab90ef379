import React, { useState, useEffect, useRef } from 'react';
import { useBooking } from '../context/BookingContext';
import { useSpace } from '../context/SpaceContext';
import { useApp } from '../context/AppContext';
import { useToast } from '../components/common/NotificationContainer';
import { 
  Send, 
  Bot, 
  User, 
  Calendar, 
  MapPin, 
  Clock, 
  Users,
  Lightbulb,
  TrendingUp,
  MessageSquare,
  Mic,
  MicOff,
  Copy,
  ThumbsUp,
  ThumbsDown
} from 'lucide-react';
import { InlineLoader } from '../components/common/LoadingSpinner';

const EnhancedAIAssistantPage = () => {
  const { state: bookingState, actions: bookingActions } = useBooking();
  const { state: spaceState, actions: spaceActions } = useSpace();
  const { state: appState } = useApp();
  const toast = useToast();
  
  const [messages, setMessages] = useState([
    {
      id: 1,
      type: 'bot',
      content: `Hello ${appState.user?.first_name || 'there'}! I'm your workspace assistant. I can help you with:
      
• Finding and booking spaces
• Checking availability
• Managing your bookings
• Getting workspace insights
• Answering questions about amenities

How can I assist you today?`,
      timestamp: new Date(),
      suggestions: [
        'Find a meeting room for 6 people',
        'Show my upcoming bookings',
        'What spaces are available now?',
        'Book a conference room for tomorrow'
      ]
    }
  ]);
  
  const [inputMessage, setInputMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const [recognition, setRecognition] = useState(null);
  const messagesEndRef = useRef(null);
  const inputRef = useRef(null);

  useEffect(() => {
    // Initialize speech recognition
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      const recognitionInstance = new SpeechRecognition();
      recognitionInstance.continuous = false;
      recognitionInstance.interimResults = false;
      recognitionInstance.lang = 'en-US';
      
      recognitionInstance.onresult = (event) => {
        const transcript = event.results[0][0].transcript;
        setInputMessage(transcript);
        setIsListening(false);
      };
      
      recognitionInstance.onerror = () => {
        setIsListening(false);
        toast.error('Speech Recognition Error', 'Could not recognize speech');
      };
      
      recognitionInstance.onend = () => {
        setIsListening(false);
      };
      
      setRecognition(recognitionInstance);
    }
  }, [toast]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSpeechRecognition = () => {
    if (!recognition) {
      toast.error('Not Supported', 'Speech recognition is not supported in this browser');
      return;
    }

    if (isListening) {
      recognition.stop();
      setIsListening(false);
    } else {
      recognition.start();
      setIsListening(true);
    }
  };

  const processUserMessage = async (message) => {
    const lowerMessage = message.toLowerCase();
    
    // Simulate AI processing delay
    setIsTyping(true);
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));
    
    let response = '';
    let suggestions = [];
    let actionData = null;

    // Intent recognition and response generation
    if (lowerMessage.includes('book') || lowerMessage.includes('reserve')) {
      if (lowerMessage.includes('meeting room') || lowerMessage.includes('conference')) {
        response = "I'd be happy to help you book a meeting room! Let me show you available options.";
        actionData = { type: 'show_spaces', filter: 'meeting_room' };
        suggestions = [
          'Show rooms for 4-6 people',
          'Find rooms with projector',
          'Book for specific time',
          'Check tomorrow\'s availability'
        ];
      } else {
        response = "I can help you book a space. What type of space are you looking for and when do you need it?";
        suggestions = [
          'Meeting room for 6 people',
          'Hot desk for today',
          'Conference room tomorrow',
          'Private office this week'
        ];
      }
    } else if (lowerMessage.includes('available') || lowerMessage.includes('free')) {
      response = "Let me check what spaces are currently available for you.";
      actionData = { type: 'check_availability' };
      suggestions = [
        'Show available now',
        'Check specific time',
        'Filter by capacity',
        'Show by floor'
      ];
    } else if (lowerMessage.includes('my booking') || lowerMessage.includes('upcoming')) {
      response = "Here are your upcoming bookings:";
      actionData = { type: 'show_bookings' };
      suggestions = [
        'Cancel a booking',
        'Extend booking time',
        'Modify booking',
        'Book another space'
      ];
    } else if (lowerMessage.includes('cancel') && lowerMessage.includes('booking')) {
      response = "I can help you cancel a booking. Which booking would you like to cancel?";
      actionData = { type: 'cancel_booking' };
      suggestions = [
        'Show my bookings',
        'Cancel today\'s booking',
        'Cancel tomorrow\'s booking'
      ];
    } else if (lowerMessage.includes('amenities') || lowerMessage.includes('features')) {
      response = "Here are the amenities available in our workspace:";
      actionData = { type: 'show_amenities' };
      suggestions = [
        'Rooms with projectors',
        'Spaces with whiteboards',
        'Rooms with video conferencing',
        'Quiet phone booths'
      ];
    } else if (lowerMessage.includes('help') || lowerMessage.includes('what can you do')) {
      response = `I can assist you with various workspace tasks:

🏢 **Space Management**
• Find and book available spaces
• Check real-time availability
• Filter by capacity, amenities, or location

📅 **Booking Management**
• View your upcoming bookings
• Cancel or modify reservations
• Extend booking duration
• Set booking reminders

📊 **Insights & Analytics**
• Show usage patterns
• Recommend optimal booking times
• Display popular spaces
• Provide utilization insights

🎯 **Smart Suggestions**
• Recommend spaces based on your preferences
• Suggest optimal meeting times
• Alert about conflicts or changes

What would you like to do?`;
      suggestions = [
        'Find a space now',
        'Show my bookings',
        'Check availability',
        'Get recommendations'
      ];
    } else if (lowerMessage.includes('recommend') || lowerMessage.includes('suggest')) {
      response = "Based on your booking history and preferences, I recommend:";
      actionData = { type: 'recommendations' };
      suggestions = [
        'Book recommended space',
        'See more options',
        'Set preferences',
        'View analytics'
      ];
    } else {
      // Default response with helpful suggestions
      response = `I understand you're asking about "${message}". Let me help you with that! Here are some things I can assist with:`;
      suggestions = [
        'Find available spaces',
        'Book a meeting room',
        'Check my bookings',
        'Show amenities'
      ];
    }

    setIsTyping(false);
    
    const botMessage = {
      id: Date.now(),
      type: 'bot',
      content: response,
      timestamp: new Date(),
      suggestions,
      actionData
    };

    setMessages(prev => [...prev, botMessage]);

    // Execute actions if needed
    if (actionData) {
      executeAction(actionData);
    }
  };

  const executeAction = async (actionData) => {
    try {
      switch (actionData.type) {
        case 'show_spaces':
          await spaceActions.loadSpaces();
          break;
        case 'check_availability':
          await spaceActions.getAvailableSpaces({
            date: new Date().toISOString().split('T')[0],
            start_time: new Date().toISOString(),
          });
          break;
        case 'show_bookings':
          await bookingActions.loadUserBookings();
          break;
        case 'show_amenities':
          await spaceActions.loadAmenities();
          break;
        default:
          break;
      }
    } catch (error) {
      console.error('Action execution error:', error);
    }
  };

  const handleSendMessage = async () => {
    if (!inputMessage.trim()) return;

    const userMessage = {
      id: Date.now(),
      type: 'user',
      content: inputMessage,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    const messageToProcess = inputMessage;
    setInputMessage('');

    await processUserMessage(messageToProcess);
  };

  const handleSuggestionClick = (suggestion) => {
    setInputMessage(suggestion);
    inputRef.current?.focus();
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const copyMessage = (content) => {
    navigator.clipboard.writeText(content);
    toast.success('Copied', 'Message copied to clipboard');
  };

  const MessageBubble = ({ message }) => (
    <div className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'} mb-4`}>
      <div className={`flex max-w-[80%] ${message.type === 'user' ? 'flex-row-reverse' : 'flex-row'}`}>
        {/* Avatar */}
        <div className={`flex-shrink-0 ${message.type === 'user' ? 'ml-3' : 'mr-3'}`}>
          <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
            message.type === 'user' 
              ? 'bg-blue-600 text-white' 
              : 'bg-gray-200 text-gray-600'
          }`}>
            {message.type === 'user' ? <User size={16} /> : <Bot size={16} />}
          </div>
        </div>
        
        {/* Message Content */}
        <div className={`rounded-lg px-4 py-2 ${
          message.type === 'user'
            ? 'bg-blue-600 text-white'
            : 'bg-gray-100 text-gray-900'
        }`}>
          <div className="whitespace-pre-wrap">{message.content}</div>
          
          {/* Suggestions */}
          {message.suggestions && message.suggestions.length > 0 && (
            <div className="mt-3 space-y-2">
              {message.suggestions.map((suggestion, index) => (
                <button
                  key={index}
                  onClick={() => handleSuggestionClick(suggestion)}
                  className="block w-full text-left px-3 py-2 text-sm bg-white bg-opacity-20 hover:bg-opacity-30 rounded-md transition-colors"
                >
                  {suggestion}
                </button>
              ))}
            </div>
          )}
          
          {/* Timestamp and Actions */}
          <div className={`flex items-center justify-between mt-2 text-xs ${
            message.type === 'user' ? 'text-blue-100' : 'text-gray-500'
          }`}>
            <span>{message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}</span>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => copyMessage(message.content)}
                className="hover:opacity-70 transition-opacity"
              >
                <Copy size={12} />
              </button>
              {message.type === 'bot' && (
                <>
                  <button className="hover:opacity-70 transition-opacity">
                    <ThumbsUp size={12} />
                  </button>
                  <button className="hover:opacity-70 transition-opacity">
                    <ThumbsDown size={12} />
                  </button>
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const QuickActions = () => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-4">
      <h3 className="text-sm font-medium text-gray-900 mb-3">Quick Actions</h3>
      <div className="grid grid-cols-2 gap-2">
        <button
          onClick={() => handleSuggestionClick('Find a meeting room for now')}
          className="flex items-center p-2 text-sm text-blue-600 bg-blue-50 rounded-md hover:bg-blue-100 transition-colors"
        >
          <Calendar className="h-4 w-4 mr-2" />
          Book Now
        </button>
        <button
          onClick={() => handleSuggestionClick('Show my upcoming bookings')}
          className="flex items-center p-2 text-sm text-green-600 bg-green-50 rounded-md hover:bg-green-100 transition-colors"
        >
          <Clock className="h-4 w-4 mr-2" />
          My Bookings
        </button>
        <button
          onClick={() => handleSuggestionClick('What spaces are available now?')}
          className="flex items-center p-2 text-sm text-purple-600 bg-purple-50 rounded-md hover:bg-purple-100 transition-colors"
        >
          <MapPin className="h-4 w-4 mr-2" />
          Check Availability
        </button>
        <button
          onClick={() => handleSuggestionClick('Give me recommendations')}
          className="flex items-center p-2 text-sm text-orange-600 bg-orange-50 rounded-md hover:bg-orange-100 transition-colors"
        >
          <Lightbulb className="h-4 w-4 mr-2" />
          Get Suggestions
        </button>
      </div>
    </div>
  );

  return (
    <div className="flex h-[calc(100vh-2rem)] max-w-6xl mx-auto p-6">
      {/* Sidebar */}
      <div className="w-80 mr-6">
        <QuickActions />
        
        {/* Recent Insights */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <h3 className="text-sm font-medium text-gray-900 mb-3">Workspace Insights</h3>
          <div className="space-y-3">
            <div className="flex items-center text-sm text-gray-600">
              <TrendingUp className="h-4 w-4 mr-2 text-green-500" />
              <span>Peak hours: 10 AM - 2 PM</span>
            </div>
            <div className="flex items-center text-sm text-gray-600">
              <Users className="h-4 w-4 mr-2 text-blue-500" />
              <span>Most popular: Conference Room A</span>
            </div>
            <div className="flex items-center text-sm text-gray-600">
              <MapPin className="h-4 w-4 mr-2 text-purple-500" />
              <span>Available now: 12 spaces</span>
            </div>
          </div>
        </div>
      </div>

      {/* Chat Interface */}
      <div className="flex-1 flex flex-col bg-white rounded-lg shadow-sm border border-gray-200">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <div className="flex items-center">
            <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
              <Bot className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <h2 className="text-lg font-semibold text-gray-900">AI Workspace Assistant</h2>
              <p className="text-sm text-gray-500">Always here to help with your workspace needs</p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
              <div className="w-2 h-2 bg-green-400 rounded-full mr-1"></div>
              Online
            </span>
          </div>
        </div>

        {/* Messages */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          {messages.map((message) => (
            <MessageBubble key={message.id} message={message} />
          ))}
          
          {isTyping && (
            <div className="flex justify-start mb-4">
              <div className="flex items-center space-x-2 bg-gray-100 rounded-lg px-4 py-2">
                <Bot size={16} className="text-gray-600" />
                <InlineLoader size="sm" text="AI is thinking..." />
              </div>
            </div>
          )}
          
          <div ref={messagesEndRef} />
        </div>

        {/* Input */}
        <div className="border-t border-gray-200 p-4">
          <div className="flex items-center space-x-2">
            <div className="flex-1 relative">
              <textarea
                ref={inputRef}
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Ask me anything about workspaces..."
                className="w-full border border-gray-300 rounded-lg px-4 py-2 pr-12 focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                rows={1}
                style={{ minHeight: '40px', maxHeight: '120px' }}
              />
              <button
                onClick={handleSpeechRecognition}
                className={`absolute right-2 top-1/2 transform -translate-y-1/2 p-1 rounded-md transition-colors ${
                  isListening 
                    ? 'text-red-600 bg-red-50 hover:bg-red-100' 
                    : 'text-gray-400 hover:text-gray-600 hover:bg-gray-50'
                }`}
              >
                {isListening ? <MicOff size={16} /> : <Mic size={16} />}
              </button>
            </div>
            <button
              onClick={handleSendMessage}
              disabled={!inputMessage.trim() || isTyping}
              className="flex items-center justify-center w-10 h-10 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <Send size={16} />
            </button>
          </div>
          <p className="text-xs text-gray-500 mt-2">
            Press Enter to send, Shift+Enter for new line
          </p>
        </div>
      </div>
    </div>
  );
};

export default EnhancedAIAssistantPage;
