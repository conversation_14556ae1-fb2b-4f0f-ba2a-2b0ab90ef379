import { useState } from "react";
import { Users, Shield } from "lucide-react";
import UserDirectory from "./UserDirectory";
import RolesPermissions from "./RolesPermissions";

export default function UserManagement() {
  const [activeTab, setActiveTab] = useState("directory");

  return (
    <div className="p-4">
      {/* Header Section */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-800">User Management</h1>
        <p className="text-gray-600">
          Manage user accounts, roles, and permissions
        </p>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200">
        <ul className="flex flex-wrap -mb-px">
          <li className="mr-2">
            <button
              className={`inline-flex items-center py-4 px-6 text-sm font-medium text-center border-b-2 ${
                activeTab === "directory"
                  ? "text-blue-600 border-blue-600"
                  : "border-transparent text-black hover:text-gray-600 hover:border-gray-300"
              }`}
              onClick={() => setActiveTab("directory")}
            >
              <Users className="w-5 h-5 mr-2" />
              User Directory
            </button>
          </li>
          <li className="mr-2">
            <button
              className={`inline-flex items-center py-4 px-6 text-sm font-medium text-center border-b-2 ${
                activeTab === "roles"
                  ? "text-blue-600 border-blue-600"
                  : "border-transparent text-black hover:text-gray-600 hover:border-gray-300"
              }`}
              onClick={() => setActiveTab("roles")}
            >
              <Shield className="w-5 h-5 mr-2" />
              Roles & Permissions
            </button>
          </li>
        </ul>
      </div>

      {/* Content Section */}
      <div className="mt-6">
        {activeTab === "directory" && <UserDirectory />}
        {activeTab === "roles" && <RolesPermissions />}
      </div>
    </div>
  );
}
