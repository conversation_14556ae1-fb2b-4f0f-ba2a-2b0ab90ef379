// Admin Routes Configuration

import { Routes, Route } from "react-router-dom";
import EmployerRegistration from "../components/dashboard/admin/EmployerRegistration";
import EmailConfirmation from "../components/dashboard/admin/EmailConfirmation";
import AdminOnboarding from "../components/dashboard/admin/AdminOnboarding";
import AdminDashboardLayout from "../components/dashboard/admin/AdminDashboardLayout";
import AdminDashboard from "../components/dashboard/admin/AdminDashboard";
import FloorPlan from "../components/features/dashboard/admin/space/FloorPlan";
import SpaceList from "../components/features/SpaceList";
import BookingRules from "../components/features/dashboard/BookingRules";
import Integrations from "../components/features/dashboard/Integrations";
import Notification from "../components/features/Notification";
import UserManagement from "../components/dashboard/admin/UserManagement";
import RolesPermissions from "../components/dashboard/admin/RolesPermissions";
import OnboardingPage1 from "../pages/OnboardingPage1";
import OnboardingPage2 from "../pages/OnboardingPage2";
import OnboardingPage3 from "../pages/OnboardingPage3";
import OnboardingPage4 from "../pages/OnboardingPage4";
import OnboardingPage5 from "../pages/OnboardingPage5";
import AdminDashboardPage from "../pages/AdminDashboardPage";
import NotFound from "../pages/NotFound";
import "../App.css";

function AdminRoutes() {
  return (
    <Routes>
      {/* Registration and confirmation routes */}
      <Route path="register" element={<EmployerRegistration />} />
      <Route path="confirmation" element={<EmailConfirmation />} />

      {/* Onboarding routes */}
      <Route path="onboarding" element={<AdminOnboarding />} />
      <Route path="onboarding/step1" element={<OnboardingPage1 />} />
      <Route path="onboarding/step2" element={<OnboardingPage2 />} />
      <Route path="onboarding/step3" element={<OnboardingPage3 />} />
      <Route path="onboarding/step4" element={<OnboardingPage4 />} />
      <Route path="onboarding/step5" element={<OnboardingPage5 />} />

      {/* Dashboard with nested routes */}
      <Route path="dashboard" element={<AdminDashboardLayout />}>
        <Route index element={<AdminDashboard />} />
        <Route path="user-management" element={<UserManagement />} />
        <Route path="floor-plan" element={<FloorPlan />} />
        <Route path="space" element={<SpaceList />} />
        <Route path="booking-rules" element={<BookingRules />} />
        <Route path="integrations" element={<Integrations />} />
        <Route path="notifications" element={<Notification />} />
        <Route path="roles-permissions" element={<RolesPermissions />} />
        <Route path="*" element={<NotFound />} />
      </Route>

      {/* Catch-all for admin routes that don't match */}
      <Route path="*" element={<NotFound />} />
    </Routes>
  );
}

export default AdminRoutes;
