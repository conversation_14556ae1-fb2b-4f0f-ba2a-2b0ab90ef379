import api from './api';
import { getApiEndpointUrl, formatApiError, retryApiCall } from '../utils/apiUtils';

/**
 * Service for handling workspace operations with environment-aware endpoints
 */
const spaceService = {
  /**
   * Get all spaces
   * @param {Object} params - Query parameters
   * @returns {Promise} - Response from API
   */
  getAllSpaces: async (params = {}) => {
    try {
      const response = await api.get('/spaces/', { params });
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  /**
   * Get space by ID
   * @param {string} id - Space ID
   * @returns {Promise} - Response from API
   */
  getSpaceById: async (id) => {
    try {
      const response = await api.get(`/spaces/${id}/`);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  /**
   * Create a new space
   * @param {Object} spaceData - Space data
   * @returns {Promise} - Response from API
   */
  createSpace: async (spaceData) => {
    try {
      const response = await api.post('/spaces/', spaceData);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  /**
   * Update a space
   * @param {string} id - Space ID
   * @param {Object} spaceData - Updated space data
   * @returns {Promise} - Response from API
   */
  updateSpace: async (id, spaceData) => {
    try {
      const response = await api.put(`/spaces/${id}/`, spaceData);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  /**
   * Delete a space
   * @param {string} id - Space ID
   * @returns {Promise} - Response from API
   */
  deleteSpace: async (id) => {
    try {
      const response = await api.delete(`/spaces/${id}/`);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  /**
   * Get available spaces for booking
   * @param {Object} params - Query parameters (date, time, etc.)
   * @returns {Promise} - Response from API
   */
  getAvailableSpaces: async (params = {}) => {
    try {
      const response = await api.get(getApiEndpointUrl('spaces', 'available'), { params });
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  /**
   * Search spaces
   * @param {Object} searchParams - Search parameters
   * @returns {Promise} - Response from API
   */
  searchSpaces: async (searchParams = {}) => {
    try {
      const response = await api.get(getApiEndpointUrl('spaces', 'search'), { params: searchParams });
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  /**
   * Upload space images
   * @param {string} spaceId - Space ID
   * @param {FileList} files - Image files
   * @returns {Promise} - Response from API
   */
  uploadSpaceImages: async (spaceId, files) => {
    try {
      const formData = new FormData();
      Array.from(files).forEach((file, index) => {
        formData.append(`image_${index}`, file);
      });

      const response = await api.post(`/spaces/${spaceId}/images/`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  /**
   * Get space utilization stats
   * @param {string} spaceId - Space ID
   * @param {Object} params - Query parameters
   * @returns {Promise} - Response from API
   */
  getSpaceUtilization: async (spaceId, params = {}) => {
    try {
      const response = await api.get(`/spaces/${spaceId}/utilization/`, { params });
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },
};

export default spaceService;
