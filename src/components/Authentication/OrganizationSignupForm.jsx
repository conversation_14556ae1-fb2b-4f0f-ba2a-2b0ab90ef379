import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Eye, EyeOff } from "lucide-react";
import { authService } from "../../services";

function OrganizationSignupForm() {
  const [formData, setFormData] = useState({
    email: "",
    password: "",
    confirmPassword: "",
    organizationName: "",
    organizationType: "private",
    industry: "",
    size: "",
    agreedToTerms: false,
  });

  const [errors, setErrors] = useState({});
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const navigate = useNavigate();

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === "checkbox" ? checked : value,
    });
  };

  const validateForm = () => {
    const newErrors = {};

    // Organization name validation
    if (!formData.organizationName) {
      newErrors.organizationName = "Organization name is required";
    }

    // Industry validation
    if (!formData.industry) {
      newErrors.industry = "Please select an industry";
    }

    // Size validation
    if (!formData.size) {
      newErrors.size = "Please select organization size";
    }

    // Email validation
    if (!formData.email) {
      newErrors.email = "Email is required";
    } else if (!/^[\w.-]+@([\w-]+\.)+[a-zA-Z]{2,}$/.test(formData.email)) {
      newErrors.email = "Please use a valid email address";
    }

    // Password validation
    if (!formData.password) {
      newErrors.password = "Password is required";
    } else if (
      !/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[^A-Za-z\d]).{8,}$/.test(
        formData.password
      )
    ) {
      newErrors.password =
        "Password must be at least 8 characters and include uppercase, lowercase, number, and symbol";
    }

    // Confirm password validation
    if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = "Passwords do not match";
    }

    // Terms agreement
    if (!formData.agreedToTerms) {
      newErrors.agreedToTerms =
        "You must agree to the terms and privacy policy";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const toggleConfirmPasswordVisibility = () => {
    setShowConfirmPassword(!showConfirmPassword);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (validateForm()) {
      try {
        // Prepare data for API
        const apiData = {
          email: formData.email,
          password: formData.password,
          confirm_password: formData.confirmPassword,
          // Required fields for the backend
          first_name: "Organization", // Default value
          last_name: "Admin", // Default value
          profession: formData.industry || "Not specified", // Use industry as profession
          phone_number: "", // Optional field
          // Organization specific fields
          organization_name: formData.organizationName,
          organization_type: formData.organizationType,
          industry: formData.industry,
          size: formData.size,
          // Make sure to use the exact value expected by the backend
          user_type: "organization_owner",
        };

        console.log("Organization signup data:", apiData);
        // Use the authService to register the organization
        const data = await authService.registerOrganization(apiData);
        console.log("Registration successful:", data);

        // Redirect to the next page
        navigate("/confirmation/organization");
      } catch (error) {
        console.error("Registration error:", error);

        // Log detailed error information for debugging
        if (error.errors) {
          console.log(
            "Validation errors:",
            JSON.stringify(error.errors, null, 2)
          );

          // Extract and display specific error messages
          let errorMessages = "";

          // Handle different error formats
          Object.entries(error.errors).forEach(([field, messages]) => {
            if (typeof messages === "object" && messages !== null) {
              // If messages is an object with message property
              const messageText = messages.message || JSON.stringify(messages);
              errorMessages += `${field}: ${messageText}\n`;

              // Provide specific guidance based on the field
              if (field === "password") {
                errorMessages +=
                  "Password must be at least 8 characters and include letters, numbers, and special characters.\n";
              } else if (field === "email") {
                // Check if this is a uniqueness error
                if (messages.code === "unique") {
                  errorMessages +=
                    "This email is already registered. Please use a different email address or try logging in.\n";
                } else {
                  errorMessages += "Please provide a valid email address.\n";
                }
              } else if (field === "profession") {
                errorMessages +=
                  "Profession is required. Please provide your occupation.\n";
              }
            } else if (Array.isArray(messages)) {
              // If messages is an array
              errorMessages += `${field}: ${messages.join(", ")}\n`;
            } else {
              // If messages is a string or other type
              errorMessages += `${field}: ${messages}\n`;
            }
          });

          alert(`Registration failed:\n${errorMessages}`);
        } else {
          alert(error.message || "An error occurred during registration");
        }
      }
    }
  };

  return (
    <div className="flex min-h-screen w-full">
      {/* Left Panel - Blue section with illustration */}
      <div className="hidden md:flex md:w-1/2 bg-blue-500 p-12 flex-col justify-between sticky top-0 h-screen">
        <div className="text-white text-3xl font-bold max-w-md">
          <h1 className="text-2xl text-white mb-4">
            Organization Registration
          </h1>
          <p className="text-lg font-normal">
            Streamline how your organization books, manages, and utilizes
            workspaces. Enter your company details to begin setting up your
            smart workspace experience.
          </p>
        </div>

        <div className="absolute bottom-0 left-0 right-0 flex justify-center items-center">
          <img
            src="/images/employer-registration.png"
            alt="Illustration of people around a table"
            className="w-full h-auto max-h-96 object-contain"
          />
        </div>
      </div>

      {/* Right Panel - Sign up form */}
      <div className="w-full md:w-1/2 p-8 flex flex-col overflow-y-auto">
        <div className="w-full max-w-md mx-auto">
          {/* Logo */}
          <div className="flex justify-center mb-8">
            <img src="/images/logo-blue.png" alt="Logo" className="h-8" />
          </div>

          <h2 className="text-2xl font-bold text-center mb-2">
            Organization Sign Up
          </h2>
          <p className="text-gray-600 text-center mb-8">
            Register your organization with JechSpace
          </p>

          <form onSubmit={handleSubmit} className="space-y-6 pb-8">
            <div>
              <label
                htmlFor="organizationName"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Organization Name
              </label>
              <input
                type="text"
                id="organizationName"
                name="organizationName"
                value={formData.organizationName}
                onChange={handleInputChange}
                placeholder="Your Organization Inc."
                className={`w-full px-4 py-2 bg-gray-100 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  errors.organizationName ? "border-red-500" : "border-gray-200"
                }`}
              />
              {errors.organizationName && (
                <p className="text-red-500 text-xs mt-1">
                  {errors.organizationName}
                </p>
              )}
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <label
                  htmlFor="organizationType"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Organization Type
                </label>
                <select
                  id="organizationType"
                  name="organizationType"
                  value={formData.organizationType}
                  onChange={handleInputChange}
                  className="w-full px-4 py-2 bg-gray-100 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 border-gray-200"
                >
                  <option value="private">Private</option>
                  <option value="public">Public</option>
                  <option value="non-profit">Non-Profit</option>
                  <option value="government">Government</option>
                </select>
              </div>

              <div>
                <label
                  htmlFor="industry"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Industry
                </label>
                <select
                  id="industry"
                  name="industry"
                  value={formData.industry}
                  onChange={handleInputChange}
                  className={`w-full px-4 py-2 bg-gray-100 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.industry ? "border-red-500" : "border-gray-200"
                  }`}
                >
                  <option value="">Select...</option>
                  <option value="technology">Technology</option>
                  <option value="finance">Finance</option>
                  <option value="healthcare">Healthcare</option>
                  <option value="education">Education</option>
                  <option value="manufacturing">Manufacturing</option>
                  <option value="retail">Retail</option>
                  <option value="other">Other</option>
                </select>
                {errors.industry && (
                  <p className="text-red-500 text-xs mt-1">{errors.industry}</p>
                )}
              </div>
            </div>

            <div>
              <label
                htmlFor="size"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Organization Size
              </label>
              <select
                id="size"
                name="size"
                value={formData.size}
                onChange={handleInputChange}
                className={`w-full px-4 py-2 bg-gray-100 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  errors.size ? "border-red-500" : "border-gray-200"
                }`}
              >
                <option value="">Select...</option>
                <option value="1-10">1-10 employees</option>
                <option value="11-50">11-50 employees</option>
                <option value="51-200">51-200 employees</option>
                <option value="201-500">201-500 employees</option>
                <option value="501+">501+ employees</option>
              </select>
              {errors.size && (
                <p className="text-red-500 text-xs mt-1">{errors.size}</p>
              )}
            </div>

            <div>
              <label
                htmlFor="email"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Work Email
              </label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                placeholder="<EMAIL>"
                className={`w-full px-4 py-2 bg-gray-100 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  errors.email ? "border-red-500" : "border-gray-200"
                }`}
              />
              {errors.email && (
                <p className="text-red-500 text-xs mt-1">{errors.email}</p>
              )}
            </div>

            <div>
              <label
                htmlFor="password"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Create Password
              </label>
              <div className="relative">
                <input
                  type={showPassword ? "text" : "password"}
                  id="password"
                  name="password"
                  value={formData.password}
                  onChange={handleInputChange}
                  placeholder="••••••••••••"
                  className={`w-full px-4 py-2 bg-gray-100 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.password ? "border-red-500" : "border-gray-200"
                  }`}
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-500 hover:text-gray-700"
                  onClick={togglePasswordVisibility}
                  aria-label={showPassword ? "Hide password" : "Show password"}
                >
                  {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                </button>
              </div>
              {errors.password && (
                <p className="text-red-500 text-xs mt-1">{errors.password}</p>
              )}
            </div>

            <div>
              <label
                htmlFor="confirmPassword"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Confirm Password
              </label>
              <div className="relative">
                <input
                  type={showConfirmPassword ? "text" : "password"}
                  id="confirmPassword"
                  name="confirmPassword"
                  value={formData.confirmPassword}
                  onChange={handleInputChange}
                  placeholder="••••••••••••"
                  className={`w-full px-4 py-2 bg-gray-100 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.confirmPassword
                      ? "border-red-500"
                      : "border-gray-200"
                  }`}
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-500 hover:text-gray-700"
                  onClick={toggleConfirmPasswordVisibility}
                  aria-label={
                    showConfirmPassword ? "Hide password" : "Show password"
                  }
                >
                  {showConfirmPassword ? (
                    <EyeOff size={18} />
                  ) : (
                    <Eye size={18} />
                  )}
                </button>
              </div>
              {errors.confirmPassword && (
                <p className="text-red-500 text-xs mt-1">
                  {errors.confirmPassword}
                </p>
              )}
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="terms"
                name="agreedToTerms"
                checked={formData.agreedToTerms}
                onChange={handleInputChange}
                className="mr-2"
              />
              <label htmlFor="terms" className="text-sm text-gray-700">
                I agree to the{" "}
                <a href="#" className="text-blue-500">
                  Terms of Service
                </a>{" "}
                and{" "}
                <a href="#" className="text-blue-500">
                  Privacy Policy
                </a>
              </label>
            </div>
            {errors.agreedToTerms && (
              <p className="text-red-500 text-xs">{errors.agreedToTerms}</p>
            )}

            <button
              type="submit"
              className="w-full bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            >
              Create Organization Account
            </button>

            <div className="text-center text-sm">
              <span className="text-gray-600">Already have an account? </span>
              <a href="/login" className="text-blue-500">
                Sign in
              </a>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}

export default OrganizationSignupForm;
