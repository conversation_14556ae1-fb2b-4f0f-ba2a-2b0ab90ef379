import React, { useState } from "react";
import { useNavigate, Link } from "react-router-dom";
import { useApp } from "../../context/AppContext";
import { authService, userService } from "../../services";
import { useToast } from "../common/NotificationContainer";
import {
  User,
  Mail,
  Lock,
  Eye,
  EyeOff,
  Phone,
  Briefcase,
  Building,
  Users,
  CheckCircle,
  AlertCircle,
  ArrowRight,
} from "lucide-react";
import { ButtonLoader } from "../common/LoadingSpinner";

const EnhancedSignupForm = () => {
  const navigate = useNavigate();
  const { actions: appActions } = useApp();
  const toast = useToast();

  const [step, setStep] = useState(1);
  const [userType, setUserType] = useState("individual"); // 'individual' or 'organization'
  const [formData, setFormData] = useState({
    // Personal Info
    first_name: "",
    last_name: "",
    email: "",
    phone_number: "",
    profession: "",

    // Organization Info (if applicable)
    organization_name: "",
    organization_size: "",
    industry: "",

    // Account Info
    password: "",
    confirm_password: "",

    // Agreements
    terms_accepted: false,
    privacy_accepted: false,
    marketing_emails: false,
  });

  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const [passwordStrength, setPasswordStrength] = useState(0);

  const validateStep1 = () => {
    const newErrors = {};

    if (!formData.first_name.trim()) {
      newErrors.first_name = "First name is required";
    }

    if (!formData.last_name.trim()) {
      newErrors.last_name = "Last name is required";
    }

    if (!formData.email.trim()) {
      newErrors.email = "Email is required";
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "Please enter a valid email address";
    }

    if (!formData.profession.trim()) {
      newErrors.profession = "Profession is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const validateStep2 = () => {
    const newErrors = {};

    if (userType === "organization") {
      if (!formData.organization_name.trim()) {
        newErrors.organization_name = "Organization name is required";
      }

      if (!formData.organization_size) {
        newErrors.organization_size = "Organization size is required";
      }

      if (!formData.industry) {
        newErrors.industry = "Industry is required";
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const validateStep3 = () => {
    const newErrors = {};

    if (!formData.password) {
      newErrors.password = "Password is required";
    } else if (formData.password.length < 8) {
      newErrors.password = "Password must be at least 8 characters";
    } else if (passwordStrength < 3) {
      newErrors.password = "Password is too weak";
    }

    if (!formData.confirm_password) {
      newErrors.confirm_password = "Please confirm your password";
    } else if (formData.password !== formData.confirm_password) {
      newErrors.confirm_password = "Passwords do not match";
    }

    if (!formData.terms_accepted) {
      newErrors.terms_accepted = "You must accept the terms and conditions";
    }

    if (!formData.privacy_accepted) {
      newErrors.privacy_accepted = "You must accept the privacy policy";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const calculatePasswordStrength = (password) => {
    let strength = 0;

    if (password.length >= 8) strength++;
    if (/[a-z]/.test(password)) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/[0-9]/.test(password)) strength++;
    if (/[^A-Za-z0-9]/.test(password)) strength++;

    return strength;
  };

  const handlePasswordChange = (password) => {
    setFormData((prev) => ({ ...prev, password }));
    setPasswordStrength(calculatePasswordStrength(password));
  };

  const handleNext = () => {
    let isValid = false;

    switch (step) {
      case 1:
        isValid = validateStep1();
        break;
      case 2:
        isValid = validateStep2();
        break;
      default:
        isValid = true;
    }

    if (isValid) {
      setStep(step + 1);
    }
  };

  const handleBack = () => {
    setStep(step - 1);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateStep3()) return;

    try {
      setLoading(true);
      setErrors({});

      const signupData = {
        email: formData.email,
        password: formData.password,
        confirm_password: formData.confirm_password,
        first_name: formData.first_name,
        last_name: formData.last_name,
        profession: formData.profession,
        phone_number: formData.phone_number,
        user_type:
          userType === "organization" ? "organization_owner" : "individual",
      };

      // Add organization data if applicable
      if (userType === "organization") {
        signupData.organization_name = formData.organization_name;
        signupData.organization_size = formData.organization_size;
        signupData.industry = formData.industry;
      }

      const response =
        userType === "organization"
          ? await authService.registerOrganization(signupData)
          : await authService.registerIndividual(signupData);

      // Load user data
      const userData = await userService.getCurrentUser();
      appActions.setUser(userData.data);

      toast.success(
        "Account Created!",
        "Welcome to JechSpace! Your account has been created successfully."
      );

      // Redirect based on user type
      const redirectPath =
        userType === "organization" ? "/admin/dashboard" : "/user/dashboard";

      navigate(redirectPath, { replace: true });
    } catch (error) {
      console.error("Signup error:", error);

      const errorMessage =
        error.message || "Failed to create account. Please try again.";
      setErrors({ general: errorMessage });
      toast.error("Signup Failed", errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const PasswordStrengthIndicator = () => {
    const getStrengthColor = () => {
      switch (passwordStrength) {
        case 0:
        case 1:
          return "bg-red-500";
        case 2:
          return "bg-yellow-500";
        case 3:
          return "bg-blue-500";
        case 4:
        case 5:
          return "bg-green-500";
        default:
          return "bg-gray-300";
      }
    };

    const getStrengthText = () => {
      switch (passwordStrength) {
        case 0:
        case 1:
          return "Weak";
        case 2:
          return "Fair";
        case 3:
          return "Good";
        case 4:
        case 5:
          return "Strong";
        default:
          return "";
      }
    };

    return (
      <div className="mt-2">
        <div className="flex space-x-1">
          {[1, 2, 3, 4, 5].map((level) => (
            <div
              key={level}
              className={`h-1 flex-1 rounded ${
                level <= passwordStrength ? getStrengthColor() : "bg-gray-200"
              }`}
            />
          ))}
        </div>
        <p className="text-xs text-gray-600 mt-1">
          Password strength: {getStrengthText()}
        </p>
      </div>
    );
  };

  const StepIndicator = () => (
    <div className="flex items-center justify-center mb-8">
      {[1, 2, 3].map((stepNumber) => (
        <React.Fragment key={stepNumber}>
          <div
            className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${
              step >= stepNumber
                ? "bg-blue-600 border-blue-600 text-white"
                : "border-gray-300 text-gray-300"
            }`}
          >
            {step > stepNumber ? (
              <CheckCircle className="w-5 h-5" />
            ) : (
              <span className="text-sm font-medium">{stepNumber}</span>
            )}
          </div>
          {stepNumber < 3 && (
            <div
              className={`w-12 h-0.5 ${
                step > stepNumber ? "bg-blue-600" : "bg-gray-300"
              }`}
            />
          )}
        </React.Fragment>
      ))}
    </div>
  );

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        {/* Header */}
        <div>
          <div className="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-blue-100">
            <User className="h-6 w-6 text-blue-600" />
          </div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Create your account
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            Already have an account?{" "}
            <Link
              to="/login"
              className="font-medium text-blue-600 hover:text-blue-500"
            >
              Sign in here
            </Link>
          </p>
        </div>

        <StepIndicator />

        {/* General Error */}
        {errors.general && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <AlertCircle className="h-5 w-5 text-red-400" />
              <div className="ml-3">
                <p className="text-sm text-red-700">{errors.general}</p>
              </div>
            </div>
          </div>
        )}

        <form onSubmit={handleSubmit} className="mt-8 space-y-6">
          {/* Step 1: Personal Information */}
          {step === 1 && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Personal Information
              </h3>

              {/* User Type Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Account Type
                </label>
                <div className="grid grid-cols-2 gap-3">
                  <button
                    type="button"
                    onClick={() => setUserType("individual")}
                    className={`flex items-center justify-center p-3 border rounded-md ${
                      userType === "individual"
                        ? "border-blue-500 bg-blue-50 text-blue-700"
                        : "border-gray-300 text-gray-700 hover:bg-gray-50"
                    }`}
                  >
                    <User className="h-5 w-5 mr-2" />
                    Individual
                  </button>
                  <button
                    type="button"
                    onClick={() => setUserType("organization")}
                    className={`flex items-center justify-center p-3 border rounded-md ${
                      userType === "organization"
                        ? "border-blue-500 bg-blue-50 text-blue-700"
                        : "border-gray-300 text-gray-700 hover:bg-gray-50"
                    }`}
                  >
                    <Building className="h-5 w-5 mr-2" />
                    Organization
                  </button>
                </div>
              </div>

              {/* Name Fields */}
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <label
                    htmlFor="first_name"
                    className="block text-sm font-medium text-gray-700"
                  >
                    First Name
                  </label>
                  <input
                    id="first_name"
                    name="first_name"
                    type="text"
                    value={formData.first_name}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        first_name: e.target.value,
                      }))
                    }
                    className={`mt-1 appearance-none relative block w-full px-3 py-2 border ${
                      errors.first_name ? "border-red-300" : "border-gray-300"
                    } placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm`}
                    placeholder="John"
                  />
                  {errors.first_name && (
                    <p className="mt-1 text-sm text-red-600">
                      {errors.first_name}
                    </p>
                  )}
                </div>
                <div>
                  <label
                    htmlFor="last_name"
                    className="block text-sm font-medium text-gray-700"
                  >
                    Last Name
                  </label>
                  <input
                    id="last_name"
                    name="last_name"
                    type="text"
                    value={formData.last_name}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        last_name: e.target.value,
                      }))
                    }
                    className={`mt-1 appearance-none relative block w-full px-3 py-2 border ${
                      errors.last_name ? "border-red-300" : "border-gray-300"
                    } placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm`}
                    placeholder="Doe"
                  />
                  {errors.last_name && (
                    <p className="mt-1 text-sm text-red-600">
                      {errors.last_name}
                    </p>
                  )}
                </div>
              </div>

              {/* Email */}
              <div>
                <label
                  htmlFor="email"
                  className="block text-sm font-medium text-gray-700"
                >
                  Email Address
                </label>
                <div className="mt-1 relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Mail className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    id="email"
                    name="email"
                    type="email"
                    autoComplete="email"
                    value={formData.email}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        email: e.target.value,
                      }))
                    }
                    className={`appearance-none relative block w-full pl-10 pr-3 py-2 border ${
                      errors.email ? "border-red-300" : "border-gray-300"
                    } placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm`}
                    placeholder="<EMAIL>"
                  />
                </div>
                {errors.email && (
                  <p className="mt-1 text-sm text-red-600">{errors.email}</p>
                )}
              </div>

              {/* Phone */}
              <div>
                <label
                  htmlFor="phone_number"
                  className="block text-sm font-medium text-gray-700"
                >
                  Phone Number (Optional)
                </label>
                <div className="mt-1 relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Phone className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    id="phone_number"
                    name="phone_number"
                    type="tel"
                    value={formData.phone_number}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        phone_number: e.target.value,
                      }))
                    }
                    className="appearance-none relative block w-full pl-10 pr-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    placeholder="+****************"
                  />
                </div>
              </div>

              {/* Profession */}
              <div>
                <label
                  htmlFor="profession"
                  className="block text-sm font-medium text-gray-700"
                >
                  Profession
                </label>
                <div className="mt-1 relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Briefcase className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    id="profession"
                    name="profession"
                    type="text"
                    value={formData.profession}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        profession: e.target.value,
                      }))
                    }
                    className={`appearance-none relative block w-full pl-10 pr-3 py-2 border ${
                      errors.profession ? "border-red-300" : "border-gray-300"
                    } placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm`}
                    placeholder="Software Developer"
                  />
                </div>
                {errors.profession && (
                  <p className="mt-1 text-sm text-red-600">
                    {errors.profession}
                  </p>
                )}
              </div>

              <button
                type="button"
                onClick={handleNext}
                className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Next Step
                <ArrowRight className="ml-2 h-4 w-4" />
              </button>
            </div>
          )}

          {/* Step 2: Organization Information (if applicable) */}
          {step === 2 && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                {userType === "organization"
                  ? "Organization Information"
                  : "Additional Information"}
              </h3>

              {userType === "organization" ? (
                <>
                  {/* Organization Name */}
                  <div>
                    <label
                      htmlFor="organization_name"
                      className="block text-sm font-medium text-gray-700"
                    >
                      Organization Name
                    </label>
                    <div className="mt-1 relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <Building className="h-5 w-5 text-gray-400" />
                      </div>
                      <input
                        id="organization_name"
                        name="organization_name"
                        type="text"
                        value={formData.organization_name}
                        onChange={(e) =>
                          setFormData((prev) => ({
                            ...prev,
                            organization_name: e.target.value,
                          }))
                        }
                        className={`appearance-none relative block w-full pl-10 pr-3 py-2 border ${
                          errors.organization_name
                            ? "border-red-300"
                            : "border-gray-300"
                        } placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm`}
                        placeholder="Acme Corporation"
                      />
                    </div>
                    {errors.organization_name && (
                      <p className="mt-1 text-sm text-red-600">
                        {errors.organization_name}
                      </p>
                    )}
                  </div>

                  {/* Organization Size */}
                  <div>
                    <label
                      htmlFor="organization_size"
                      className="block text-sm font-medium text-gray-700"
                    >
                      Organization Size
                    </label>
                    <div className="mt-1 relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <Users className="h-5 w-5 text-gray-400" />
                      </div>
                      <select
                        id="organization_size"
                        name="organization_size"
                        value={formData.organization_size}
                        onChange={(e) =>
                          setFormData((prev) => ({
                            ...prev,
                            organization_size: e.target.value,
                          }))
                        }
                        className={`appearance-none relative block w-full pl-10 pr-3 py-2 border ${
                          errors.organization_size
                            ? "border-red-300"
                            : "border-gray-300"
                        } text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm`}
                      >
                        <option value="">Select organization size</option>
                        <option value="1-10">1-10 employees</option>
                        <option value="11-50">11-50 employees</option>
                        <option value="51-200">51-200 employees</option>
                        <option value="201-500">201-500 employees</option>
                        <option value="501-1000">501-1000 employees</option>
                        <option value="1000+">1000+ employees</option>
                      </select>
                    </div>
                    {errors.organization_size && (
                      <p className="mt-1 text-sm text-red-600">
                        {errors.organization_size}
                      </p>
                    )}
                  </div>

                  {/* Industry */}
                  <div>
                    <label
                      htmlFor="industry"
                      className="block text-sm font-medium text-gray-700"
                    >
                      Industry
                    </label>
                    <select
                      id="industry"
                      name="industry"
                      value={formData.industry}
                      onChange={(e) =>
                        setFormData((prev) => ({
                          ...prev,
                          industry: e.target.value,
                        }))
                      }
                      className={`mt-1 appearance-none relative block w-full px-3 py-2 border ${
                        errors.industry ? "border-red-300" : "border-gray-300"
                      } text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm`}
                    >
                      <option value="">Select industry</option>
                      <option value="technology">Technology</option>
                      <option value="finance">Finance</option>
                      <option value="healthcare">Healthcare</option>
                      <option value="education">Education</option>
                      <option value="retail">Retail</option>
                      <option value="manufacturing">Manufacturing</option>
                      <option value="consulting">Consulting</option>
                      <option value="media">Media & Entertainment</option>
                      <option value="nonprofit">Non-profit</option>
                      <option value="government">Government</option>
                      <option value="other">Other</option>
                    </select>
                    {errors.industry && (
                      <p className="mt-1 text-sm text-red-600">
                        {errors.industry}
                      </p>
                    )}
                  </div>
                </>
              ) : (
                <div className="text-center py-8">
                  <CheckCircle className="mx-auto h-12 w-12 text-green-500" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">
                    Personal information completed
                  </h3>
                  <p className="mt-1 text-sm text-gray-500">
                    Ready to set up your account security
                  </p>
                </div>
              )}

              <div className="flex space-x-3">
                <button
                  type="button"
                  onClick={handleBack}
                  className="flex-1 flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Back
                </button>
                <button
                  type="button"
                  onClick={handleNext}
                  className="flex-1 flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Next Step
                  <ArrowRight className="ml-2 h-4 w-4" />
                </button>
              </div>
            </div>
          )}

          {/* Step 3: Account Security */}
          {step === 3 && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Account Security
              </h3>

              {/* Password */}
              <div>
                <label
                  htmlFor="password"
                  className="block text-sm font-medium text-gray-700"
                >
                  Password
                </label>
                <div className="mt-1 relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Lock className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    id="password"
                    name="password"
                    type={showPassword ? "text" : "password"}
                    value={formData.password}
                    onChange={(e) => handlePasswordChange(e.target.value)}
                    className={`appearance-none relative block w-full pl-10 pr-10 py-2 border ${
                      errors.password ? "border-red-300" : "border-gray-300"
                    } placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm`}
                    placeholder="Create a strong password"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  >
                    {showPassword ? (
                      <EyeOff className="h-5 w-5 text-gray-400 hover:text-gray-600" />
                    ) : (
                      <Eye className="h-5 w-5 text-gray-400 hover:text-gray-600" />
                    )}
                  </button>
                </div>
                {formData.password && <PasswordStrengthIndicator />}
                {errors.password && (
                  <p className="mt-1 text-sm text-red-600">{errors.password}</p>
                )}
              </div>

              {/* Confirm Password */}
              <div>
                <label
                  htmlFor="confirm_password"
                  className="block text-sm font-medium text-gray-700"
                >
                  Confirm Password
                </label>
                <div className="mt-1 relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Lock className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    id="confirm_password"
                    name="confirm_password"
                    type={showConfirmPassword ? "text" : "password"}
                    value={formData.confirm_password}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        confirm_password: e.target.value,
                      }))
                    }
                    className={`appearance-none relative block w-full pl-10 pr-10 py-2 border ${
                      errors.confirm_password
                        ? "border-red-300"
                        : "border-gray-300"
                    } placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm`}
                    placeholder="Confirm your password"
                  />
                  <button
                    type="button"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  >
                    {showConfirmPassword ? (
                      <EyeOff className="h-5 w-5 text-gray-400 hover:text-gray-600" />
                    ) : (
                      <Eye className="h-5 w-5 text-gray-400 hover:text-gray-600" />
                    )}
                  </button>
                </div>
                {errors.confirm_password && (
                  <p className="mt-1 text-sm text-red-600">
                    {errors.confirm_password}
                  </p>
                )}
              </div>

              {/* Terms and Conditions */}
              <div className="space-y-3">
                <div className="flex items-start">
                  <input
                    id="terms_accepted"
                    name="terms_accepted"
                    type="checkbox"
                    checked={formData.terms_accepted}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        terms_accepted: e.target.checked,
                      }))
                    }
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mt-1"
                  />
                  <label
                    htmlFor="terms_accepted"
                    className="ml-2 block text-sm text-gray-900"
                  >
                    I agree to the{" "}
                    <Link
                      to="/terms"
                      className="text-blue-600 hover:text-blue-500"
                    >
                      Terms and Conditions
                    </Link>
                  </label>
                </div>
                {errors.terms_accepted && (
                  <p className="text-sm text-red-600">
                    {errors.terms_accepted}
                  </p>
                )}

                <div className="flex items-start">
                  <input
                    id="privacy_accepted"
                    name="privacy_accepted"
                    type="checkbox"
                    checked={formData.privacy_accepted}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        privacy_accepted: e.target.checked,
                      }))
                    }
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mt-1"
                  />
                  <label
                    htmlFor="privacy_accepted"
                    className="ml-2 block text-sm text-gray-900"
                  >
                    I agree to the{" "}
                    <Link
                      to="/privacy"
                      className="text-blue-600 hover:text-blue-500"
                    >
                      Privacy Policy
                    </Link>
                  </label>
                </div>
                {errors.privacy_accepted && (
                  <p className="text-sm text-red-600">
                    {errors.privacy_accepted}
                  </p>
                )}

                <div className="flex items-start">
                  <input
                    id="marketing_emails"
                    name="marketing_emails"
                    type="checkbox"
                    checked={formData.marketing_emails}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        marketing_emails: e.target.checked,
                      }))
                    }
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mt-1"
                  />
                  <label
                    htmlFor="marketing_emails"
                    className="ml-2 block text-sm text-gray-900"
                  >
                    I would like to receive marketing emails and product updates
                    (optional)
                  </label>
                </div>
              </div>

              <div className="flex space-x-3">
                <button
                  type="button"
                  onClick={handleBack}
                  className="flex-1 flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Back
                </button>
                <ButtonLoader
                  loading={loading}
                  type="submit"
                  className="flex-1 flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Create Account
                </ButtonLoader>
              </div>
            </div>
          )}
        </form>
      </div>
    </div>
  );
};

export default EnhancedSignupForm;
