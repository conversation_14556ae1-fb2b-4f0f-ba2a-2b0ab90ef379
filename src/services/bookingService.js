import api from "./api";
import {
  getApiEndpointUrl,
  formatApiError,
  retryApiCall,
} from "../utils/apiUtils";

/**
 * Service for handling booking operations with environment-aware endpoints
 */
const bookingService = {
  /**
   * Get all bookings
   * @param {Object} params - Query parameters
   * @returns {Promise} - Response from API
   */
  getAllBookings: async (params = {}) => {
    try {
      const response = await api.get(getApiEndpointUrl("bookings", "list"), {
        params,
      });
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  /**
   * Get booking by ID
   * @param {string} id - Booking ID
   * @returns {Promise} - Response from API
   */
  getBookingById: async (id) => {
    try {
      const response = await api.get(`/bookings/${id}/`);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  /**
   * Create a new booking
   * @param {Object} bookingData - Booking data
   * @returns {Promise} - Response from API
   */
  createBooking: async (bookingData) => {
    try {
      const response = await api.post(
        getApiEndpointUrl("bookings", "create"),
        bookingData
      );
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  /**
   * Update a booking
   * @param {string} id - Booking ID
   * @param {Object} bookingData - Updated booking data
   * @returns {Promise} - Response from API
   */
  updateBooking: async (id, bookingData) => {
    try {
      const response = await api.put(`/bookings/${id}/`, bookingData);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  /**
   * Cancel a booking
   * @param {string} id - Booking ID
   * @returns {Promise} - Response from API
   */
  cancelBooking: async (id) => {
    try {
      const response = await api.post(`/bookings/${id}/cancel/`);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  /**
   * Get user's bookings
   * @param {Object} params - Query parameters
   * @returns {Promise} - Response from API
   */
  getUserBookings: async (params = {}) => {
    try {
      const response = await api.get(
        getApiEndpointUrl("bookings", "userBookings"),
        { params }
      );
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  /**
   * Extend a booking
   * @param {string} id - Booking ID
   * @param {Object} data - Extension data
   * @param {string} data.end_time - New end time
   * @returns {Promise} - Response from API
   */
  extendBooking: async (id, data) => {
    try {
      const response = await api.post(`/bookings/${id}/extend/`, data);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },
};

export default bookingService;
