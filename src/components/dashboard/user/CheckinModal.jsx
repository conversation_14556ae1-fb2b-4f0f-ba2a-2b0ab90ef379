import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { X, Check } from "lucide-react";
import { QRCodeCanvas } from "qrcode.react";

const CheckinModal = ({ isOpen, onClose, bookingData }) => {
  const [isScanned, setIsScanned] = useState(false);

  // For demo purposes - in real app, this would be triggered by actual QR scan
  useEffect(() => {
    if (isScanned) {
      // Reset after animation completes
      const timer = setTimeout(() => {
        setIsScanned(false);
        onClose();
      }, 3000);

      return () => clearTimeout(timer);
    }
  }, [isScanned, onClose]);

  // Function to simulate QR code scan (for demo purposes)
  const simulateScan = () => {
    setIsScanned(true);
  };

  // Create QR code data from booking information
  const qrData = bookingData
    ? JSON.stringify({
        bookingId: bookingData.id,
        userId: bookingData.userId,
        spaceName: bookingData.spaceName,
        date: bookingData.date,
        timeSlot: bookingData.timeSlot,
      })
    : "";

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0  bg-black/60  bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50"
          onClick={(e) => {
            if (e.target === e.currentTarget) onClose();
          }}
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            className="bg-white rounded-lg shadow-xl p-6 w-80 max-w-md relative"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Close button */}
            <button
              onClick={onClose}
              className="absolute top-4 right-4 text-gray-500 hover:text-gray-700"
            >
              <X size={20} />
            </button>

            {/* Modal content */}
            <div className="text-center">
              <h2 className="text-xl font-semibold mb-4">Check-in</h2>

              {!isScanned ? (
                <>
                  <p className="text-gray-600 mb-6">
                    Show this QR code at the front desk to check in to your
                    workspace.
                  </p>

                  {/* QR Code */}
                  <div className="flex justify-center mb-6">
                    <div className="border-8 border-white shadow-lg rounded-lg">
                      <QRCodeCanvas
                        value={qrData}
                        size={200}
                        level="H"
                        renderAs="svg"
                      />
                    </div>
                  </div>

                  {/* Booking details */}
                  {bookingData && (
                    <div className="bg-gray-50 p-4 rounded-lg text-left mb-4">
                      <p className="font-medium">{bookingData.spaceName}</p>
                      <p className="text-sm text-gray-600">
                        {bookingData.date}
                      </p>
                      <p className="text-sm text-gray-600">
                        {bookingData.timeSlot}
                      </p>
                    </div>
                  )}

                  {/* Demo button to simulate scan */}
                  <button
                    onClick={simulateScan}
                    className="mt-2 text-sm text-blue-600 hover:underline"
                  >
                    (Demo: Simulate Scan)
                  </button>
                </>
              ) : (
                <motion.div
                  initial={{ scale: 0.5, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  className="py-8 flex flex-col items-center"
                >
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ delay: 0.2, type: "spring" }}
                    className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mb-4"
                  >
                    <Check className="text-green-600" size={40} />
                  </motion.div>
                  <h3 className="text-xl font-medium text-green-600 mb-2">
                    Successfully Checked In!
                  </h3>
                  <p className="text-gray-600">
                    Welcome to your workspace. Enjoy your day!
                  </p>
                </motion.div>
              )}
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default CheckinModal;
