import React from "react";
import { useNavigate } from "react-router-dom";

const OrganizationConfirmation = () => {
  const navigate = useNavigate();

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-white p-4">
      <div className="max-w-md w-full text-center">
        <div className="mb-8 flex justify-center">
          <img
            src="/images/logo-blue.png"
            alt="JechSpace Logo"
            className="h-10 object-contain"
          />
        </div>

        <div className="bg-blue-50 rounded-2xl p-8 mb-8 flex flex-col items-center">
          <div className="bg-blue-500 rounded-full p-4 mb-4">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-8 w-8 text-white"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M5 13l4 4L19 7"
              />
            </svg>
          </div>

          <h1 className="text-2xl font-bold text-gray-800 mb-2">
            Organization Registration Successful
          </h1>
          <p className="text-gray-600 mb-4">
            We've sent a confirmation link to your organization email. Please
            verify your account to continue with the onboarding process.
          </p>
        </div>

        <div className="text-sm text-gray-600 mb-8">
          <p className="mb-1">Didn't receive the email?</p>
          <p>
            Check your spam folder or{" "}
            <button className="text-blue-500 hover:underline">
              resend verification link
            </button>
          </p>
        </div>

        <div className="space-y-4">
          <button
            onClick={() => navigate("/admin/onboarding/step1")}
            className="bg-blue-500 hover:bg-blue-600 text-white py-2 px-6 w-full rounded-md transition-all duration-200"
          >
            Begin Onboarding Process
          </button>

          <button
            onClick={() => navigate("/admin/dashboard")}
            className="bg-white hover:bg-gray-50 text-blue-500 border border-blue-500 py-2 px-6 w-full rounded-md transition-all duration-200"
          >
            Skip for Now
          </button>
        </div>
      </div>
    </div>
  );
};

export default OrganizationConfirmation;
