import React, { useState, useEffect } from "react";
import {
  Calendar,
  Plus,
  Edit,
  Trash2,
  Save,
  X,
  Info,
  ChevronLeft,
  ChevronRight,
  Users,
  Clock,
  DollarSign,
  Image,
  Settings,
  AlertCircle,
} from "lucide-react";
import { spaceService, bookingService } from "../../services";

const SpaceList = () => {
  // State variables
  const [spaces, setSpaces] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch spaces from API
  useEffect(() => {
    const fetchSpaces = async () => {
      try {
        setLoading(true);
        const response = await spaceService.getAllSpaces();

        // Transform API data to match component's expected format
        const formattedSpaces = response.data.map((space) => ({
          id: space.id,
          name: space.name,
          capacity: space.capacity,
          availability: space.is_active ? "Available" : "Maintenance",
          rate: space.hourly_rate || 0,
          features: space.amenities || [],
          description: space.description || "",
          type: space.type,
          floor: space.floor,
          room: space.room,
        }));

        setSpaces(formattedSpaces);
      } catch (err) {
        console.error("Error fetching spaces:", err);
        setError("Failed to load spaces. Please try again later.");

        // Fallback to sample data if API fails
        setSpaces([
          {
            id: 1,
            name: "Conference Room A",
            capacity: 12,
            availability: "Available",
            rate: 25,
            features: ["Projector", "Whiteboard"],
            description: "Large conference room for meetings",
          },
          {
            id: 2,
            name: "Meeting Room B",
            capacity: 6,
            availability: "Booked",
            rate: 15,
            features: ["TV Screen", "Conference Phone"],
            description: "Medium-sized meeting room",
          },
          {
            id: 3,
            name: "Studio Space",
            capacity: 20,
            availability: "Available",
            rate: 40,
            features: ["Sound System", "Open Floor"],
            description: "Open space for creative activities",
          },
          {
            id: 4,
            name: "Private Office 1",
            capacity: 2,
            availability: "Maintenance",
            rate: 20,
            features: ["Desk", "Private Phone Line"],
            description: "Small private office space",
          },
        ]);
      } finally {
        setLoading(false);
      }
    };

    fetchSpaces();
  }, []);
  const [activeTab, setActiveTab] = useState("list");
  const [editingSpace, setEditingSpace] = useState(null);
  const [newSpace, setNewSpace] = useState({
    name: "",
    capacity: 0,
    availability: "Available",
    rate: 0,
    features: [],
    description: "",
    image: "/api/placeholder/400/320",
    minBookingHours: 1,
    maxBookingHours: 8,
    openTime: "08:00",
    closeTime: "18:00",
    daysAvailable: ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"],
  });
  const [selectedSpace, setSelectedSpace] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [newFeature, setNewFeature] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const [filterAvailability, setFilterAvailability] = useState("All");
  const [currentDate, setCurrentDate] = useState(new Date());
  const [showSettingsModal, setShowSettingsModal] = useState(false);
  const [bulkActionMode, setBulkActionMode] = useState(false);
  const [selectedSpaces, setSelectedSpaces] = useState([]);
  const [bookings, setBookings] = useState([]);

  // Fetch bookings from API
  useEffect(() => {
    const fetchBookings = async () => {
      try {
        const response = await bookingService.getAllBookings();

        // Transform API data to match component's expected format
        const formattedBookings = response.data.map((booking) => ({
          id: booking.id,
          spaceId: booking.space_id,
          userName: booking.user_name,
          startTime: booking.start_time,
          endTime: booking.end_time,
          status:
            booking.status.charAt(0).toUpperCase() + booking.status.slice(1),
          notes: booking.notes || "",
        }));

        setBookings(formattedBookings);
      } catch (err) {
        console.error("Error fetching bookings:", err);

        // Fallback to sample data if API fails
        setBookings([
          {
            id: 1,
            spaceId: 2,
            userName: "John Smith",
            startTime: "2025-05-06T10:00",
            endTime: "2025-05-06T12:00",
            status: "Confirmed",
          },
          {
            id: 2,
            spaceId: 1,
            userName: "Sarah Johnson",
            startTime: "2025-05-07T14:00",
            endTime: "2025-05-07T16:00",
            status: "Pending",
          },
          {
            id: 3,
            spaceId: 3,
            userName: "Michael Brown",
            startTime: "2025-05-08T09:00",
            endTime: "2025-05-08T17:00",
            status: "Confirmed",
          },
        ]);
      }
    };

    fetchBookings();
  }, []);
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState({ type: "", message: "" });
  const [viewMode, setViewMode] = useState("table"); // 'table' or 'grid'
  const [selectedDateBookings, setSelectedDateBookings] = useState([]);
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [showCreateBookingModal, setShowCreateBookingModal] = useState(false);
  const [newBooking, setNewBooking] = useState({
    spaceId: null,
    userName: "",
    startTime: "",
    endTime: "",
    status: "Pending",
    notes: "",
  });

  // Display toast notification
  const displayToast = (type, message) => {
    setToastMessage({ type, message });
    setShowToast(true);

    // Auto-hide toast after 3 seconds
    setTimeout(() => {
      setShowToast(false);
    }, 3000);
  };

  // Handle editing a space
  const handleEdit = (space) => {
    setEditingSpace({ ...space });
    setShowModal(true);
  };

  // Handle deleting a space
  const handleDelete = async (id) => {
    if (confirm("Are you sure you want to delete this space?")) {
      try {
        await spaceService.deleteSpace(id);
        setSpaces(spaces.filter((space) => space.id !== id));
        displayToast("success", "Space deleted successfully");
        return true;
      } catch (error) {
        console.error("Error deleting space:", error);
        displayToast(
          "error",
          error.message || "Failed to delete space. Please try again."
        );
        return false;
      }
    }
    return false;
  };

  // Save edited space
  const handleSave = async () => {
    const currentSpace = editingSpace || newSpace;

    // Basic validation
    if (!currentSpace.name.trim()) {
      displayToast("error", "Space name is required");
      return;
    }

    try {
      // Format data for API
      const spaceData = {
        name: currentSpace.name,
        capacity: currentSpace.capacity,
        description: currentSpace.description,
        type: currentSpace.type || "room",
        floor: currentSpace.floor || "1",
        room: currentSpace.room || "",
        is_active: currentSpace.availability === "Available",
        amenities: currentSpace.features,
      };

      if (editingSpace) {
        // Update existing space
        await spaceService.updateSpace(editingSpace.id, spaceData);
        setSpaces(
          spaces.map((space) =>
            space.id === editingSpace.id
              ? {
                  ...space,
                  ...spaceData,
                  availability: spaceData.is_active
                    ? "Available"
                    : "Maintenance",
                }
              : space
          )
        );
        setEditingSpace(null);
        displayToast("success", "Space updated successfully");
      } else {
        // Create new space
        const response = await spaceService.createSpace(spaceData);
        const newSpace = {
          id: response.data.id,
          name: response.data.name,
          capacity: response.data.capacity,
          availability: response.data.is_active ? "Available" : "Maintenance",
          rate: response.data.hourly_rate || 0,
          features: response.data.amenities || [],
          description: response.data.description || "",
          type: response.data.type,
          floor: response.data.floor,
          room: response.data.room,
        };

        setSpaces([...spaces, newSpace]);
        setNewSpace({
          name: "",
          capacity: 0,
          availability: "Available",
          rate: 0,
          features: [],
          description: "",
          image: "/api/placeholder/400/320",
          minBookingHours: 1,
          maxBookingHours: 8,
          openTime: "08:00",
          closeTime: "18:00",
          daysAvailable: [
            "Monday",
            "Tuesday",
            "Wednesday",
            "Thursday",
            "Friday",
          ],
        });
        displayToast("success", "New space created successfully");
      }
    } catch (error) {
      console.error("Error saving space:", error);
      displayToast(
        "error",
        error.message || "Failed to save space. Please try again."
      );
    }

    setShowModal(false);
  };

  // Add a new feature to a space
  const handleAddFeature = () => {
    if (newFeature.trim() !== "") {
      if (editingSpace) {
        setEditingSpace({
          ...editingSpace,
          features: [...editingSpace.features, newFeature.trim()],
        });
      } else {
        setNewSpace({
          ...newSpace,
          features: [...newSpace.features, newFeature.trim()],
        });
      }
      setNewFeature("");
    }
  };

  // Remove a feature from a space
  const handleRemoveFeature = (index) => {
    if (editingSpace) {
      const updatedFeatures = [...editingSpace.features];
      updatedFeatures.splice(index, 1);
      setEditingSpace({
        ...editingSpace,
        features: updatedFeatures,
      });
    } else {
      const updatedFeatures = [...newSpace.features];
      updatedFeatures.splice(index, 1);
      setNewSpace({
        ...newSpace,
        features: updatedFeatures,
      });
    }
  };

  // View space details
  const handleViewDetails = (space) => {
    setSelectedSpace(space);
    setActiveTab("details");
  };

  // Filter spaces based on search term and availability
  const filteredSpaces = spaces.filter((space) => {
    const matchesSearch =
      space.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      space.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesAvailability =
      filterAvailability === "All" || space.availability === filterAvailability;

    return matchesSearch && matchesAvailability;
  });

  // Space modal component
  const SpaceModal = () => {
    const currentSpace = editingSpace || newSpace;

    return (
      <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 w-full max-w-lg">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-bold">
              {editingSpace ? "Edit Space" : "Add New Space"}
            </h2>
            <button
              onClick={() => setShowModal(false)}
              className="p-1 rounded-full hover:bg-gray-200"
            >
              <X size={20} />
            </button>
          </div>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Name
              </label>
              <input
                type="text"
                className="w-full border rounded-md p-2"
                value={currentSpace.name}
                onChange={(e) =>
                  editingSpace
                    ? setEditingSpace({ ...editingSpace, name: e.target.value })
                    : setNewSpace({ ...newSpace, name: e.target.value })
                }
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Capacity
                </label>
                <input
                  type="number"
                  className="w-full border rounded-md p-2"
                  value={currentSpace.capacity}
                  onChange={(e) => {
                    const value = parseInt(e.target.value) || 0;
                    editingSpace
                      ? setEditingSpace({ ...editingSpace, capacity: value })
                      : setNewSpace({ ...newSpace, capacity: value });
                  }}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Rate ($/hr)
                </label>
                <input
                  type="number"
                  className="w-full border rounded-md p-2"
                  value={currentSpace.rate}
                  onChange={(e) => {
                    const value = parseInt(e.target.value) || 0;
                    editingSpace
                      ? setEditingSpace({ ...editingSpace, rate: value })
                      : setNewSpace({ ...newSpace, rate: value });
                  }}
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Availability
              </label>
              <select
                className="w-full border rounded-md p-2"
                value={currentSpace.availability}
                onChange={(e) =>
                  editingSpace
                    ? setEditingSpace({
                        ...editingSpace,
                        availability: e.target.value,
                      })
                    : setNewSpace({ ...newSpace, availability: e.target.value })
                }
              >
                <option value="Available">Available</option>
                <option value="Booked">Booked</option>
                <option value="Maintenance">Maintenance</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Description
              </label>
              <textarea
                className="w-full border rounded-md p-2 h-20"
                value={currentSpace.description}
                onChange={(e) =>
                  editingSpace
                    ? setEditingSpace({
                        ...editingSpace,
                        description: e.target.value,
                      })
                    : setNewSpace({ ...newSpace, description: e.target.value })
                }
              ></textarea>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Features
              </label>
              <div className="flex space-x-2 mb-2">
                <input
                  type="text"
                  className="flex-1 border rounded-md p-2"
                  value={newFeature}
                  onChange={(e) => setNewFeature(e.target.value)}
                  placeholder="Add a feature"
                  onKeyPress={(e) => {
                    if (e.key === "Enter") {
                      e.preventDefault();
                      handleAddFeature();
                    }
                  }}
                />
                <button
                  onClick={handleAddFeature}
                  className="bg-blue-500 text-white px-3 py-2 rounded-md hover:bg-blue-600"
                >
                  Add
                </button>
              </div>

              <div className="flex flex-wrap gap-2">
                {currentSpace.features.map((feature, index) => (
                  <div
                    key={index}
                    className="bg-gray-100 px-3 py-1 rounded-full flex items-center"
                  >
                    <span>{feature}</span>
                    <button
                      onClick={() => handleRemoveFeature(index)}
                      className="ml-2 text-gray-500 hover:text-red-500"
                    >
                      <X size={14} />
                    </button>
                  </div>
                ))}
                {currentSpace.features.length === 0 && (
                  <p className="text-gray-500 text-sm italic">
                    No features added yet
                  </p>
                )}
              </div>
            </div>

            <div className="border-t pt-4 mt-2">
              <h3 className="text-sm font-medium text-gray-700 mb-3">
                Advanced Settings
              </h3>

              <div className="grid grid-cols-2 gap-4 mb-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Min Booking (hrs)
                  </label>
                  <input
                    type="number"
                    className="w-full border rounded-md p-2"
                    value={currentSpace.minBookingHours || 1}
                    min="1"
                    onChange={(e) => {
                      const value = parseInt(e.target.value) || 1;
                      editingSpace
                        ? setEditingSpace({
                            ...editingSpace,
                            minBookingHours: value,
                          })
                        : setNewSpace({ ...newSpace, minBookingHours: value });
                    }}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Max Booking (hrs)
                  </label>
                  <input
                    type="number"
                    className="w-full border rounded-md p-2"
                    value={currentSpace.maxBookingHours || 8}
                    min="1"
                    onChange={(e) => {
                      const value = parseInt(e.target.value) || 8;
                      editingSpace
                        ? setEditingSpace({
                            ...editingSpace,
                            maxBookingHours: value,
                          })
                        : setNewSpace({ ...newSpace, maxBookingHours: value });
                    }}
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Open Time
                  </label>
                  <input
                    type="time"
                    className="w-full border rounded-md p-2"
                    value={currentSpace.openTime || "08:00"}
                    onChange={(e) => {
                      editingSpace
                        ? setEditingSpace({
                            ...editingSpace,
                            openTime: e.target.value,
                          })
                        : setNewSpace({
                            ...newSpace,
                            openTime: e.target.value,
                          });
                    }}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Close Time
                  </label>
                  <input
                    type="time"
                    className="w-full border rounded-md p-2"
                    value={currentSpace.closeTime || "18:00"}
                    onChange={(e) => {
                      editingSpace
                        ? setEditingSpace({
                            ...editingSpace,
                            closeTime: e.target.value,
                          })
                        : setNewSpace({
                            ...newSpace,
                            closeTime: e.target.value,
                          });
                    }}
                  />
                </div>
              </div>
            </div>
          </div>

          <div className="mt-6 flex justify-end space-x-3">
            <button
              onClick={() => setShowModal(false)}
              className="px-4 py-2 border rounded-md hover:bg-gray-100"
            >
              Cancel
            </button>
            <button
              onClick={handleSave}
              className="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600"
            >
              {editingSpace ? "Update Space" : "Create Space"}
            </button>
          </div>
        </div>
      </div>
    );
  };

  // Booking modal component
  const BookingModal = () => {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 w-full max-w-lg">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-bold">Create New Booking</h2>
            <button
              onClick={() => setShowCreateBookingModal(false)}
              className="p-1 rounded-full hover:bg-gray-200"
            >
              <X size={20} />
            </button>
          </div>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Space
              </label>
              <select
                className="w-full border rounded-md p-2"
                value={newBooking.spaceId || ""}
                onChange={(e) =>
                  setNewBooking({
                    ...newBooking,
                    spaceId: parseInt(e.target.value),
                  })
                }
              >
                <option value="">Select a space</option>
                {spaces
                  .filter((space) => space.availability === "Available")
                  .map((space) => (
                    <option key={space.id} value={space.id}>
                      {space.name}
                    </option>
                  ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                User/Client Name
              </label>
              <input
                type="text"
                className="w-full border rounded-md p-2"
                value={newBooking.userName}
                onChange={(e) =>
                  setNewBooking({ ...newBooking, userName: e.target.value })
                }
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Start Time
                </label>
                <input
                  type="datetime-local"
                  className="w-full border rounded-md p-2"
                  value={newBooking.startTime}
                  onChange={(e) =>
                    setNewBooking({ ...newBooking, startTime: e.target.value })
                  }
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  End Time
                </label>
                <input
                  type="datetime-local"
                  className="w-full border rounded-md p-2"
                  value={newBooking.endTime}
                  onChange={(e) =>
                    setNewBooking({ ...newBooking, endTime: e.target.value })
                  }
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Status
              </label>
              <select
                className="w-full border rounded-md p-2"
                value={newBooking.status}
                onChange={(e) =>
                  setNewBooking({ ...newBooking, status: e.target.value })
                }
              >
                <option value="Pending">Pending</option>
                <option value="Confirmed">Confirmed</option>
                <option value="Cancelled">Cancelled</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Notes
              </label>
              <textarea
                className="w-full border rounded-md p-2 h-20"
                value={newBooking.notes || ""}
                onChange={(e) =>
                  setNewBooking({ ...newBooking, notes: e.target.value })
                }
                placeholder="Add any special requirements or notes"
              ></textarea>
            </div>
          </div>

          <div className="mt-6 flex justify-end space-x-3">
            <button
              onClick={() => setShowCreateBookingModal(false)}
              className="px-4 py-2 border rounded-md hover:bg-gray-100"
            >
              Cancel
            </button>
            <button
              onClick={async () => {
                // Basic validation
                if (
                  !newBooking.spaceId ||
                  !newBooking.userName ||
                  !newBooking.startTime ||
                  !newBooking.endTime
                ) {
                  displayToast("error", "Please fill in all required fields");
                  return;
                }

                try {
                  // Format data for API
                  const bookingData = {
                    space_id: newBooking.spaceId,
                    user_name: newBooking.userName,
                    start_time: newBooking.startTime,
                    end_time: newBooking.endTime,
                    status: newBooking.status.toLowerCase(),
                    notes: newBooking.notes || "",
                  };

                  // Create booking via API
                  const response = await bookingService.createBooking(
                    bookingData
                  );

                  // Add new booking to state
                  const newBookingData = {
                    id: response.data.id,
                    spaceId: response.data.space_id,
                    userName: response.data.user_name,
                    startTime: response.data.start_time,
                    endTime: response.data.end_time,
                    status:
                      response.data.status.charAt(0).toUpperCase() +
                      response.data.status.slice(1),
                    notes: response.data.notes,
                  };

                  setBookings([...bookings, newBookingData]);

                  // Update space availability if booking is confirmed
                  if (newBooking.status === "Confirmed") {
                    setSpaces(
                      spaces.map((space) =>
                        space.id === newBooking.spaceId
                          ? { ...space, availability: "Booked" }
                          : space
                      )
                    );
                  }

                  setShowCreateBookingModal(false);
                  setNewBooking({
                    spaceId: null,
                    userName: "",
                    startTime: "",
                    endTime: "",
                    status: "Pending",
                    notes: "",
                  });
                  displayToast("success", "Booking created successfully");
                } catch (error) {
                  console.error("Error creating booking:", error);
                  displayToast(
                    "error",
                    error.message ||
                      "Failed to create booking. Please try again."
                  );
                }
              }}
              className="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600"
            >
              Create Booking
            </button>
          </div>
        </div>
      </div>
    );
  };

  // Helper function to generate calendar days
  const generateCalendarDays = () => {
    const days = [];
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();

    // Start from the first day of the month
    const firstDay = new Date(year, month, 1);
    // Get the last day of the month
    const lastDay = new Date(year, month + 1, 0);

    // Fill in days from previous month to start the calendar from Sunday
    const firstDayOfWeek = firstDay.getDay();
    const prevMonthLastDay = new Date(year, month, 0).getDate();

    for (let i = firstDayOfWeek - 1; i >= 0; i--) {
      const date = new Date(year, month - 1, prevMonthLastDay - i);
      days.push({
        date,
        isCurrentMonth: false,
        isToday: isSameDay(date, new Date()),
        bookings: getBookingsForDate(date),
      });
    }

    // Current month days
    for (let i = 1; i <= lastDay.getDate(); i++) {
      const date = new Date(year, month, i);
      days.push({
        date,
        isCurrentMonth: true,
        isToday: isSameDay(date, new Date()),
        bookings: getBookingsForDate(date),
      });
    }

    // Fill in remaining days from next month to complete the calendar grid
    const remainingDays = 42 - days.length; // 6 rows * 7 days = 42
    for (let i = 1; i <= remainingDays; i++) {
      const date = new Date(year, month + 1, i);
      days.push({
        date,
        isCurrentMonth: false,
        isToday: isSameDay(date, new Date()),
        bookings: getBookingsForDate(date),
      });
    }

    return days;
  };

  // Helper to check if two dates are the same day
  const isSameDay = (date1, date2) => {
    return (
      date1.getDate() === date2.getDate() &&
      date1.getMonth() === date2.getMonth() &&
      date1.getFullYear() === date2.getFullYear()
    );
  };

  // Get bookings for a specific date
  const getBookingsForDate = (date) => {
    return bookings.filter((booking) => {
      const bookingDate = new Date(booking.startTime);
      return isSameDay(date, bookingDate);
    });
  };

  // Handle bulk actions
  const handleBulkAction = (action) => {
    if (selectedSpaces.length === 0) {
      displayToast("error", "Please select at least one space");
      return;
    }

    switch (action) {
      case "delete":
        if (
          confirm(
            `Are you sure you want to delete ${selectedSpaces.length} spaces?`
          )
        ) {
          setSpaces(
            spaces.filter((space) => !selectedSpaces.includes(space.id))
          );
          setSelectedSpaces([]);
          setBulkActionMode(false);
          displayToast(
            "success",
            `${selectedSpaces.length} spaces deleted successfully`
          );
        }
        break;
      case "setAvailable":
        setSpaces(
          spaces.map((space) =>
            selectedSpaces.includes(space.id)
              ? { ...space, availability: "Available" }
              : space
          )
        );
        displayToast(
          "success",
          `${selectedSpaces.length} spaces marked as Available`
        );
        break;
      case "setMaintenance":
        setSpaces(
          spaces.map((space) =>
            selectedSpaces.includes(space.id)
              ? { ...space, availability: "Maintenance" }
              : space
          )
        );
        displayToast(
          "success",
          `${selectedSpaces.length} spaces set to Maintenance`
        );
        break;
      default:
        break;
    }
  };

  // Toggle space selection for bulk actions
  const toggleSpaceSelection = (spaceId) => {
    if (selectedSpaces.includes(spaceId)) {
      setSelectedSpaces(selectedSpaces.filter((id) => id !== spaceId));
    } else {
      setSelectedSpaces([...selectedSpaces, spaceId]);
    }
  };

  // Handle calendar date selection
  const handleDateSelect = (date) => {
    setSelectedDate(date);
    const dateBookings = getBookingsForDate(date);
    setSelectedDateBookings(dateBookings);
  };

  // Format date for display
  const formatDate = (date) => {
    const options = { weekday: "short", month: "short", day: "numeric" };
    return date.toLocaleDateString("en-US", options);
  };

  // Move to previous/next month in calendar
  const changeMonth = (increment) => {
    const newDate = new Date(currentDate);
    newDate.setMonth(newDate.getMonth() + increment);
    setCurrentDate(newDate);
  };

  // Get space by ID
  const getSpaceById = (id) => {
    return spaces.find((space) => space.id === id) || { name: "Unknown Space" };
  };

  // Format time for display
  const formatTime = (timeString) => {
    const date = new Date(timeString);
    return date.toLocaleTimeString("en-US", {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  // Calculate duration between two times - fixed to use numerical operations
  const calculateDuration = (startTime, endTime) => {
    const start = new Date(startTime);
    const end = new Date(endTime);
    const diffMs = end - start;
    const diffHrs = Math.round((diffMs / (1000 * 60 * 60)) * 10) / 10;
    return `${diffHrs} hrs`;
  };

  return (
    <div className="bg-gray-50 min-h-screen">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 py-6 flex justify-between items-center">
          <h1 className="text-2xl font-bold text-gray-900">
            Workspace Space Management
          </h1>
          <div className="flex items-center space-x-4">
            <button
              onClick={() => setActiveTab("calendar")}
              className={`flex items-center px-3 py-2 rounded-md ${
                activeTab === "calendar"
                  ? "bg-blue-100 text-blue-600"
                  : "hover:bg-gray-100"
              }`}
            >
              <Calendar size={18} className="mr-2" />
              <span>Calendar</span>
            </button>
            <button
              onClick={() => setShowSettingsModal(true)}
              className="flex items-center px-3 py-2 rounded-md hover:bg-gray-100"
            >
              <Settings size={18} className="mr-2" />
              <span>Settings</span>
            </button>
            <button
              onClick={() => {
                setShowModal(true);
                setEditingSpace(null);
              }}
              className="flex items-center bg-blue-500 text-white px-3 py-2 rounded-md hover:bg-blue-600"
            >
              <Plus size={18} className="mr-2" />
              <span>Add Space</span>
            </button>
          </div>
        </div>
      </header>

      {/* Main content area */}
      <main className="max-w-7xl mx-auto px-4 py-6">
        {/* Tabs */}
        <div className="mb-6 border-b">
          <div className="flex">
            <button
              onClick={() => setActiveTab("list")}
              className={`px-4 py-2 border-b-2 ${
                activeTab === "list"
                  ? "border-blue-500 text-blue-600"
                  : "border-transparent"
              }`}
            >
              Spaces List
            </button>
            {selectedSpace && (
              <button
                onClick={() => setActiveTab("details")}
                className={`px-4 py-2 border-b-2 ${
                  activeTab === "details"
                    ? "border-blue-500 text-blue-600"
                    : "border-transparent"
                }`}
              >
                Space Details
              </button>
            )}
            <button
              onClick={() => setActiveTab("calendar")}
              className={`px-4 py-2 border-b-2 ${
                activeTab === "calendar"
                  ? "border-blue-500 text-blue-600"
                  : "border-transparent"
              }`}
            >
              Calendar View
            </button>
            <button
              onClick={() => setActiveTab("bookings")}
              className={`px-4 py-2 border-b-2 ${
                activeTab === "bookings"
                  ? "border-blue-500 text-blue-600"
                  : "border-transparent"
              }`}
            >
              Bookings
            </button>
          </div>
        </div>

        {/* List View */}
        {activeTab === "list" && (
          <div>
            {/* Filter and Search */}
            <div className="mb-6 flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
              <div className="flex items-center space-x-4">
                <div>
                  <label
                    htmlFor="filter"
                    className="block text-sm font-medium text-gray-700 mb-1"
                  >
                    Filter by
                  </label>
                  <select
                    id="filter"
                    className="border rounded-md p-2"
                    value={filterAvailability}
                    onChange={(e) => setFilterAvailability(e.target.value)}
                  >
                    <option value="All">All Spaces</option>
                    <option value="Available">Available</option>
                    <option value="Booked">Booked</option>
                    <option value="Maintenance">Maintenance</option>
                  </select>
                </div>
                <div>
                  <label
                    htmlFor="search"
                    className="block text-sm font-medium text-gray-700 mb-1"
                  >
                    Search
                  </label>
                  <input
                    id="search"
                    type="text"
                    className="border rounded-md p-2"
                    placeholder="Search spaces..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    View Mode
                  </label>
                  <div className="flex border rounded-md overflow-hidden">
                    <button
                      onClick={() => setViewMode("table")}
                      className={`px-3 py-2 ${
                        viewMode === "table"
                          ? "bg-blue-500 text-white"
                          : "bg-gray-100"
                      }`}
                    >
                      Table
                    </button>
                    <button
                      onClick={() => setViewMode("grid")}
                      className={`px-3 py-2 ${
                        viewMode === "grid"
                          ? "bg-blue-500 text-white"
                          : "bg-gray-100"
                      }`}
                    >
                      Grid
                    </button>
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Actions
                  </label>
                  <button
                    onClick={() => {
                      setBulkActionMode(!bulkActionMode);
                      setSelectedSpaces([]);
                    }}
                    className={`px-3 py-2 border rounded-md ${
                      bulkActionMode
                        ? "bg-blue-100 text-blue-600"
                        : "hover:bg-gray-100"
                    }`}
                  >
                    {bulkActionMode ? "Cancel Bulk Actions" : "Bulk Actions"}
                  </button>
                </div>
              </div>
            </div>

            {/* Bulk Actions Bar */}
            {bulkActionMode && (
              <div className="mb-6 p-3 bg-blue-50 border border-blue-100 rounded-md flex flex-wrap justify-between items-center">
                <div>
                  <span className="text-sm text-blue-600 font-medium">
                    {selectedSpaces.length}{" "}
                    {selectedSpaces.length === 1 ? "space" : "spaces"} selected
                  </span>
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={() => handleBulkAction("setAvailable")}
                    className="px-3 py-1 text-sm bg-green-500 text-white rounded-md hover:bg-green-600"
                  >
                    Set Available
                  </button>
                  <button
                    onClick={() => handleBulkAction("setMaintenance")}
                    className="px-3 py-1 text-sm bg-amber-500 text-white rounded-md hover:bg-amber-600"
                  >
                    Set Maintenance
                  </button>
                  <button
                    onClick={() => handleBulkAction("delete")}
                    className="px-3 py-1 text-sm bg-red-500 text-white rounded-md hover:bg-red-600"
                  >
                    Delete Selected
                  </button>
                </div>
              </div>
            )}

            {/* Table View */}
            {viewMode === "table" && (
              <div className="bg-white shadow-sm rounded-lg overflow-hidden">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      {bulkActionMode && (
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          <input
                            type="checkbox"
                            onChange={(e) => {
                              if (e.target.checked) {
                                setSelectedSpaces(
                                  filteredSpaces.map((space) => space.id)
                                );
                              } else {
                                setSelectedSpaces([]);
                              }
                            }}
                            checked={
                              selectedSpaces.length === filteredSpaces.length &&
                              filteredSpaces.length > 0
                            }
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          />
                        </th>
                      )}
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Name
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Capacity
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Rate
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Availability
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredSpaces.length > 0 ? (
                      filteredSpaces.map((space) => (
                        <tr key={space.id} className="hover:bg-gray-50">
                          {bulkActionMode && (
                            <td className="px-6 py-4 whitespace-nowrap">
                              <input
                                type="checkbox"
                                checked={selectedSpaces.includes(space.id)}
                                onChange={() => toggleSpaceSelection(space.id)}
                                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                              />
                            </td>
                          )}
                          <td className="px-6 py-4">
                            <div className="flex items-center">
                              <button
                                onClick={() => handleViewDetails(space)}
                                className="text-blue-600 hover:text-blue-800 hover:underline font-medium"
                              >
                                {space.name}
                              </button>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <Users size={16} className="text-gray-400 mr-2" />
                              <span>{space.capacity}</span>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <DollarSign
                                size={16}
                                className="text-gray-400 mr-1"
                              />
                              <span>{space.rate}/hr</span>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span
                              className={`px-3 py-1 rounded-full text-xs font-medium
                              ${
                                space.availability === "Available"
                                  ? "bg-green-100 text-green-800"
                                  : space.availability === "Booked"
                                  ? "bg-blue-100 text-blue-800"
                                  : "bg-amber-100 text-amber-800"
                              }`}
                            >
                              {space.availability}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <button
                              onClick={() => handleEdit(space)}
                              className="text-blue-600 hover:text-blue-900 mr-4"
                            >
                              <Edit size={18} />
                            </button>
                            <button
                              onClick={() => handleDelete(space.id)}
                              className="text-red-600 hover:text-red-900"
                            >
                              <Trash2 size={18} />
                            </button>
                          </td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td
                          colSpan={bulkActionMode ? 6 : 5}
                          className="px-6 py-4 text-center text-gray-500"
                        >
                          No spaces found. Try adjusting your filters or add a
                          new space.
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            )}

            {/* Grid View */}
            {viewMode === "grid" && (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {filteredSpaces.length > 0 ? (
                  filteredSpaces.map((space) => (
                    <div
                      key={space.id}
                      className="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200"
                    >
                      <div className="h-40 bg-gray-200 relative">
                        <div className="absolute inset-0 flex items-center justify-center">
                          <Image size={48} className="text-gray-400" />
                        </div>
                        {bulkActionMode && (
                          <div className="absolute top-2 left-2">
                            <input
                              type="checkbox"
                              checked={selectedSpaces.includes(space.id)}
                              onChange={() => toggleSpaceSelection(space.id)}
                              className="h-5 w-5 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                            />
                          </div>
                        )}
                        <div className="absolute top-2 right-2">
                          <span
                            className={`px-2 py-1 rounded-full text-xs font-medium
                            ${
                              space.availability === "Available"
                                ? "bg-green-100 text-green-800"
                                : space.availability === "Booked"
                                ? "bg-blue-100 text-blue-800"
                                : "bg-amber-100 text-amber-800"
                            }`}
                          >
                            {space.availability}
                          </span>
                        </div>
                      </div>
                      <div className="p-4">
                        <div className="flex justify-between items-center mb-2">
                          <h3 className="font-medium text-gray-900">
                            <button
                              onClick={() => handleViewDetails(space)}
                              className="hover:underline"
                            >
                              {space.name}
                            </button>
                          </h3>
                          <div className="flex space-x-2">
                            <button
                              onClick={() => handleEdit(space)}
                              className="p-1 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded"
                            >
                              <Edit size={16} />
                            </button>
                            <button
                              onClick={() => handleDelete(space.id)}
                              className="p-1 text-red-600 hover:text-red-800 hover:bg-red-50 rounded"
                            >
                              <Trash2 size={16} />
                            </button>
                          </div>
                        </div>
                        <div className="flex items-center mb-1 text-sm">
                          <Users size={14} className="text-gray-400 mr-1" />
                          <span className="text-gray-600">
                            Capacity: {space.capacity}
                          </span>
                        </div>
                        <div className="flex items-center mb-2 text-sm">
                          <DollarSign
                            size={14}
                            className="text-gray-400 mr-1"
                          />
                          <span className="text-gray-600">
                            ${space.rate}/hour
                          </span>
                        </div>
                        {space.features.length > 0 && (
                          <div className="flex flex-wrap gap-1 mt-3">
                            {space.features
                              .slice(0, 3)
                              .map((feature, index) => (
                                <span
                                  key={index}
                                  className="text-xs px-2 py-1 bg-gray-100 rounded-full"
                                >
                                  {feature}
                                </span>
                              ))}
                            {space.features.length > 3 && (
                              <span className="text-xs px-2 py-1 bg-gray-100 rounded-full">
                                +{space.features.length - 3} more
                              </span>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="col-span-full py-8 text-center text-gray-500">
                    No spaces found. Try adjusting your filters or add a new
                    space.
                  </div>
                )}
              </div>
            )}
          </div>
        )}

        {/* Details View */}
        {activeTab === "details" && selectedSpace && (
          <div className="bg-white shadow-sm rounded-lg p-6">
            <div className="mb-6 flex justify-between items-center">
              <button
                onClick={() => setActiveTab("list")}
                className="flex items-center text-blue-600 hover:text-blue-800"
              >
                <ChevronLeft size={20} className="mr-1" />
                <span>Back to List</span>
              </button>
              <div className="flex space-x-3">
                <button
                  onClick={() => handleEdit(selectedSpace)}
                  className="flex items-center px-3 py-2 border border-blue-500 text-blue-500 rounded-md hover:bg-blue-50"
                >
                  <Edit size={16} className="mr-2" />
                  <span>Edit Space</span>
                </button>
                <button
                  onClick={() => {
                    if (handleDelete(selectedSpace.id)) {
                      setActiveTab("list");
                      setSelectedSpace(null);
                    }
                  }}
                  className="flex items-center px-3 py-2 border border-red-500 text-red-500 rounded-md hover:bg-red-50"
                >
                  <Trash2 size={16} className="mr-2" />
                  <span>Delete Space</span>
                </button>
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Left Column - Basic Info */}
              <div className="lg:col-span-2 space-y-6">
                <div>
                  <h2 className="text-2xl font-bold">{selectedSpace.name}</h2>
                  <div className="mt-2">
                    <span
                      className={`px-3 py-1 rounded-full text-xs font-medium
                      ${
                        selectedSpace.availability === "Available"
                          ? "bg-green-100 text-green-800"
                          : selectedSpace.availability === "Booked"
                          ? "bg-blue-100 text-blue-800"
                          : "bg-amber-100 text-amber-800"
                      }`}
                    >
                      {selectedSpace.availability}
                    </span>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-medium mb-3">Description</h3>
                  <p className="text-gray-600">{selectedSpace.description}</p>
                </div>

                <div className="grid grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-lg font-medium mb-3">Details</h3>
                    <div className="space-y-3">
                      <div className="flex items-center">
                        <Users size={18} className="text-gray-400 mr-2" />
                        <span className="text-gray-600">
                          Capacity: <strong>{selectedSpace.capacity}</strong>
                        </span>
                      </div>
                      <div className="flex items-center">
                        <DollarSign size={18} className="text-gray-400 mr-2" />
                        <span className="text-gray-600">
                          Rate: <strong>${selectedSpace.rate}/hour</strong>
                        </span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium mb-3">
                      Advanced Settings
                    </h3>
                    <div className="space-y-2 text-sm">
                      <div className="flex items-center justify-between">
                        <span className="text-gray-600">Min Booking:</span>
                        <span className="font-medium">
                          {selectedSpace.minBookingHours || 1} hrs
                        </span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-gray-600">Max Booking:</span>
                        <span className="font-medium">
                          {selectedSpace.maxBookingHours || 8} hrs
                        </span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-gray-600">Open Time:</span>
                        <span className="font-medium">
                          {selectedSpace.openTime || "08:00"}
                        </span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-gray-600">Close Time:</span>
                        <span className="font-medium">
                          {selectedSpace.closeTime || "18:00"}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-medium mb-3">Features</h3>
                  <div className="flex flex-wrap gap-2">
                    {selectedSpace.features.map((feature, index) => (
                      <span
                        key={index}
                        className="px-3 py-1 bg-gray-100 rounded-full text-sm"
                      >
                        {feature}
                      </span>
                    ))}
                    {selectedSpace.features.length === 0 && (
                      <p className="text-gray-500 text-sm italic">
                        No features listed
                      </p>
                    )}
                  </div>
                </div>
              </div>

              {/* Right Column - Space Image */}
              <div>
                <div className="bg-gray-200 rounded-lg h-64 flex items-center justify-center border">
                  <Image size={64} className="text-gray-400" />
                </div>

                <div className="mt-6 border-t pt-6">
                  <h3 className="text-lg font-medium mb-3">Bookings</h3>
                  <div className="space-y-3">
                    {bookings
                      .filter((booking) => booking.spaceId === selectedSpace.id)
                      .map((booking) => (
                        <div key={booking.id} className="p-3 border rounded-md">
                          <div className="flex justify-between">
                            <span className="font-medium">
                              {booking.userName}
                            </span>
                            <span
                              className={`px-2 py-1 rounded-full text-xs font-medium
                            ${
                              booking.status === "Confirmed"
                                ? "bg-green-100 text-green-800"
                                : booking.status === "Pending"
                                ? "bg-amber-100 text-amber-800"
                                : "bg-red-100 text-red-800"
                            }`}
                            >
                              {booking.status}
                            </span>
                          </div>
                          <div className="text-sm text-gray-600 mt-1">
                            {formatTime(booking.startTime)} -{" "}
                            {formatTime(booking.endTime)}
                          </div>
                        </div>
                      ))}
                    {bookings.filter(
                      (booking) => booking.spaceId === selectedSpace.id
                    ).length === 0 && (
                      <p className="text-gray-500 text-sm italic">
                        No upcoming bookings
                      </p>
                    )}
                    <button
                      onClick={() => {
                        setNewBooking({
                          ...newBooking,
                          spaceId: selectedSpace.id,
                        });
                        setShowCreateBookingModal(true);
                      }}
                      className="w-full mt-2 bg-blue-500 text-white px-3 py-2 rounded-md hover:bg-blue-600"
                    >
                      Create Booking
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Calendar View */}
        {activeTab === "calendar" && (
          <div>
            <div className="mb-6 flex justify-between items-center">
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => changeMonth(-1)}
                  className="p-1 rounded-full hover:bg-gray-200"
                >
                  <ChevronLeft size={24} />
                </button>
                <h2 className="text-xl font-bold">
                  {currentDate.toLocaleDateString("en-US", {
                    month: "long",
                    year: "numeric",
                  })}
                </h2>
                <button
                  onClick={() => changeMonth(1)}
                  className="p-1 rounded-full hover:bg-gray-200"
                >
                  <ChevronRight size={24} />
                </button>
              </div>
              <div>
                <button
                  onClick={() => {
                    setNewBooking({
                      ...newBooking,
                      startTime: new Date().toISOString().slice(0, 16),
                      endTime: new Date(Date.now() + 60 * 60 * 1000)
                        .toISOString()
                        .slice(0, 16),
                    });
                    setShowCreateBookingModal(true);
                  }}
                  className="flex items-center bg-blue-500 text-white px-3 py-2 rounded-md hover:bg-blue-600"
                >
                  <Plus size={18} className="mr-2" />
                  <span>New Booking</span>
                </button>
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Calendar Grid */}
              <div className="lg:col-span-2">
                <div className="bg-white shadow-sm rounded-lg overflow-hidden">
                  {/* Calendar Header */}
                  <div className="grid grid-cols-7 gap-px bg-gray-200">
                    {["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"].map(
                      (day) => (
                        <div
                          key={day}
                          className="p-2 text-center text-sm font-medium bg-white"
                        >
                          {day}
                        </div>
                      )
                    )}
                  </div>

                  {/* Calendar Days */}
                  <div className="grid grid-cols-7 gap-px bg-gray-200">
                    {generateCalendarDays().map((day, index) => (
                      <div
                        key={index}
                        onClick={() => handleDateSelect(day.date)}
                        className={`min-h-[100px] p-2 bg-white cursor-pointer hover:bg-blue-50
                          ${!day.isCurrentMonth ? "text-gray-400" : ""}
                          ${day.isToday ? "bg-blue-50" : ""}
                          ${
                            isSameDay(day.date, selectedDate)
                              ? "ring-2 ring-blue-500"
                              : ""
                          }
                        `}
                      >
                        <div className="flex justify-between items-center mb-1">
                          <span
                            className={`text-sm font-medium ${
                              day.isToday ? "text-blue-600" : ""
                            }`}
                          >
                            {day.date.getDate()}
                          </span>
                          {day.bookings.length > 0 && (
                            <span className="flex h-5 w-5 items-center justify-center bg-blue-500 text-white rounded-full text-xs">
                              {day.bookings.length}
                            </span>
                          )}
                        </div>

                        {day.bookings.slice(0, 2).map((booking, index) => (
                          <div
                            key={booking.id}
                            className={`text-xs px-1 py-0.5 mb-1 truncate rounded
                              ${
                                booking.status === "Confirmed"
                                  ? "bg-green-100 text-green-800"
                                  : booking.status === "Pending"
                                  ? "bg-amber-100 text-amber-800"
                                  : "bg-red-100 text-red-800"
                              }`}
                          >
                            {formatTime(booking.startTime)} -{" "}
                            {getSpaceById(booking.spaceId).name}
                          </div>
                        ))}
                        {day.bookings.length > 2 && (
                          <div className="text-xs text-blue-600">
                            + {day.bookings.length - 2} more
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Day View */}
              <div>
                <div className="bg-white shadow-sm rounded-lg p-4">
                  <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg font-medium">
                      {formatDate(selectedDate)}
                    </h3>
                    <button
                      onClick={() => {
                        setNewBooking({
                          ...newBooking,
                          startTime:
                            selectedDate.toISOString().slice(0, 10) + "T09:00",
                          endTime:
                            selectedDate.toISOString().slice(0, 10) + "T10:00",
                        });
                        setShowCreateBookingModal(true);
                      }}
                      className="flex items-center text-sm bg-blue-500 text-white px-2 py-1 rounded-md hover:bg-blue-600"
                    >
                      <Plus size={14} className="mr-1" />
                      <span>Add</span>
                    </button>
                  </div>

                  {selectedDateBookings.length > 0 ? (
                    <div className="space-y-3">
                      {selectedDateBookings.map((booking) => {
                        const space = getSpaceById(booking.spaceId);
                        return (
                          <div
                            key={booking.id}
                            className="p-3 border rounded-md"
                          >
                            <div className="flex justify-between items-start">
                              <div>
                                <div className="font-medium">{space.name}</div>
                                <div className="text-sm text-gray-600">
                                  {booking.userName}
                                </div>
                              </div>
                              <span
                                className={`px-2 py-0.5 rounded-full text-xs font-medium
                                ${
                                  booking.status === "Confirmed"
                                    ? "bg-green-100 text-green-800"
                                    : booking.status === "Pending"
                                    ? "bg-amber-100 text-amber-800"
                                    : "bg-red-100 text-red-800"
                                }`}
                              >
                                {booking.status}
                              </span>
                            </div>
                            <div className="flex justify-between mt-2 text-sm text-gray-600">
                              <div>
                                {formatTime(booking.startTime)} -{" "}
                                {formatTime(booking.endTime)}
                              </div>
                              <div>
                                {calculateDuration(
                                  booking.startTime,
                                  booking.endTime
                                )}
                              </div>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  ) : (
                    <div className="text-center py-6 text-gray-500">
                      <Calendar
                        size={36}
                        className="mx-auto text-gray-400 mb-2"
                      />
                      <p>No bookings for this date</p>
                      <p className="text-sm mt-1">Click to add a new booking</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Bookings View */}
        {activeTab === "bookings" && (
          <div>
            <div className="mb-6 flex justify-between items-center">
              <h2 className="text-xl font-semibold">All Bookings</h2>
              <button
                onClick={() => setShowCreateBookingModal(true)}
                className="flex items-center bg-blue-500 text-white px-3 py-2 rounded-md hover:bg-blue-600"
              >
                <Plus size={18} className="mr-2" />
                <span>New Booking</span>
              </button>
            </div>

            <div className="bg-white shadow-sm rounded-lg overflow-hidden">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Space
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Client
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Date & Time
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Duration
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {bookings.map((booking) => {
                    const space = getSpaceById(booking.spaceId);
                    return (
                      <tr key={booking.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="font-medium text-gray-900">
                            {space.name}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {booking.userName}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            {new Date(booking.startTime).toLocaleDateString()}
                          </div>
                          <div className="text-sm text-gray-500">
                            {formatTime(booking.startTime)} -{" "}
                            {formatTime(booking.endTime)}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {calculateDuration(
                            booking.startTime,
                            booking.endTime
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span
                            className={`px-3 py-1 rounded-full text-xs font-medium
                            ${
                              booking.status === "Confirmed"
                                ? "bg-green-100 text-green-800"
                                : booking.status === "Pending"
                                ? "bg-amber-100 text-amber-800"
                                : "bg-red-100 text-red-800"
                            }`}
                          >
                            {booking.status}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <button
                            onClick={() => {
                              // Handle editing booking
                            }}
                            className="text-blue-600 hover:text-blue-900 mr-4"
                          >
                            <Edit size={18} />
                          </button>
                          <button
                            onClick={async () => {
                              // Handle deleting booking
                              if (
                                confirm(
                                  "Are you sure you want to delete this booking?"
                                )
                              ) {
                                try {
                                  await bookingService.cancelBooking(
                                    booking.id
                                  );
                                  setBookings(
                                    bookings.filter((b) => b.id !== booking.id)
                                  );
                                  displayToast(
                                    "success",
                                    "Booking cancelled successfully"
                                  );
                                } catch (error) {
                                  console.error(
                                    "Error cancelling booking:",
                                    error
                                  );
                                  displayToast(
                                    "error",
                                    error.message ||
                                      "Failed to cancel booking. Please try again."
                                  );
                                }
                              }
                            }}
                            className="text-red-600 hover:text-red-900"
                          >
                            <Trash2 size={18} />
                          </button>
                        </td>
                      </tr>
                    );
                  })}
                  {bookings.length === 0 && (
                    <tr>
                      <td
                        colSpan={6}
                        className="px-6 py-4 text-center text-gray-500"
                      >
                        No bookings found. Create your first booking to get
                        started.
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {/* Toast Notification */}
        {showToast && (
          <div
            className={`fixed bottom-4 right-4 px-4 py-3 rounded-lg shadow-lg flex items-center space-x-2 ${
              toastMessage.type === "success"
                ? "bg-green-500 text-white"
                : toastMessage.type === "error"
                ? "bg-red-500 text-white"
                : "bg-blue-500 text-white"
            }`}
          >
            {toastMessage.type === "success" ? (
              <div className="rounded-full bg-white bg-opacity-20 p-1">
                <Check size={16} />
              </div>
            ) : toastMessage.type === "error" ? (
              <div className="rounded-full bg-white bg-opacity-20 p-1">
                <AlertCircle size={16} />
              </div>
            ) : (
              <div className="rounded-full bg-white bg-opacity-20 p-1">
                <Info size={16} />
              </div>
            )}
            <span>{toastMessage.message}</span>
          </div>
        )}
      </main>

      {/* Modals */}
      {showModal && <SpaceModal />}
      {showCreateBookingModal && <BookingModal />}
    </div>
  );
};

export default SpaceList;
