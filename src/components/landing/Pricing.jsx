import { motion } from "framer-motion";
import { useState } from "react";

const plans = [
  {
    title: "Basic",
    tag: "Basic",
    monthlyPrice: "$99",
    annualPrice: "$79",
    features: [
      { text: "Up to 50 users", included: true },
      { text: "Space management", included: true },
      { text: "Basic analytics", included: true },
      { text: "Email support", included: true },
      { text: "Custom reporting", included: false },
      { text: "API access", included: false },
    ],
  },
  {
    title: "Professional",
    tag: "Professional",
    monthlyPrice: "$199",
    annualPrice: "$159",
    isPopular: true,
    features: [
      { text: "Up to 200 users", included: true },
      { text: "Advanced space management", included: true },
      { text: "Advanced analytics", included: true },
      { text: "Priority support", included: true },
      { text: "Custom reporting", included: true },
      { text: "API access", included: false },
    ],
  },
  {
    title: "Enterprise",
    tag: "Enterprise",
    monthlyPrice: "$399",
    annualPrice: "$319",
    features: [
      { text: "Unlimited users", included: true },
      { text: "Full feature access", included: true },
      { text: "Advanced analytics", included: true },
      { text: "24/7 dedicated support", included: true },
      { text: "Custom reporting", included: true },
      { text: "API access", included: true },
    ],
  },
];

const PricingCard = ({
  title,
  tag,
  price,
  period,
  features,
  isPopular,
  index,
}) => {
  return (
    <motion.div
      className={`bg-white rounded-2xl overflow-hidden shadow-lg transition-all duration-300 ${
        isPopular
          ? "border-2 border-primary md:-translate-y-5"
          : "hover:-translate-y-3 hover:shadow-xl"
      }`}
      initial={{ opacity: 0, y: 50 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      viewport={{ once: true }}
    >
      <div className="p-8 bg-gray-50">
        <div className="inline-block px-3 py-1 bg-primary-100 text-primary rounded-full text-sm font-semibold mb-5">
          {tag}
        </div>
        <div className="text-3xl font-bold mb-1">{price}</div>
        <div className="text-gray-500 text-sm">{period}</div>
      </div>

      <div className="p-8">
        <ul className="space-y-4 mb-8">
          {features.map((feature, idx) => (
            <li
              key={idx}
              className={`flex items-center gap-3 ${
                feature.included ? "text-gray-600" : "text-gray-300"
              }`}
            >
              <i
                className={`fas ${
                  feature.included
                    ? "fa-check-circle text-primary"
                    : "fa-times-circle"
                }`}
              ></i>
              <span>{feature.text}</span>
            </li>
          ))}
        </ul>

        <button
          className={`w-full py-3 px-6 rounded-lg font-semibold transition-all ${
            isPopular
              ? "bg-gradient-to-r from-blue-500 to-blue-700 hover:translate-y-[-3px] hover:shadow-lg transition-all"
              : "border-2 border-primary text-primary hover:bg-primary-50"
          }`}
        >
          Get Started
        </button>
      </div>
    </motion.div>
  );
};

const PricingSection = () => {
  const [isAnnual, setIsAnnual] = useState(false);

  return (
    <section id="pricing" className="py-24">
      <div className="container mx-auto px-4">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl font-bold mb-4">
            Simple, Transparent Pricing
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Choose the plan that fits your needs. No hidden fees or surprises.
          </p>
        </motion.div>

        <div className="flex justify-center items-center gap-4 mb-16">
          <span
            className={`font-medium ${
              !isAnnual ? "text-primary" : "text-gray-600"
            }`}
          >
            Monthly
          </span>

          <button
            className={`w-14 h-7 rounded-full relative transition-all ${
              isAnnual ? "bg-primary-light" : "bg-gray-300"
            }`}
            onClick={() => setIsAnnual(!isAnnual)}
          >
            <span
              className={`absolute w-5 h-5 bg-white rounded-full top-1 transition-all ${
                isAnnual ? "left-8" : "left-1"
              }`}
            ></span>
          </button>

          <span
            className={`font-medium ${
              isAnnual ? "text-primary" : "text-gray-600"
            }`}
          >
            Annual
          </span>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {plans.map((plan, index) => (
            <PricingCard
              key={index}
              title={plan.title}
              tag={plan.tag}
              price={isAnnual ? plan.annualPrice : plan.monthlyPrice}
              period={isAnnual ? "per year" : "per month"}
              features={plan.features}
              isPopular={plan.isPopular}
              index={index}
            />
          ))}
        </div>
        <div className="text-center mt-12">
          <p className="text-gray-500 text-sm">
            All plans come with a 14-day free trial. No credit card required.
          </p>
        </div>
      </div>
    </section>
  );
};

export default PricingSection;
