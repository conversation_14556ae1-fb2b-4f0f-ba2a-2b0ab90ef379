import React, { useState, useEffect } from 'react';
import { useBooking } from '../context/BookingContext';
import { useSpace } from '../context/SpaceContext';
import { useToast } from '../components/common/NotificationContainer';
import FullCalendar from '@fullcalendar/react';
import dayGridPlugin from '@fullcalendar/daygrid';
import timeGridPlugin from '@fullcalendar/timegrid';
import interactionPlugin from '@fullcalendar/interaction';
import { 
  Calendar, 
  Clock, 
  Users, 
  MapPin, 
  Filter,
  Search,
  X,
  Plus
} from 'lucide-react';
import { InlineLoader, ButtonLoader } from '../components/common/LoadingSpinner';

const EnhancedBookingPage = () => {
  const { state: bookingState, actions: bookingActions } = useBooking();
  const { state: spaceState, actions: spaceActions } = useSpace();
  const toast = useToast();
  
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [selectedTimeSlot, setSelectedTimeSlot] = useState(null);
  const [showBookingModal, setShowBookingModal] = useState(false);
  const [calendarView, setCalendarView] = useState('timeGridWeek');
  const [filters, setFilters] = useState({
    spaceType: 'all',
    capacity: 'all',
    amenities: [],
  });

  useEffect(() => {
    // Load initial data
    loadBookingData();
    spaceActions.loadSpaces();
    spaceActions.loadAmenities();
  }, []);

  const loadBookingData = async () => {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 7);
    const endDate = new Date();
    endDate.setDate(endDate.getDate() + 30);

    await bookingActions.loadBookings({
      start_date: startDate.toISOString().split('T')[0],
      end_date: endDate.toISOString().split('T')[0],
    });
  };

  // Transform bookings for FullCalendar
  const calendarEvents = bookingState.bookings.map(booking => ({
    id: booking.id,
    title: `${booking.space_name} - ${booking.purpose || 'Meeting'}`,
    start: booking.start_time,
    end: booking.end_time,
    backgroundColor: booking.status === 'confirmed' ? '#10B981' : 
                    booking.status === 'pending' ? '#F59E0B' : '#EF4444',
    borderColor: booking.status === 'confirmed' ? '#059669' : 
                booking.status === 'pending' ? '#D97706' : '#DC2626',
    extendedProps: {
      booking: booking,
    },
  }));

  const handleDateSelect = (selectInfo) => {
    setSelectedDate(new Date(selectInfo.start));
    setSelectedTimeSlot({
      start: selectInfo.start,
      end: selectInfo.end,
    });
    setShowBookingModal(true);
  };

  const handleEventClick = (clickInfo) => {
    const booking = clickInfo.event.extendedProps.booking;
    bookingActions.setSelectedBooking(booking);
    // Open booking details modal
  };

  const BookingModal = () => {
    const [formData, setFormData] = useState({
      spaceId: '',
      purpose: '',
      attendees: 1,
      notes: '',
      startTime: selectedTimeSlot?.start ? 
        new Date(selectedTimeSlot.start).toISOString().slice(0, 16) : '',
      endTime: selectedTimeSlot?.end ? 
        new Date(selectedTimeSlot.end).toISOString().slice(0, 16) : '',
    });

    const handleSubmit = async (e) => {
      e.preventDefault();
      
      try {
        await bookingActions.createBooking({
          space_id: formData.spaceId,
          start_time: formData.startTime,
          end_time: formData.endTime,
          purpose: formData.purpose,
          attendees: formData.attendees,
          notes: formData.notes,
        });
        
        setShowBookingModal(false);
        toast.success('Booking Created', 'Your booking has been created successfully!');
        loadBookingData(); // Refresh calendar
      } catch (error) {
        toast.error('Booking Failed', error.message || 'Failed to create booking');
      }
    };

    const availableSpaces = spaceState.spaces.filter(space => {
      if (filters.spaceType !== 'all' && space.type !== filters.spaceType) return false;
      if (filters.capacity !== 'all' && space.capacity < parseInt(filters.capacity)) return false;
      if (filters.amenities.length > 0) {
        const spaceAmenities = space.amenities || [];
        if (!filters.amenities.every(amenity => spaceAmenities.includes(amenity))) return false;
      }
      return space.is_active;
    });

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
          <div className="flex items-center justify-between p-6 border-b">
            <h2 className="text-xl font-semibold text-gray-900">Create Booking</h2>
            <button
              onClick={() => setShowBookingModal(false)}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="h-6 w-6" />
            </button>
          </div>
          
          <form onSubmit={handleSubmit} className="p-6 space-y-6">
            {/* Space Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Select Space
              </label>
              <select
                value={formData.spaceId}
                onChange={(e) => setFormData(prev => ({ ...prev, spaceId: e.target.value }))}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              >
                <option value="">Choose a space...</option>
                {availableSpaces.map(space => (
                  <option key={space.id} value={space.id}>
                    {space.name} - Capacity: {space.capacity} - ${space.hourly_rate}/hr
                  </option>
                ))}
              </select>
            </div>

            {/* Date and Time */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Start Time
                </label>
                <input
                  type="datetime-local"
                  value={formData.startTime}
                  onChange={(e) => setFormData(prev => ({ ...prev, startTime: e.target.value }))}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  End Time
                </label>
                <input
                  type="datetime-local"
                  value={formData.endTime}
                  onChange={(e) => setFormData(prev => ({ ...prev, endTime: e.target.value }))}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>
            </div>

            {/* Purpose and Attendees */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Purpose
                </label>
                <input
                  type="text"
                  value={formData.purpose}
                  onChange={(e) => setFormData(prev => ({ ...prev, purpose: e.target.value }))}
                  placeholder="Meeting purpose..."
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Attendees
                </label>
                <input
                  type="number"
                  min="1"
                  value={formData.attendees}
                  onChange={(e) => setFormData(prev => ({ ...prev, attendees: parseInt(e.target.value) }))}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>

            {/* Notes */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Additional Notes
              </label>
              <textarea
                value={formData.notes}
                onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                rows={3}
                placeholder="Any special requirements or notes..."
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            {/* Actions */}
            <div className="flex justify-end space-x-3 pt-4 border-t">
              <button
                type="button"
                onClick={() => setShowBookingModal(false)}
                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
              >
                Cancel
              </button>
              <ButtonLoader
                loading={bookingState.loading.creating}
                type="submit"
                className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                Create Booking
              </ButtonLoader>
            </div>
          </form>
        </div>
      </div>
    );
  };

  const FilterPanel = () => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-gray-900">Filters</h3>
        <button
          onClick={() => setFilters({ spaceType: 'all', capacity: 'all', amenities: [] })}
          className="text-sm text-gray-600 hover:text-gray-800"
        >
          Clear All
        </button>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Space Type
          </label>
          <select
            value={filters.spaceType}
            onChange={(e) => setFilters(prev => ({ ...prev, spaceType: e.target.value }))}
            className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
          >
            <option value="all">All Types</option>
            <option value="meeting_room">Meeting Room</option>
            <option value="conference_room">Conference Room</option>
            <option value="hot_desk">Hot Desk</option>
            <option value="private_office">Private Office</option>
          </select>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Min Capacity
          </label>
          <select
            value={filters.capacity}
            onChange={(e) => setFilters(prev => ({ ...prev, capacity: e.target.value }))}
            className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
          >
            <option value="all">Any Size</option>
            <option value="2">2+ People</option>
            <option value="5">5+ People</option>
            <option value="10">10+ People</option>
            <option value="20">20+ People</option>
          </select>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            View
          </label>
          <select
            value={calendarView}
            onChange={(e) => setCalendarView(e.target.value)}
            className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
          >
            <option value="dayGridMonth">Month</option>
            <option value="timeGridWeek">Week</option>
            <option value="timeGridDay">Day</option>
          </select>
        </div>
      </div>
    </div>
  );

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Booking Calendar</h1>
          <p className="text-gray-600 mt-1">
            View and manage your workspace bookings
          </p>
        </div>
        <button
          onClick={() => setShowBookingModal(true)}
          className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
        >
          <Plus className="h-4 w-4 mr-2" />
          New Booking
        </button>
      </div>

      <FilterPanel />

      {/* Calendar */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        {bookingState.loading.bookings ? (
          <div className="flex justify-center py-12">
            <InlineLoader text="Loading calendar..." />
          </div>
        ) : (
          <FullCalendar
            plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin]}
            headerToolbar={{
              left: 'prev,next today',
              center: 'title',
              right: 'dayGridMonth,timeGridWeek,timeGridDay'
            }}
            initialView={calendarView}
            editable={true}
            selectable={true}
            selectMirror={true}
            dayMaxEvents={true}
            weekends={true}
            events={calendarEvents}
            select={handleDateSelect}
            eventClick={handleEventClick}
            height="auto"
            slotMinTime="06:00:00"
            slotMaxTime="22:00:00"
            businessHours={{
              daysOfWeek: [1, 2, 3, 4, 5],
              startTime: '09:00',
              endTime: '18:00',
            }}
          />
        )}
      </div>

      {/* Booking Modal */}
      {showBookingModal && <BookingModal />}
    </div>
  );
};

export default EnhancedBookingPage;
