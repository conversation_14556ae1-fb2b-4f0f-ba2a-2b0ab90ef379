import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { MenuIcon, XIcon } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { Button } from "flowbite-react";

const Header = () => {
  const [scrolled, setScrolled] = useState(false);
  const [mobileNavOpen, setMobileNavOpen] = useState(false);
  const navigate = useNavigate();

  const logoElement = (
    <img
      src="/images/logo-blue.png"
      alt="Logo"
      className="h-16 w-auto" // Increased from h-8 to h-10
    />
  );

  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 50);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  return (
    <>
      <header
        className={`fixed w-full top-0 left-0 z-50 transition-all duration-300 ${
          scrolled ? "bg-white shadow-md py-3" : "py-5"
        }`}
      >
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center">
            <Link
              to="#"
              className="flex items-center gap-2 text-xl font-bold text-primary-dark"
            >
              {logoElement}
            </Link>
            <ul className="hidden md:flex gap-8">
              {["features", "how-it-works", "pricing", "testimonials"].map(
                (item) => (
                  <li key={item}>
                    <a
                      href={`#${item}`}
                      className="font-medium text-dark relative after:content-[''] after:absolute after:bottom-[-5px] after:left-0 after:w-0 after:h-0.5 after:bg-gradient-to-r after:from-blue-500 after:to-blue-700 after:transition-all hover:after:w-full"
                    >
                      {item
                        .split("-")
                        .map(
                          (word) => word.charAt(0).toUpperCase() + word.slice(1)
                        )
                        .join(" ")}
                    </a>
                  </li>
                )
              )}
            </ul>
            <div className="hidden md:flex items-center gap-5">
              <Button
                className="inline-flex items-center justify-center px-7 py-3 rounded-lg font-semibold text-blue-600 border-2 border-blue-600 transition-all hover:bg-blue-50"
                onClick={() => navigate("/login")}
              >
                Log In
              </Button>
              <Button
                className="inline-flex items-center justify-center px-7 py-3 rounded-lg font-semibold text-white bg-gradient-to-r from-blue-500 to-blue-700 hover:translate-y-[-3px] hover:shadow-lg transition-all"
                onClick={() => navigate("/Signup")}
              >
                Get Started
              </Button>
            </div>
            <button
              className="md:hidden text-2xl text-blue-600"
              onClick={() => setMobileNavOpen(true)}
            >
              <MenuIcon size={24} />
            </button>
          </div>
        </div>
      </header>

      {/* Mobile Navigation */}
      <div
        className={`fixed top-0 left-0 w-full h-full bg-white z-50 transform transition-transform duration-300 ${
          mobileNavOpen ? "translate-x-0" : "translate-x-full"
        }`}
      >
        <div className="p-5">
          <div className="flex justify-between items-center mb-8">
            <Link
              to="#"
              className="flex items-center gap-2 text-xl font-bold text-blue-700"
            >
              {logoElement}
            </Link>
            <button
              className="text-2xl text-blue-600"
              onClick={() => setMobileNavOpen(false)}
            >
              <XIcon size={24} />
            </button>
          </div>
          <ul className="flex flex-col">
            {["features", "how-it-works", "pricing", "testimonials"].map(
              (item) => (
                <li key={item} className="py-4 border-b border-gray-200">
                  <a
                    href={`#${item}`}
                    className="text-lg font-medium text-blue-800"
                    onClick={() => setMobileNavOpen(false)}
                  >
                    {item
                      .split("-")
                      .map(
                        (word) => word.charAt(0).toUpperCase() + word.slice(1)
                      )
                      .join(" ")}
                  </a>
                </li>
              )
            )}
            <li className="py-4 border-b border-gray-200">
              <Link
                to="/login"
                className="text-lg font-medium text-blue-600"
                onClick={() => setMobileNavOpen(false)}
              >
                Log In
              </Link>
            </li>
            <li className="py-4">
              <Link
                to="/signup"
                className="text-lg font-medium text-blue-700"
                onClick={() => setMobileNavOpen(false)}
              >
                Get Started
              </Link>
            </li>
          </ul>
        </div>
      </div>
    </>
  );
};

export default Header;
