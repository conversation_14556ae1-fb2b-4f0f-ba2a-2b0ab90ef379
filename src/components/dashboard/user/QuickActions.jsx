// src/components/dashboard/QuickActions.jsx
import React from "react";
import BookingModal from "../BookingModal";
import { XIcon } from "lucide-react";

// open booking modal when button"book a spce" is clicked and close it when the close(x) button is clicked

const QuickActions = () => {
  const [isModalOpen, setIsModalOpen] = React.useState(false);
  const handleOpenModal = () => {
    setIsModalOpen(true);
  };
  const handleCloseModal = () => {
    setIsModalOpen(false);
  };
  return (
    <div className="bg-white shadow-md rounded-xl p-6">
      <h2 className="text-lg font-semibold mb-4">Quick Actions</h2>
      <div className="flex space-x-4">
        <button
          onClick={handleOpenModal}
          className="bg-primary text-white px-4 py-2 rounded-xl hover:bg-hover"
        >
          Book a Space
        </button>

        <button className="bg-white text-gray-700 px-4 py-2 rounded-lg border border-gray-300 hover:bg-gray-50">
          View Bookings
        </button>
      </div>

      <BookingModal isOpen={isModalOpen} onClose={handleCloseModal} />
    </div>
  );
};
export default QuickActions;

// return (
//   <div className="bg-white shadow-md rounded-xl p-6">
//     <h2 className="text-lg font-semibold mb-4">Quick Actions</h2>
//     <button
//       onClick={handleOpenModal}
//       className="bg-primary text-white px-4 py-2 margin rounded-xl hover:bg-hover"
//     >
//       Book a Space
//     </button>

//     <button className="bg-white text-gray-700 px-4 py-2 rounded-lg border border-gray-300 hover:bg-gray-50">
//       View Bookings
//     </button>

//     <BookingModal isOpen={isModalOpen} onClose={handleCloseModal} />
//   </div>
// );
