/** @type {import('tailwindcss').Config} */
export default {
  content: ["./index.html", "./src/**/*.{js,jsx,ts,tsx}"],
  theme: {
    extend: {
      colors: {
        primary: "#2E7EE8",
        secondary: "#1C3FAA", // Adjust this based on your preferred blue shade
        success: "#05F283",
        warning: "#FACC15",
        danger: "#EF4444",
        inactive: "#E5E7EB",
        hover: "#2563EB",
        dark: "#111827",
      },
      boxShadow: {
        card: "0 4px 12px rgba(0, 0, 0, 0.08)",
        input: "0 2px 4px rgba(0, 0, 0, 0.05)",
      },
      fontFamily: {
        sans: ["IBM Plex Sans", "sans serif"],
        serif: ["IBM Plex Serif", "serif"],
        mono: ["IBM Plex Mono", "monospace"],
        display: ["IBM Plex Sans", "sans serif"],
      },
      fontSize: {
        heading1: "2.25rem",
        heading2: "1.5rem",
        body: "1rem",
        small: "0.875rem",
      },
      backgroundImage: {
        gradient: "linear-gradient(to right, #2E7EE8, #05F283)",
      },
    },
  },
  plugins: [],
};
