@import "tailwindcss";
@import "flowbite/src/themes/default";

@plugin "flowbite/plugin";
@source "../node_modules/flowbite";

@theme {
  /* Colors */
  --color-primary: #2e7ee8;
  --color-secondary: #1c3faa;
  --color-success: #05f283;
  --color-warning: #facc15;
  --color-danger: #ef4444;
  --color-inactive: #e5e7eb;
  --color-hover: #2563eb;
  --color-dark: #111827;

  /* Shadows */
  --shadow-card: 0 4px 12px rgba(0, 0, 0, 0.08);
  --shadow-input: 0 2px 4px rgba(0, 0, 0, 0.05);

  /* Font Sizes */
  --font-sans: "IBM Plex Sans", sans-serif;
  --text-heading1: 2.25rem;
  --text-heading2: 1.5rem;
  --text-body: 1rem;
  --text-small: 0.875rem;

  /* Background Gradient */
  --background-gradient: linear-gradient(to right, #2e7ee8, #05f283);
}

/* margin and padding reset */
body {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

@layer base {
  body {
    @apply text-body text-dark font-sans;
  }

  small {
    @apply text-small text-inactive;
  }

  button {
    @apply px-4 py-2 rounded-md text-white transition;
  }

  input,
  textarea {
    @apply border border-inactive p-2 rounded-md shadow-input focus:outline-none focus:border-primary;
  }
}
