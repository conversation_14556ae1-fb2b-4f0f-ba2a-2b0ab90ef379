import React, { createContext, useContext, useReducer, useCallback } from 'react';
import { spaceService, amenityService } from '../services';
import { useApp } from './AppContext';

// Initial state
const initialState = {
  spaces: [],
  amenities: [],
  selectedSpace: null,
  spaceForm: {
    name: '',
    description: '',
    capacity: 1,
    type: 'meeting_room',
    floor: '',
    room: '',
    hourly_rate: 0,
    amenities: [],
    is_active: true,
    images: [],
  },
  filters: {
    search: '',
    type: 'all',
    capacity: 'all',
    availability: 'all',
    floor: 'all',
    amenities: [],
  },
  viewMode: 'grid', // 'grid' or 'list'
  sortBy: 'name',
  sortOrder: 'asc',
  loading: {
    spaces: false,
    amenities: false,
    creating: false,
    updating: false,
    deleting: false,
  },
  error: null,
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0,
  },
};

// Action types
const actionTypes = {
  SET_SPACES: 'SET_SPACES',
  SET_AMENITIES: 'SET_AMENITIES',
  SET_SELECTED_SPACE: 'SET_SELECTED_SPACE',
  UPDATE_SPACE_FORM: 'UPDATE_SPACE_FORM',
  RESET_SPACE_FORM: 'RESET_SPACE_FORM',
  SET_FILTERS: 'SET_FILTERS',
  SET_VIEW_MODE: 'SET_VIEW_MODE',
  SET_SORT: 'SET_SORT',
  SET_LOADING: 'SET_LOADING',
  SET_ERROR: 'SET_ERROR',
  CLEAR_ERROR: 'CLEAR_ERROR',
  ADD_SPACE: 'ADD_SPACE',
  UPDATE_SPACE: 'UPDATE_SPACE',
  REMOVE_SPACE: 'REMOVE_SPACE',
  SET_PAGINATION: 'SET_PAGINATION',
};

// Reducer
const spaceReducer = (state, action) => {
  switch (action.type) {
    case actionTypes.SET_SPACES:
      return { ...state, spaces: action.payload };
      
    case actionTypes.SET_AMENITIES:
      return { ...state, amenities: action.payload };
      
    case actionTypes.SET_SELECTED_SPACE:
      return { ...state, selectedSpace: action.payload };
      
    case actionTypes.UPDATE_SPACE_FORM:
      return {
        ...state,
        spaceForm: { ...state.spaceForm, ...action.payload },
      };
      
    case actionTypes.RESET_SPACE_FORM:
      return { ...state, spaceForm: initialState.spaceForm };
      
    case actionTypes.SET_FILTERS:
      return { ...state, filters: { ...state.filters, ...action.payload } };
      
    case actionTypes.SET_VIEW_MODE:
      return { ...state, viewMode: action.payload };
      
    case actionTypes.SET_SORT:
      return { 
        ...state, 
        sortBy: action.payload.sortBy,
        sortOrder: action.payload.sortOrder,
      };
      
    case actionTypes.SET_LOADING:
      return {
        ...state,
        loading: { ...state.loading, [action.payload.key]: action.payload.value },
      };
      
    case actionTypes.SET_ERROR:
      return { ...state, error: action.payload };
      
    case actionTypes.CLEAR_ERROR:
      return { ...state, error: null };
      
    case actionTypes.ADD_SPACE:
      return {
        ...state,
        spaces: [action.payload, ...state.spaces],
        pagination: {
          ...state.pagination,
          total: state.pagination.total + 1,
        },
      };
      
    case actionTypes.UPDATE_SPACE:
      return {
        ...state,
        spaces: state.spaces.map(s =>
          s.id === action.payload.id ? action.payload : s
        ),
      };
      
    case actionTypes.REMOVE_SPACE:
      return {
        ...state,
        spaces: state.spaces.filter(s => s.id !== action.payload),
        pagination: {
          ...state.pagination,
          total: Math.max(0, state.pagination.total - 1),
        },
      };
      
    case actionTypes.SET_PAGINATION:
      return { ...state, pagination: { ...state.pagination, ...action.payload } };
      
    default:
      return state;
  }
};

// Create context
const SpaceContext = createContext();

// Provider component
export const SpaceProvider = ({ children }) => {
  const [state, dispatch] = useReducer(spaceReducer, initialState);
  const { actions: appActions } = useApp();

  // Helper function to handle errors
  const handleError = useCallback((error, operation) => {
    console.error(`Space ${operation} error:`, error);
    const errorMessage = error.response?.data?.message || error.message || `Failed to ${operation}`;
    dispatch({ type: actionTypes.SET_ERROR, payload: errorMessage });
    appActions.setError(errorMessage);
  }, [appActions]);

  // Action creators
  const actions = {
    // Load all spaces
    loadSpaces: useCallback(async (params = {}) => {
      try {
        dispatch({ type: actionTypes.SET_LOADING, payload: { key: 'spaces', value: true } });
        dispatch({ type: actionTypes.CLEAR_ERROR });
        
        const response = await spaceService.getAllSpaces({
          page: state.pagination.page,
          limit: state.pagination.limit,
          ...params,
        });
        
        dispatch({ type: actionTypes.SET_SPACES, payload: response.data.results || response.data });
        
        if (response.data.pagination) {
          dispatch({ type: actionTypes.SET_PAGINATION, payload: response.data.pagination });
        }
      } catch (error) {
        handleError(error, 'load spaces');
      } finally {
        dispatch({ type: actionTypes.SET_LOADING, payload: { key: 'spaces', value: false } });
      }
    }, [handleError, state.pagination.page, state.pagination.limit]),

    // Load amenities
    loadAmenities: useCallback(async () => {
      try {
        dispatch({ type: actionTypes.SET_LOADING, payload: { key: 'amenities', value: true } });
        
        const response = await amenityService.getAllAmenities();
        dispatch({ type: actionTypes.SET_AMENITIES, payload: response.data });
      } catch (error) {
        handleError(error, 'load amenities');
      } finally {
        dispatch({ type: actionTypes.SET_LOADING, payload: { key: 'amenities', value: false } });
      }
    }, [handleError]),

    // Get space by ID
    getSpace: useCallback(async (spaceId) => {
      try {
        dispatch({ type: actionTypes.SET_LOADING, payload: { key: 'spaces', value: true } });
        dispatch({ type: actionTypes.CLEAR_ERROR });
        
        const response = await spaceService.getSpaceById(spaceId);
        dispatch({ type: actionTypes.SET_SELECTED_SPACE, payload: response.data });
        
        return response.data;
      } catch (error) {
        handleError(error, 'load space');
        throw error;
      } finally {
        dispatch({ type: actionTypes.SET_LOADING, payload: { key: 'spaces', value: false } });
      }
    }, [handleError]),

    // Create space
    createSpace: useCallback(async (spaceData) => {
      try {
        dispatch({ type: actionTypes.SET_LOADING, payload: { key: 'creating', value: true } });
        dispatch({ type: actionTypes.CLEAR_ERROR });
        
        const response = await spaceService.createSpace(spaceData);
        dispatch({ type: actionTypes.ADD_SPACE, payload: response.data });
        dispatch({ type: actionTypes.RESET_SPACE_FORM });
        
        appActions.addNotification({
          id: Date.now(),
          type: 'success',
          title: 'Space Created',
          message: 'The space has been created successfully.',
          timestamp: new Date().toISOString(),
        });
        
        return response.data;
      } catch (error) {
        handleError(error, 'create space');
        throw error;
      } finally {
        dispatch({ type: actionTypes.SET_LOADING, payload: { key: 'creating', value: false } });
      }
    }, [handleError, appActions]),

    // Update space
    updateSpace: useCallback(async (spaceId, spaceData) => {
      try {
        dispatch({ type: actionTypes.SET_LOADING, payload: { key: 'updating', value: true } });
        dispatch({ type: actionTypes.CLEAR_ERROR });
        
        const response = await spaceService.updateSpace(spaceId, spaceData);
        dispatch({ type: actionTypes.UPDATE_SPACE, payload: response.data });
        
        appActions.addNotification({
          id: Date.now(),
          type: 'success',
          title: 'Space Updated',
          message: 'The space has been updated successfully.',
          timestamp: new Date().toISOString(),
        });
        
        return response.data;
      } catch (error) {
        handleError(error, 'update space');
        throw error;
      } finally {
        dispatch({ type: actionTypes.SET_LOADING, payload: { key: 'updating', value: false } });
      }
    }, [handleError, appActions]),

    // Delete space
    deleteSpace: useCallback(async (spaceId) => {
      try {
        dispatch({ type: actionTypes.SET_LOADING, payload: { key: 'deleting', value: true } });
        dispatch({ type: actionTypes.CLEAR_ERROR });
        
        await spaceService.deleteSpace(spaceId);
        dispatch({ type: actionTypes.REMOVE_SPACE, payload: spaceId });
        
        appActions.addNotification({
          id: Date.now(),
          type: 'info',
          title: 'Space Deleted',
          message: 'The space has been deleted successfully.',
          timestamp: new Date().toISOString(),
        });
      } catch (error) {
        handleError(error, 'delete space');
        throw error;
      } finally {
        dispatch({ type: actionTypes.SET_LOADING, payload: { key: 'deleting', value: false } });
      }
    }, [handleError, appActions]),

    // Get available spaces
    getAvailableSpaces: useCallback(async (params = {}) => {
      try {
        dispatch({ type: actionTypes.SET_LOADING, payload: { key: 'spaces', value: true } });
        dispatch({ type: actionTypes.CLEAR_ERROR });
        
        const response = await spaceService.getAvailableSpaces(params);
        return response.data;
      } catch (error) {
        handleError(error, 'load available spaces');
        throw error;
      } finally {
        dispatch({ type: actionTypes.SET_LOADING, payload: { key: 'spaces', value: false } });
      }
    }, [handleError]),

    // UI actions
    setSelectedSpace: (space) => dispatch({ type: actionTypes.SET_SELECTED_SPACE, payload: space }),
    updateSpaceForm: (formData) => dispatch({ type: actionTypes.UPDATE_SPACE_FORM, payload: formData }),
    resetSpaceForm: () => dispatch({ type: actionTypes.RESET_SPACE_FORM }),
    setFilters: (filters) => dispatch({ type: actionTypes.SET_FILTERS, payload: filters }),
    setViewMode: (mode) => dispatch({ type: actionTypes.SET_VIEW_MODE, payload: mode }),
    setSort: (sortBy, sortOrder) => dispatch({ 
      type: actionTypes.SET_SORT, 
      payload: { sortBy, sortOrder } 
    }),
    setPagination: (pagination) => dispatch({ type: actionTypes.SET_PAGINATION, payload: pagination }),
    clearError: () => dispatch({ type: actionTypes.CLEAR_ERROR }),
  };

  return (
    <SpaceContext.Provider value={{ state, actions }}>
      {children}
    </SpaceContext.Provider>
  );
};

// Custom hook to use the context
export const useSpace = () => {
  const context = useContext(SpaceContext);
  if (!context) {
    throw new Error('useSpace must be used within a SpaceProvider');
  }
  return context;
};

export default SpaceContext;
