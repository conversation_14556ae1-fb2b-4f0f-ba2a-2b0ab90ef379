import { NavLink } from "react-router-dom";
import PropTypes from "prop-types";
import { useState } from "react";

import {
  LayoutDashboard,
  Users,
  Calendar,
  Settings,
  BarChart4,
  LogOut,
  Building,
  ClipboardList,
  Wrench,
  UserCheck,
  ShieldCheck,
  FileText,
  Pie<PERSON>hart,
  <PERSON>liders,
  Bell,
} from "lucide-react";

import UserManagement from "./UserManagement";

function AdminSidebar({ isOpen, setIsOpen }) {
  const [currentPage, setCurrentPage] = useState("dashboard");

  const navItems = [
    {
      heading: "Admin Dashboard",
      items: [
        {
          id: "/admin/dashboard/adminDashboard",
          icon: <LayoutDashboard size={20} />,
          label: "Home",
        },
      ],
    },
    {
      heading: "Space Management",
      items: [
        {
          id: "/admin/dashboard/floor-Plan",
          icon: <Building size={20} />,
          label: "Floor Plan Editor",
        },
        {
          id: "/admin/dashboard/space",
          icon: <ClipboardList size={20} />,
          label: "Space List",
        },
        {
          id: "/admin/space/details",
          icon: <Building size={20} />,
          label: "Space Details",
        },
        {
          id: "/admin/space/maintenance",
          icon: <Wrench size={20} />,
          label: "Maintenance Schedule",
        },
      ],
    },
    {
      heading: "User Management",
      items: [
        {
          id: "/admin/dashboard/userManagement",
          icon: <UserCheck size={20} />,
          label: "User Directory",
        },
        {
          id: "/admin/dashboard/rolesPermission",
          icon: <ShieldCheck size={20} />,
          label: "Roles & Permissions",
        },
      ],
    },
    {
      heading: "Reports & Analytics",
      items: [
        {
          id: "/admin/reports/standard",
          icon: <FileText size={20} />,
          label: "Standard Reports",
        },
        {
          id: "/admin/reports/custom",
          icon: <PieChart size={20} />,
          label: "Custom Reports",
        },
        {
          id: "/admin/reports/interactive",
          icon: <BarChart4 size={20} />,
          label: "Interactive Dashboards",
        },
      ],
    },
    {
      heading: "System Configuration",
      items: [
        {
          id: "/admin/dashboard/booking-rules",
          icon: <Sliders size={20} />,
          label: "Booking Rules",
        },
        {
          id: "/admin/dashboard/integrations",
          icon: <Settings size={20} />,
          label: "Integrations",
        },
        {
          id: "/admin/dashboard/notifications",
          icon: <Bell size={20} />,
          label: "Notifications",
        },
        {
          id: "/admin/config/logout",
          icon: <LogOut size={20} />,
          label: "Log Out",
        },
      ],
    },
  ];

  const navigateTo = (pageId) => {
    setCurrentPage(pageId);

    // If clicking on a parent menu with submenu, expand it
    if (pageId === "user-management") {
      // You might want to add state to track expanded menus
      setCurrentPage("user-directory"); // Default to user directory when clicking on User Management
    }
  };

  return (
    <div
      className={`bg-white border-r border-gray-200 w-64 fixed inset-y-0 left-0 z-30 transition-transform duration-300 transform ${
        isOpen ? "translate-x-0" : "-translate-x-full"
      } md:translate-x-0 flex flex-col`}
    >
      {/* Logo */}
      <div className="px-6 pt-8 pb-6 flex items-center justify-between shrink-0">
        <img
          src="/images/logo-blue.png"
          alt="JechAdmin Logo"
          className="h-10 w-auto"
        />
        <button
          onClick={() => setIsOpen(false)}
          className="md:hidden text-gray-500 hover:text-gray-700"
        >
          <svg
            className="w-6 h-6"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>
      </div>

      {/* Navigation - with proper overflow handling */}
      <div className="px-4 space-y-6 overflow-y-auto flex-1 pb-8">
        {navItems.map((section, index) => (
          <div key={index} className="pb-2">
            <h2 className="text-sm font-bold text-gray-500 uppercase px-4">
              {section.heading}
            </h2>
            <div className="mt-2 space-y-1">
              {section.items.map((item) => (
                <NavLink
                  key={item.id}
                  to={item.id}
                  className={({ isActive }) =>
                    `flex items-center px-4 py-3 text-gray-700 rounded-lg hover:bg-blue-50 hover:text-blue-700 transition-colors ${
                      isActive ? "bg-blue-50 text-blue-700" : ""
                    }`
                  }
                >
                  <span className="mr-3">{item.icon}</span>
                  <span className="font-medium">{item.label}</span>
                </NavLink>
              ))}
            </div>
          </div>
        ))}
      </div>

      {/* show sidebar content on the main content */}
    </div>
  );
}

export default AdminSidebar;
AdminSidebar.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  setIsOpen: PropTypes.func.isRequired,
};
