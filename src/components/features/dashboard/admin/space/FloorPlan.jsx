import React, { useState, useEffect, useRef } from 'react';

const FloorPlan = () => {
  // State for managing rooms and layout
  const [rooms, setRooms] = useState([
    { id: 'room1', name: 'Meeting Room 1', x: 50, y: 50, width: 100, height: 80, available: true, color: '#4ADE80' },
    { id: 'room2', name: 'Meeting Room 2', x: 170, y: 50, width: 100, height: 80, available: false, color: '#F87171' },
    { id: 'room3', name: 'Office Space A', x: 50, y: 150, width: 220, height: 80, available: true, color: '#4ADE80' },
    { id: 'room4', name: 'Lounge Area', x: 300, y: 50, width: 120, height: 180, available: true, color: '#4ADE80' },
    { id: 'room5', name: 'Conference Room', x: 50, y: 250, width: 370, height: 100, available: false, color: '#F87171' },
  ]);
  
  // UI Control States
  const [isAdmin, setIsAdmin] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [selectedRoom, setSelectedRoom] = useState(null);
  const [showBookingModal, setShowBookingModal] = useState(false);
  const [showRoomPropertiesModal, setShowRoomPropertiesModal] = useState(false);
  const [drawingMode, setDrawingMode] = useState(false);
  
  // Room manipulation states
  const [newRoom, setNewRoom] = useState(null);
  const [draggedRoom, setDraggedRoom] = useState(null);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [resizingRoom, setResizingRoom] = useState(null);
  const [resizeHandle, setResizeHandle] = useState('');
  const [roomProperties, setRoomProperties] = useState({
    name: '',
    color: '#4ADE80',
    available: true
  });
  
  // Booking form state
  const [bookingDetails, setBookingDetails] = useState({
    name: '',
    email: '',
    date: '',
    startTime: '',
    endTime: '',
    purpose: ''
  });
  
  // Floor plan file state
  const [floorPlanName, setFloorPlanName] = useState('Main Office');
  const [savedFloorPlans, setSavedFloorPlans] = useState(['Main Office', 'Second Floor']);
  const [showSaveModal, setShowSaveModal] = useState(false);
  const [showLoadModal, setShowLoadModal] = useState(false);
  const [newFloorPlanName, setNewFloorPlanName] = useState('');
  
  const svgRef = useRef(null);
  
  // Get SVG coordinates for accurate mouse positioning
  const getSVGCoordinates = (event) => {
    const svg = svgRef.current;
    if (!svg) return { x: 0, y: 0 };
    
    const CTM = svg.getScreenCTM();
    const pt = svg.createSVGPoint();
    pt.x = event.clientX;
    pt.y = event.clientY;
    const svgPoint = pt.matrixTransform(CTM.inverse());
    
    return { x: svgPoint.x, y: svgPoint.y };
  };
  
  // Room click handler - different behavior depending on mode
  const handleRoomClick = (room, event) => {
    event.stopPropagation();
    
    // Don't select room if we're dragging or resizing
    if (draggedRoom || newRoom || resizingRoom) return;
    
    if (isEditMode) {
      // Double click to edit room properties
      if (event.detail === 2) {
        setSelectedRoom(room);
        setRoomProperties({
          name: room.name,
          color: room.color || (room.available ? '#4ADE80' : '#F87171'),
          available: room.available
        });
        setShowRoomPropertiesModal(true);
        return;
      }
      
      // For right-click in edit mode
      if (event.button === 2) {
        event.preventDefault();
        setSelectedRoom(room);
        setRoomProperties({
          name: room.name,
          color: room.color || (room.available ? '#4ADE80' : '#F87171'),
          available: room.available
        });
        setShowRoomPropertiesModal(true);
        return;
      }
    } else {
      // Normal mode - select room for booking
      setSelectedRoom(room);
    }
  };
  
  // Drawing functions for new rooms
  const handleDrawStart = (event) => {
    if (!drawingMode || !isEditMode) return;
    
    event.preventDefault();
    const coords = getSVGCoordinates(event);
    
    setNewRoom({
      id: `room${Date.now()}`,
      name: 'New Room',
      x: coords.x,
      y: coords.y,
      width: 0,
      height: 0,
      available: true,
      color: '#4ADE80'
    });
  };
  
  const handleDrawMove = (event) => {
    if (!newRoom || !drawingMode) return;
    
    event.preventDefault();
    const coords = getSVGCoordinates(event);
    
    setNewRoom(prev => ({
      ...prev,
      width: Math.max(20, coords.x - prev.x),
      height: Math.max(20, coords.y - prev.y)
    }));
  };
  
  const handleDrawEnd = () => {
    if (!newRoom || !drawingMode) return;
    
    // Add room only if it has a minimum size
    if (newRoom.width > 20 && newRoom.height > 20) {
      setRooms(prev => [...prev, newRoom]);
      
      // Open properties modal for the new room
      setSelectedRoom(newRoom);
      setRoomProperties({
        name: newRoom.name,
        color: newRoom.color,
        available: newRoom.available
      });
      setShowRoomPropertiesModal(true);
    }
    
    setNewRoom(null);
    setDrawingMode(false); // Exit drawing mode after adding a room
  };
  
  // Room dragging functions
  const handleRoomDragStart = (room, event) => {
    if (!isEditMode) return;
    
    event.stopPropagation();
    event.preventDefault();
    const coords = getSVGCoordinates(event);
    
    setDraggedRoom(room);
    setDragStart({
      x: coords.x - room.x,
      y: coords.y - room.y
    });
  };
  
  const handleRoomDragMove = (event) => {
    if (!draggedRoom) return;
    
    event.preventDefault();
    const coords = getSVGCoordinates(event);
    
    setRooms(prevRooms => 
      prevRooms.map(room => 
        room.id === draggedRoom.id
          ? {
              ...room,
              x: Math.max(0, coords.x - dragStart.x),
              y: Math.max(0, coords.y - dragStart.y)
            }
          : room
      )
    );
  };
  
  const handleRoomDragEnd = () => {
    setDraggedRoom(null);
  };
  
  // Room resizing functions
  const handleResizeStart = (room, handle, event) => {
    if (!isEditMode) return;
    
    event.stopPropagation();
    event.preventDefault();
    
    setResizingRoom(room);
    setResizeHandle(handle);
    setDragStart(getSVGCoordinates(event));
  };
  
  const handleResizeMove = (event) => {
    if (!resizingRoom) return;
    
    event.preventDefault();
    const coords = getSVGCoordinates(event);
    const deltaX = coords.x - dragStart.x;
    const deltaY = coords.y - dragStart.y;
    
    setRooms(prevRooms => 
      prevRooms.map(room => {
        if (room.id !== resizingRoom.id) return room;
        
        let updatedRoom = { ...room };
        
        // Handle different resize directions
        switch (resizeHandle) {
          case 'n':
            updatedRoom.y = Math.min(room.y + room.height - 20, room.y + deltaY);
            updatedRoom.height = Math.max(20, room.height - deltaY);
            break;
          case 's':
            updatedRoom.height = Math.max(20, room.height + deltaY);
            break;
          case 'e':
            updatedRoom.width = Math.max(20, room.width + deltaX);
            break;
          case 'w':
            updatedRoom.x = Math.min(room.x + room.width - 20, room.x + deltaX);
            updatedRoom.width = Math.max(20, room.width - deltaX);
            break;
          case 'ne':
            updatedRoom.y = Math.min(room.y + room.height - 20, room.y + deltaY);
            updatedRoom.height = Math.max(20, room.height - deltaY);
            updatedRoom.width = Math.max(20, room.width + deltaX);
            break;
          case 'nw':
            updatedRoom.y = Math.min(room.y + room.height - 20, room.y + deltaY);
            updatedRoom.height = Math.max(20, room.height - deltaY);
            updatedRoom.x = Math.min(room.x + room.width - 20, room.x + deltaX);
            updatedRoom.width = Math.max(20, room.width - deltaX);
            break;
          case 'se':
            updatedRoom.height = Math.max(20, room.height + deltaY);
            updatedRoom.width = Math.max(20, room.width + deltaX);
            break;
          case 'sw':
            updatedRoom.height = Math.max(20, room.height + deltaY);
            updatedRoom.x = Math.min(room.x + room.width - 20, room.x + deltaX);
            updatedRoom.width = Math.max(20, room.width - deltaX);
            break;
          default:
            break;
        }
        
        return updatedRoom;
      })
    );
    
    setDragStart(coords);
  };
  
  const handleResizeEnd = () => {
    setResizingRoom(null);
    setResizeHandle('');
  };
  
  // Handling room properties changes
  const handlePropertyChange = (e) => {
    const { name, value, type, checked } = e.target;
    setRoomProperties(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };
  
  // Save room properties
  const saveRoomProperties = () => {
    if (!selectedRoom) return;
    
    setRooms(prevRooms => 
      prevRooms.map(room => 
        room.id === selectedRoom.id
          ? { 
              ...room, 
              name: roomProperties.name,
              color: roomProperties.color,
              available: roomProperties.available 
            }
          : room
      )
    );
    
    setShowRoomPropertiesModal(false);
    setSelectedRoom(null);
  };
  
  // Delete a room
  const deleteRoom = (roomId) => {
    setRooms(prevRooms => prevRooms.filter(room => room.id !== roomId));
    setShowRoomPropertiesModal(false);
    if (selectedRoom && selectedRoom.id === roomId) {
      setSelectedRoom(null);
    }
  };
  
  // Handle booking form input changes
  const handleBookingInputChange = (e) => {
    const { name, value } = e.target;
    setBookingDetails(prev => ({
      ...prev,
      [name]: value
    }));
  };
  
  // Process a booking
  const handleBooking = () => {
    // Here you would typically send this data to your backend
    console.log('Booking details:', { room: selectedRoom, ...bookingDetails });
    
    // Update room availability in local state
    setRooms(prevRooms => 
      prevRooms.map(room => 
        room.id === selectedRoom.id
          ? { ...room, available: false }
          : room
      )
    );
    
    // Reset and close
    setShowBookingModal(false);
    setSelectedRoom(null);
    setBookingDetails({
      name: '',
      email: '',
      date: '',
      startTime: '',
      endTime: '',
      purpose: ''
    });
  };
  
  // Save floor plan
  const saveFloorPlan = () => {
    // Here you would save to backend
    console.log('Floor plan saved:', {
      name: floorPlanName,
      rooms: rooms
    });
    
    // If it's a new name, add to saved list
    if (!savedFloorPlans.includes(floorPlanName)) {
      setSavedFloorPlans(prev => [...prev, floorPlanName]);
    }
    
    setShowSaveModal(false);
    alert('Floor plan saved successfully!');
  };
  
  // Handle floor plan name input
  const handleFloorPlanNameChange = (e) => {
    setNewFloorPlanName(e.target.value);
  };
  
  // Handle save as new floor plan
  const saveAsNewFloorPlan = () => {
    if (newFloorPlanName.trim() === '') return;
    
    setFloorPlanName(newFloorPlanName);
    setSavedFloorPlans(prev => [...prev, newFloorPlanName]);
    
    // Here you would save to backend with new name
    console.log('Floor plan saved as:', {
      name: newFloorPlanName,
      rooms: rooms
    });
    
    setShowSaveModal(false);
    setNewFloorPlanName('');
    alert(`Floor plan saved as "${newFloorPlanName}" successfully!`);
  };
  
  // Load a saved floor plan (mock implementation)
  const loadFloorPlan = (planName) => {
    // In a real app, you would fetch this from your backend
    console.log(`Loading floor plan: ${planName}`);
    
    // Mock data for demonstration - in real app would come from API
    const mockData = {
      'Main Office': [
        { id: 'room1', name: 'Meeting Room 1', x: 50, y: 50, width: 100, height: 80, available: true, color: '#4ADE80' },
        { id: 'room2', name: 'Meeting Room 2', x: 170, y: 50, width: 100, height: 80, available: false, color: '#F87171' },
        { id: 'room3', name: 'Office Space A', x: 50, y: 150, width: 220, height: 80, available: true, color: '#4ADE80' },
        { id: 'room4', name: 'Lounge Area', x: 300, y: 50, width: 120, height: 180, available: true, color: '#4ADE80' },
        { id: 'room5', name: 'Conference Room', x: 50, y: 250, width: 370, height: 100, available: false, color: '#F87171' },
      ],
      'Second Floor': [
        { id: 'room6', name: 'Executive Suite', x: 50, y: 50, width: 150, height: 100, available: true, color: '#4ADE80' },
        { id: 'room7', name: 'Boardroom', x: 220, y: 50, width: 200, height: 100, available: true, color: '#4ADE80' },
        { id: 'room8', name: 'Break Room', x: 50, y: 170, width: 120, height: 120, available: true, color: '#4ADE80' },
        { id: 'room9', name: 'IT Department', x: 190, y: 170, width: 230, height: 120, available: false, color: '#F87171' },
      ],
    };
    
    if (mockData[planName]) {
      setRooms(mockData[planName]);
      setFloorPlanName(planName);
    }
    
    setShowLoadModal(false);
  };
  
  // Calculate grid size based on room positions and dimensions
  const calculateGridSize = () => {
    if (rooms.length === 0) return { width: 800, height: 600 };
    
    const maxX = Math.max(...rooms.map(room => room.x + room.width));
    const maxY = Math.max(...rooms.map(room => room.y + room.height));
    
    return {
      width: Math.max(800, maxX + 100),
      height: Math.max(600, maxY + 100)
    };
  };
  
  const gridSize = calculateGridSize();
  
  // Get today's date in YYYY-MM-DD format
  const today = new Date().toISOString().split('T')[0];
  
  // Prevent context menu on right click when in edit mode
  useEffect(() => {
    const handleContextMenu = (event) => {
      if (isEditMode) {
        event.preventDefault();
      }
    };
    
    document.addEventListener('contextmenu', handleContextMenu);
    return () => {
      document.removeEventListener('contextmenu', handleContextMenu);
    };
  }, [isEditMode]);
  
  // Exit edit mode when switching from admin to user view
  useEffect(() => {
    if (!isAdmin) {
      setIsEditMode(false);
    }
  }, [isAdmin]);
  
  return (
    <div className="w-full h-full bg-gray-50 p-4">
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        {/* Header */}
        <div className="bg-blue-600 text-white p-4 flex justify-between items-center">
          <div className="flex items-center space-x-3">
            <h1 className="text-xl font-bold">Workspace Floor Plan</h1>
            <span className="px-2 py-1 bg-blue-700 rounded-lg text-sm">{floorPlanName}</span>
          </div>
          
          <div className="flex items-center space-x-3">
            {/* Admin mode toggle */}
            <div className="flex items-center">
              <input
                id="adminSwitch"
                type="checkbox"
                className="sr-only peer"
                checked={isAdmin}
                onChange={() => setIsAdmin(!isAdmin)}
              />
              <label
                htmlFor="adminSwitch"
                className="relative flex items-center cursor-pointer w-11 h-6 bg-gray-200 rounded-full peer-checked:bg-indigo-500 transition-colors"
              >
                <span className="absolute left-1 top-1 bg-white w-4 h-4 rounded-full transition-transform peer-checked:translate-x-5"></span>
              </label>
              <span className="ml-2 text-sm font-medium">Admin Mode</span>
            </div>
            
            {/* View selector */}
            <div className="flex border border-blue-700 rounded-lg overflow-hidden">
              <button className="py-1 px-3 text-sm bg-blue-700 text-white">Map</button>
            </div>
          </div>
        </div>
        
        {/* Control bar */}
        <div className="bg-white border-b p-3 flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="relative">
              <button className="py-2 px-3 border rounded-lg flex items-center space-x-2 text-gray-700">
                <span>Tue 6 May</span>
                <svg className="w-4 h-4" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>
            </div>
            
            <div className="relative">
              <button className="py-2 px-3 border rounded-lg flex items-center space-x-2 text-gray-700">
                <span>08:00</span>
                <svg className="w-4 h-4" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>
            </div>
          </div>
          
          {/* Admin controls */}
          {isAdmin && (
            <div className="flex items-center space-x-2">
              {isEditMode ? (
                <>
                  <button 
                    onClick={() => setDrawingMode(!drawingMode)}
                    className={`py-1 px-3 rounded-lg text-sm font-medium ${drawingMode 
                      ? 'bg-green-100 text-green-700 border border-green-300' 
                      : 'bg-gray-100 text-gray-700 border border-gray-300'}`}
                  >
                    {drawingMode ? 'Cancel Drawing' : 'Draw Room'}
                  </button>
                  
                  <button 
                    onClick={() => setShowSaveModal(true)}
                    className="py-1 px-3 bg-blue-50 text-blue-600 rounded-lg text-sm font-medium border border-blue-200"
                  >
                    Save Floor Plan
                  </button>
                  
                  <button 
                    onClick={() => setShowLoadModal(true)}
                    className="py-1 px-3 bg-gray-50 text-gray-600 rounded-lg text-sm font-medium border border-gray-200"
                  >
                    Load Floor Plan
                  </button>
                  
                  <button 
                    onClick={() => setIsEditMode(false)}
                    className="py-1 px-3 bg-red-50 text-red-600 rounded-lg text-sm font-medium border border-red-200"
                  >
                    Exit Edit Mode
                  </button>
                </>
              ) : (
                <button 
                  onClick={() => setIsEditMode(true)}
                  className="py-1 px-3 bg-blue-600 text-white rounded-lg text-sm font-medium"
                >
                  Edit Floor Plan
                </button>
              )}
            </div>
          )}
        </div>
        
        {/* Edit mode instructions */}
        {isEditMode && (
          <div className="bg-blue-50 border-b border-blue-100 p-3">
            <div className="flex items-center text-blue-800 text-sm">
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <p className="font-medium">Edit Mode Instructions:</p>
            </div>
            <ul className="ml-7 mt-1 text-xs text-blue-700 grid grid-cols-2 gap-x-4">
              <li className="flex items-center">
                <span className="w-1 h-1 bg-blue-700 rounded-full mr-1"></span>
                Click "Draw Room" and drag on the grid to create a new room
              </li>
              <li className="flex items-center">
                <span className="w-1 h-1 bg-blue-700 rounded-full mr-1"></span>
                Drag existing rooms to reposition them
              </li>
              <li className="flex items-center">
                <span className="w-1 h-1 bg-blue-700 rounded-full mr-1"></span>
                Use the handles to resize rooms
              </li>
              <li className="flex items-center">
                <span className="w-1 h-1 bg-blue-700 rounded-full mr-1"></span>
                Double-click a room to edit properties
              </li>
            </ul>
          </div>
        )}
        
        {/* Legend */}
        <div className="bg-white border-b border-gray-100 p-2 flex items-center justify-end space-x-4">
          <div className="flex items-center">
            <div className="w-3 h-3 bg-green-400 rounded-sm mr-1"></div>
            <span className="text-xs text-gray-600">Available</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-red-400 rounded-sm mr-1"></div>
            <span className="text-xs text-gray-600">Booked</span>
          </div>
        </div>
        
        {/* Floor plan canvas */}
        <div 
          className="bg-white relative overflow-auto"
          style={{ height: 'calc(100vh - 240px)' }}
          onMouseUp={() => {
            handleDrawEnd();
            handleRoomDragEnd();
            handleResizeEnd();
          }}
          onMouseLeave={() => {
            handleRoomDragEnd();
            handleResizeEnd();
          }}
        >
          <svg 
            ref={svgRef}
            width={gridSize.width} 
            height={gridSize.height} 
            viewBox={`0 0 ${gridSize.width} ${gridSize.height}`}
            className="block"
            onMouseDown={isEditMode && drawingMode ? handleDrawStart : undefined}
            onMouseMove={(e) => {
              if (!isEditMode) return;
              handleDrawMove(e);
              handleRoomDragMove(e);
              handleResizeMove(e);
            }}
          >
            {/* Grid background */}
            <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
              <path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(0,0,0,0.05)" strokeWidth="1"/>
            </pattern>
            <rect width="100%" height="100%" fill="url(#grid)" />
            
            {/* Rooms */}
            {rooms.map(room => (
              <g 
                key={room.id} 
                onClick={(e) => handleRoomClick(room, e)}
                onMouseDown={(e) => isEditMode && handleRoomDragStart(room, e)}
                style={{ cursor: isEditMode ? 'move' : 'pointer' }}
              >
                <rect
                  x={room.x}
                  y={room.y}
                  width={room.width}
                  height={room.height}
                  rx="4"
                  fill={`${room.color || (room.available ? '#4ADE80' : '#F87171')}33`} // 20% opacity
                  stroke={room.color || (room.available ? '#4ADE80' : '#F87171')}
                  strokeWidth="2"
                />
                <text
                  x={room.x + room.width/2}
                  y={room.y + room.height/2}
                  textAnchor="middle"
                  dominantBaseline="middle"
                  fontSize="12"
                  fontWeight="500"
                  fill="#333"
                >
                  {room.name}
                </text>
                
                {/* Resize handles (only visible in edit mode) */}
                {isEditMode && (
                  <>
                    {/* Corner handles */}
                    <circle cx={room.x} cy={room.y} r="4" fill="#2563eb" 
                      onMouseDown={(e) => handleResizeStart(room, 'nw', e)}
                      style={{ cursor: 'nwse-resize' }}
                    />
                    <circle cx={room.x + room.width} cy={room.y} r="4" fill="#2563eb" 
                      onMouseDown={(e) => handleResizeStart(room, 'ne', e)}
                      style={{ cursor: 'nesw-resize' }}
                    />
                    <circle cx={room.x} cy={room.y + room.height} r="4" fill="#2563eb" 
                      onMouseDown={(e) => handleResizeStart(room, 'sw', e)}
                      style={{ cursor: 'nesw-resize' }}
                    />
                    <circle cx={room.x + room.width} cy={room.y + room.height} r="4" fill="#2563eb" 
                      onMouseDown={(e) => handleResizeStart(room, 'se', e)}
                      style={{ cursor: 'nwse-resize' }}
                    />
                    
                    <circle cx={room.x + room.width/2} cy={room.y} r="4" fill="#2563eb" 
                      onMouseDown={(e) => handleResizeStart(room, 'n', e)}
                      style={{ cursor: 'ns-resize' }}
                    />
                    <circle cx={room.x + room.width} cy={room.y + room.height/2} r="4" fill="#2563eb" 
                      onMouseDown={(e) => handleResizeStart(room, 'e', e)}
                      style={{ cursor: 'ew-resize' }}
                    />
                    <circle cx={room.x + room.width/2} cy={room.y + room.height} r="4" fill="#2563eb" 
                      onMouseDown={(e) => handleResizeStart(room, 's', e)}
                      style={{ cursor: 'ns-resize' }}
                    />
                   <circle cx={room.x + room.width/2} cy={room.y} r="4" fill="#2563eb" 
                      onMouseDown={(e) => handleResizeStart(room, 'n', e)}
                      style={{ cursor: 'ns-resize' }}
                    />
                    <circle cx={room.x + room.width} cy={room.y + room.height/2} r="4" fill="#2563eb" 
                      onMouseDown={(e) => handleResizeStart(room, 'e', e)}
                      style={{ cursor: 'ew-resize' }}
                    />
                    <circle cx={room.x + room.width/2} cy={room.y + room.height} r="4" fill="#2563eb" 
                      onMouseDown={(e) => handleResizeStart(room, 's', e)}
                      style={{ cursor: 'ns-resize' }}
                    />
                   <circle cx={room.x + room.width/2} cy={room.y} r="4" fill="#2563eb" 
                      onMouseDown={(e) => handleResizeStart(room, 'n', e)}
                      style={{ cursor: 'ns-resize' }}
                    />
                    <circle cx={room.x + room.width} cy={room.y + room.height/2} r="4" fill="#2563eb" 
                      onMouseDown={(e) => handleResizeStart(room, 'e', e)}
                      style={{ cursor: 'ew-resize' }}
                    />
                    <circle cx={room.x + room.width/2} cy={room.y + room.height} r="4" fill="#2563eb" 
                      onMouseDown={(e) => handleResizeStart(room, 's', e)}
                      style={{ cursor: 'ns-resize' }}
                    />
                    <circle cx={room.x} cy={room.y + room.height/2} r="4" fill="#2563eb" 
                      onMouseDown={(e) => handleResizeStart(room, 'w', e)}
                      style={{ cursor: 'ew-resize' }}
                    />
                  </>
                )}
              </g>
            ))}
            
            {/* Room currently being drawn */}
            {newRoom && (
              <rect
                x={newRoom.x}
                y={newRoom.y}
                width={newRoom.width}
                height={newRoom.height}
                rx="4"
                fill="#60A5FA33"
                stroke="#60A5FA"
                strokeWidth="2"
                strokeDasharray="4"
              />
            )}
          </svg>
        </div>
      </div>
      
      {/* Room details sidebar when room is selected */}
      {selectedRoom && !isEditMode && !showBookingModal && (
        <div className="fixed right-4 top-20 w-72 bg-white rounded-lg shadow-lg border overflow-hidden">
          <div className="p-4 border-b">
            <h2 className="font-medium text-lg">{selectedRoom.name}</h2>
            <div className="flex items-center mt-1">
            <div className={`w-3 h-3 rounded-full ${selectedRoom.available ? 'bg-green-500' : 'bg-red-500'} mr-2`}></div>
              <span className="text-sm text-gray-600">{selectedRoom.available ? 'Available' : 'Booked'}</span>
            </div>
          </div>
          
          <div className="p-4">
            {selectedRoom.available ? (
              <>
                <p className="text-sm text-gray-600 mb-4">This room is currently available for booking.</p>
                <button
                  onClick={() => setShowBookingModal(true)}
                  className="w-full py-2 px-4 bg-blue-600 hover:bg-blue-700 text-white rounded-lg"
                >
                  Book This Room
                </button>
              </>
            ) : (
              <>
                <p className="text-sm text-gray-600 mb-2">This room is currently booked.</p>
                <div className="text-sm text-gray-700">
                  <div className="flex justify-between py-1 border-b">
                    <span>Time:</span>
                    <span className="font-medium">9:00 AM - 11:00 AM</span>
                  </div>
                  <div className="flex justify-between py-1 border-b">
                    <span>Booked by:</span>
                    <span className="font-medium">John Doe</span>
                  </div>
                  <div className="flex justify-between py-1">
                    <span>Purpose:</span>
                    <span className="font-medium">Team Meeting</span>
                  </div>
                </div>
              </>
            )}
          </div>
          
          <button
            onClick={() => setSelectedRoom(null)}
            className="absolute top-2 right-2 text-gray-400 hover:text-gray-600"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      )}
      
      {/* Room booking modal */}
      {showBookingModal && selectedRoom && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-md">
            <div className="p-6 border-b">
              <h2 className="text-xl font-medium">Book {selectedRoom.name}</h2>
            </div>
            
            <div className="p-6">
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Your Name</label>
                  <input
                    type="text"
                    name="name"
                    value={bookingDetails.name}
                    onChange={handleBookingInputChange}
                    className="w-full px-3 py-2 border rounded-md"
                    placeholder="Enter your name"
                    required
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                  <input
                    type="email"
                    name="email"
                    value={bookingDetails.email}
                    onChange={handleBookingInputChange}
                    className="w-full px-3 py-2 border rounded-md"
                    placeholder="<EMAIL>"
                    required
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Date</label>
                  <input
                    type="date"
                    name="date"
                    value={bookingDetails.date}
                    onChange={handleBookingInputChange}
                    min={today}
                    className="w-full px-3 py-2 border rounded-md"
                    required
                  />
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Start Time</label>
                    <input
                      type="time"
                      name="startTime"
                      value={bookingDetails.startTime}
                      onChange={handleBookingInputChange}
                      className="w-full px-3 py-2 border rounded-md"
                      required
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">End Time</label>
                    <input
                      type="time"
                      name="endTime"
                      value={bookingDetails.endTime}
                      onChange={handleBookingInputChange}
                      className="w-full px-3 py-2 border rounded-md"
                      required
                    />
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Purpose</label>
                  <textarea
                    name="purpose"
                    value={bookingDetails.purpose}
                    onChange={handleBookingInputChange}
                    className="w-full px-3 py-2 border rounded-md"
                    rows="3"
                    placeholder="Brief description of your meeting"
                    required
                  ></textarea>
                </div>
              </div>
            </div>
            
            <div className="p-4 bg-gray-50 border-t flex justify-end space-x-3">
              <button
                onClick={() => setShowBookingModal(false)}
                className="px-4 py-2 border rounded-md text-gray-700 hover:bg-gray-100"
              >
                Cancel
              </button>
              <button
                onClick={handleBooking}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                disabled={!bookingDetails.name || !bookingDetails.email || !bookingDetails.date || 
                  !bookingDetails.startTime || !bookingDetails.endTime}
              >
                Confirm Booking
              </button>
            </div>
          </div>
        </div>
      )}
      
      {/* Room properties modal (edit mode) */}
      {showRoomPropertiesModal && selectedRoom && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-md">
            <div className="p-6 border-b">
              <h2 className="text-xl font-medium">Edit Room Properties</h2>
            </div>
            
            <div className="p-6">
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Room Name</label>
                  <input
                    type="text"
                    name="name"
                    value={roomProperties.name}
                    onChange={handlePropertyChange}
                    className="w-full px-3 py-2 border rounded-md"
                    placeholder="Enter room name"
                    required
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Room Color</label>
                  <input
                    type="color"
                    name="color"
                    value={roomProperties.color}
                    onChange={handlePropertyChange}
                    className="w-full h-10 border rounded-md cursor-pointer"
                  />
                </div>
                
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="room-available"
                    name="available"
                    checked={roomProperties.available}
                    onChange={handlePropertyChange}
                    className="w-4 h-4 text-blue-600 border-gray-300 rounded"
                  />
                  <label htmlFor="room-available" className="ml-2 text-sm text-gray-700">
                    Room is available for booking
                  </label>
                </div>
              </div>
            </div>
            
            <div className="p-4 bg-gray-50 border-t flex justify-between">
              <button
                onClick={() => deleteRoom(selectedRoom.id)}
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
              >
                Delete Room
              </button>
              
              <div className="flex space-x-3">
                <button
                  onClick={() => setShowRoomPropertiesModal(false)}
                  className="px-4 py-2 border rounded-md text-gray-700 hover:bg-gray-100"
                >
                  Cancel
                </button>
                <button
                  onClick={saveRoomProperties}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  Save Changes
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
      
      {/* Save Floor Plan Modal */}
      {showSaveModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-md">
            <div className="p-6 border-b">
              <h2 className="text-xl font-medium">Save Floor Plan</h2>
            </div>
            
            <div className="p-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Current Floor Plan Name</label>
                <div className="flex items-center">
                  <span className="px-3 py-2 bg-gray-100 rounded-md text-gray-700">{floorPlanName}</span>
                  <button
                    onClick={saveFloorPlan}
                    className="ml-3 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm"
                  >
                    Save
                  </button>
                </div>
                
                <div className="mt-6">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Save as New Floor Plan</label>
                  <div className="flex items-center">
                    <input
                      type="text"
                      value={newFloorPlanName}
                      onChange={handleFloorPlanNameChange}
                      className="flex-1 px-3 py-2 border rounded-md"
                      placeholder="Enter new floor plan name"
                    />
                    <button
                      onClick={saveAsNewFloorPlan}
                      disabled={newFloorPlanName.trim() === ''}
                      className="ml-3 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Save As
                    </button>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="p-4 bg-gray-50 border-t flex justify-end">
              <button
                onClick={() => setShowSaveModal(false)}
                className="px-4 py-2 border rounded-md text-gray-700 hover:bg-gray-100"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}  
      
      {/* Load Floor Plan Modal */}
      {showLoadModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-md">
            <div className="p-6 border-b">
              <h2 className="text-xl font-medium">Load Floor Plan</h2>
            </div>
            
            <div className="p-6">
              <p className="text-sm text-gray-600 mb-4">Select a floor plan to load:</p>
              <div className="space-y-1 max-h-48 overflow-y-auto">
                {savedFloorPlans.map((planName) => (
                  <button
                    key={planName}
                    onClick={() => loadFloorPlan(planName)}
                    className="w-full text-left px-4 py-2 hover:bg-blue-50 rounded-md flex items-center justify-between"
                  >
                    <span>{planName}</span>
                    {planName === floorPlanName && (
                      <span className="px-2 py-1 bg-blue-100 text-blue-700 rounded-md text-xs">Current</span>
                    )}
                  </button>
                ))}
              </div>
            </div>
            
            <div className="p-4 bg-gray-50 border-t flex justify-end">
              <button
                onClick={() => setShowLoadModal(false)}
                className="px-4 py-2 border rounded-md text-gray-700 hover:bg-gray-100"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default FloorPlan;