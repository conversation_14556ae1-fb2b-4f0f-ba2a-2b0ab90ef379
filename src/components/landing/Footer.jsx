import { Link } from "react-router-dom";

const Footer = () => {
  const currentYear = new Date().getFullYear();

  // add icon image for socials
  const socialIcons = {
    github: "/images/GitHub.png",
    twitter: "/images/X.png",
    linkedin: "/images/LinkedIn.png",
    instagram: "/images/Instagram.png",
  };

  return (
    <footer className="bg-gray-900 text-white pt-16 pb-8">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8 mb-16">
          <div className="lg:col-span-2">
            <Link
              to="/"
              className="flex items-center gap-2 text-xl font-bold text-white mb-6"
            >
              <i className="fas fa-building text-2xl"></i>
              JechSpace
            </Link>
            <p className="text-gray-400 mb-6">
              A comprehensive workspace management solution designed to optimize
              your office space, boost productivity, and enhance workplace
              experience.
            </p>
            <div className="flex gap-4">
              {Object.entries(socialIcons).map(([name, iconPath]) => (
                <a
                  key={name}
                  href="#"
                  className="w-10 h-10 rounded-full bg-gray-800 flex items-center justify-center text-lg hover:bg-primary transition-all"
                >
                  <img src={iconPath} alt={name} className="w-6 h-6" />
                </a>
              ))}
            </div>
          </div>

          <div>
            <h3 className="font-semibold text-lg mb-4">Product</h3>
            <ul className="space-y-3">
              {[
                "Features",
                "How It Works",
                "Pricing",
                "Use Cases",
                "Integrations",
              ].map((item) => (
                <li key={item}>
                  <a
                    href="#"
                    className="text-gray-400 hover:text-white transition-all"
                  >
                    {item}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          <div>
            <h3 className="font-semibold text-lg mb-4">Company</h3>
            <ul className="space-y-3">
              {["About Us", "Careers", "Blog", "Press", "Contact"].map(
                (item) => (
                  <li key={item}>
                    <a
                      href="#"
                      className="text-gray-400 hover:text-white transition-all"
                    >
                      {item}
                    </a>
                  </li>
                )
              )}
            </ul>
          </div>

          <div>
            <h3 className="font-semibold text-lg mb-4">Resources</h3>
            <ul className="space-y-3">
              {["Documentation", "Support", "API", "Community", "Partners"].map(
                (item) => (
                  <li key={item}>
                    <a
                      href="#"
                      className="text-gray-400 hover:text-white transition-all"
                    >
                      {item}
                    </a>
                  </li>
                )
              )}
            </ul>
          </div>
        </div>

        <div className="pt-8 border-t border-gray-800 flex flex-col md:flex-row justify-between items-center">
          <p className="text-gray-500 mb-4 md:mb-0">
            © {currentYear} JechSpace. All rights reserved.
          </p>
          <div className="flex gap-6">
            <a
              href="#"
              className="text-gray-500 hover:text-white transition-all"
            >
              Privacy Policy
            </a>
            <a
              href="#"
              className="text-gray-500 hover:text-white transition-all"
            >
              Terms of Service
            </a>
            <a
              href="#"
              className="text-gray-500 hover:text-white transition-all"
            >
              Cookie Policy
            </a>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
