import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { authService, userService, notificationService } from '../services';

// Initial state
const initialState = {
  // Authentication
  user: null,
  isAuthenticated: false,
  isLoading: true,
  
  // UI State
  sidebarOpen: false,
  theme: 'light',
  
  // Notifications
  notifications: [],
  unreadCount: 0,
  
  // Global loading states
  globalLoading: false,
  
  // Error handling
  error: null,
  
  // User preferences
  preferences: {
    language: 'en',
    timezone: 'UTC',
    notifications: {
      email: true,
      push: true,
      sms: false,
    },
  },
};

// Action types
const actionTypes = {
  // Authentication
  SET_USER: 'SET_USER',
  SET_AUTHENTICATED: 'SET_AUTHENTICATED',
  SET_LOADING: 'SET_LOADING',
  LOGOUT: 'LOGOUT',
  
  // UI
  TOGGLE_SIDEBAR: 'TOGGLE_SIDEBAR',
  SET_THEME: 'SET_THEME',
  
  // Notifications
  SET_NOTIFICATIONS: 'SET_NOTIFICATIONS',
  ADD_NOTIFICATION: 'ADD_NOTIFICATION',
  REMOVE_NOTIFICATION: 'REMOVE_NOTIFICATION',
  SET_UNREAD_COUNT: 'SET_UNREAD_COUNT',
  MARK_NOTIFICATION_READ: 'MARK_NOTIFICATION_READ',
  
  // Global states
  SET_GLOBAL_LOADING: 'SET_GLOBAL_LOADING',
  SET_ERROR: 'SET_ERROR',
  CLEAR_ERROR: 'CLEAR_ERROR',
  
  // Preferences
  SET_PREFERENCES: 'SET_PREFERENCES',
  UPDATE_PREFERENCE: 'UPDATE_PREFERENCE',
};

// Reducer
const appReducer = (state, action) => {
  switch (action.type) {
    case actionTypes.SET_USER:
      return {
        ...state,
        user: action.payload,
        isAuthenticated: !!action.payload,
      };
      
    case actionTypes.SET_AUTHENTICATED:
      return {
        ...state,
        isAuthenticated: action.payload,
      };
      
    case actionTypes.SET_LOADING:
      return {
        ...state,
        isLoading: action.payload,
      };
      
    case actionTypes.LOGOUT:
      return {
        ...state,
        user: null,
        isAuthenticated: false,
        notifications: [],
        unreadCount: 0,
      };
      
    case actionTypes.TOGGLE_SIDEBAR:
      return {
        ...state,
        sidebarOpen: !state.sidebarOpen,
      };
      
    case actionTypes.SET_THEME:
      return {
        ...state,
        theme: action.payload,
      };
      
    case actionTypes.SET_NOTIFICATIONS:
      return {
        ...state,
        notifications: action.payload,
      };
      
    case actionTypes.ADD_NOTIFICATION:
      return {
        ...state,
        notifications: [action.payload, ...state.notifications],
        unreadCount: state.unreadCount + 1,
      };
      
    case actionTypes.REMOVE_NOTIFICATION:
      return {
        ...state,
        notifications: state.notifications.filter(n => n.id !== action.payload),
      };
      
    case actionTypes.SET_UNREAD_COUNT:
      return {
        ...state,
        unreadCount: action.payload,
      };
      
    case actionTypes.MARK_NOTIFICATION_READ:
      return {
        ...state,
        notifications: state.notifications.map(n =>
          n.id === action.payload ? { ...n, read: true } : n
        ),
        unreadCount: Math.max(0, state.unreadCount - 1),
      };
      
    case actionTypes.SET_GLOBAL_LOADING:
      return {
        ...state,
        globalLoading: action.payload,
      };
      
    case actionTypes.SET_ERROR:
      return {
        ...state,
        error: action.payload,
      };
      
    case actionTypes.CLEAR_ERROR:
      return {
        ...state,
        error: null,
      };
      
    case actionTypes.SET_PREFERENCES:
      return {
        ...state,
        preferences: action.payload,
      };
      
    case actionTypes.UPDATE_PREFERENCE:
      return {
        ...state,
        preferences: {
          ...state.preferences,
          [action.payload.key]: action.payload.value,
        },
      };
      
    default:
      return state;
  }
};

// Create context
const AppContext = createContext();

// Provider component
export const AppProvider = ({ children }) => {
  const [state, dispatch] = useReducer(appReducer, initialState);

  // Initialize app
  useEffect(() => {
    initializeApp();
  }, []);

  const initializeApp = async () => {
    try {
      dispatch({ type: actionTypes.SET_LOADING, payload: true });
      
      // Check if user is authenticated
      if (authService.isAuthenticated()) {
        try {
          const userData = await userService.getCurrentUser();
          dispatch({ type: actionTypes.SET_USER, payload: userData.data });
          
          // Load notifications
          await loadNotifications();
        } catch (error) {
          console.error('Failed to load user data:', error);
          // Clear invalid token
          localStorage.removeItem('authToken');
          dispatch({ type: actionTypes.LOGOUT });
        }
      }
    } catch (error) {
      console.error('App initialization error:', error);
      dispatch({ type: actionTypes.SET_ERROR, payload: error.message });
    } finally {
      dispatch({ type: actionTypes.SET_LOADING, payload: false });
    }
  };

  const loadNotifications = async () => {
    try {
      const [notifications, unreadCount] = await Promise.all([
        notificationService.getNotifications({ limit: 50 }),
        notificationService.getUnreadCount(),
      ]);
      
      dispatch({ type: actionTypes.SET_NOTIFICATIONS, payload: notifications.data });
      dispatch({ type: actionTypes.SET_UNREAD_COUNT, payload: unreadCount.data.count });
    } catch (error) {
      console.error('Failed to load notifications:', error);
    }
  };

  // Action creators
  const actions = {
    // Authentication actions
    setUser: (user) => dispatch({ type: actionTypes.SET_USER, payload: user }),
    setAuthenticated: (isAuth) => dispatch({ type: actionTypes.SET_AUTHENTICATED, payload: isAuth }),
    logout: () => {
      localStorage.removeItem('authToken');
      dispatch({ type: actionTypes.LOGOUT });
    },
    
    // UI actions
    toggleSidebar: () => dispatch({ type: actionTypes.TOGGLE_SIDEBAR }),
    setTheme: (theme) => dispatch({ type: actionTypes.SET_THEME, payload: theme }),
    
    // Notification actions
    addNotification: (notification) => dispatch({ type: actionTypes.ADD_NOTIFICATION, payload: notification }),
    removeNotification: (id) => dispatch({ type: actionTypes.REMOVE_NOTIFICATION, payload: id }),
    markNotificationRead: (id) => dispatch({ type: actionTypes.MARK_NOTIFICATION_READ, payload: id }),
    refreshNotifications: loadNotifications,
    
    // Global state actions
    setGlobalLoading: (loading) => dispatch({ type: actionTypes.SET_GLOBAL_LOADING, payload: loading }),
    setError: (error) => dispatch({ type: actionTypes.SET_ERROR, payload: error }),
    clearError: () => dispatch({ type: actionTypes.CLEAR_ERROR }),
    
    // Preference actions
    setPreferences: (preferences) => dispatch({ type: actionTypes.SET_PREFERENCES, payload: preferences }),
    updatePreference: (key, value) => dispatch({ 
      type: actionTypes.UPDATE_PREFERENCE, 
      payload: { key, value } 
    }),
  };

  return (
    <AppContext.Provider value={{ state, actions }}>
      {children}
    </AppContext.Provider>
  );
};

// Custom hook to use the context
export const useApp = () => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
};

export default AppContext;
