import { motion } from "framer-motion";

const CTA = () => {
  return (
    <section
      id="contact"
      className="py-24 bg-gradient-to-r from-blue-500 to-blue-400 text-white"
    >
      <div className="container mx-auto px-4">
        <div className="flex flex-col lg:flex-row items-center justify-between gap-10">
          <motion.div
            className="w-full lg:w-1/2 text-center lg:text-left"
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl text-white md:text-4xl font-bold mb-4">
              Ready to transform your workspace?
            </h2>
            <p className="text-lg opacity-90 mb-8 max-w-xl">
              Join thousands of companies that have already revolutionized their
              workspace management. Get started with JechSpace today.
            </p>
            <div className="flex flex-wrap gap-4 justify-center lg:justify-start mb-8 lg:mb-0">
              <a
                href="/register"
                className="inline-flex items-center justify-center px-8 py-4 rounded-lg font-semibold bg-white text-primary hover:bg-opacity-90 transition-all"
              >
                Start Your Free Trial
              </a>
              <a
                href="#"
                className="inline-flex items-center justify-center px-8 py-4 rounded-lg font-semibold border-2 border-white hover:bg-blue-700 hover:bg-opacity-0.3 transition-all"
              >
                Schedule a Demo
              </a>
            </div>
          </motion.div>

          <motion.div
            className="w-full lg:w-1/2 bg-white p-8 rounded-xl shadow-2xl"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <h3 className="text-2xl font-bold text-gray-800 mb-6">
              Contact our team
            </h3>
            <form>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div>
                  <label className="block text-gray-700 text-sm font-medium mb-2">
                    Full Name
                  </label>
                  <input
                    type="text"
                    className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary"
                    placeholder="John Doe"
                  />
                </div>
                <div>
                  <label className="block text-gray-700 text-sm font-medium mb-2">
                    Email Address
                  </label>
                  <input
                    type="email"
                    className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary"
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>

              <div className="mb-6">
                <label className="block text-gray-700 text-sm font-medium mb-2">
                  Company Name
                </label>
                <input
                  type="text"
                  className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary"
                  placeholder="Your Company Inc."
                />
              </div>

              <div className="mb-6">
                <label className="block text-gray-700 text-sm font-medium mb-2">
                  Message
                </label>
                <textarea
                  className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary h-32"
                  placeholder="Tell us about your workspace needs..."
                ></textarea>
              </div>

              <button
                type="submit"
                className="w-full py-4 rounded-lg font-semibold bg-gradient-to-r from-blue-500 to-blue-700 hover:translate-y-[-3px] hover:shadow-lg transition-all"
              >
                Send Message
              </button>
            </form>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default CTA;
