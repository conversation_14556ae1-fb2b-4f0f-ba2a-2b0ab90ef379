import api from './api';

/**
 * Service for handling analytics and reporting operations
 */
const analyticsService = {
  /**
   * Get dashboard analytics
   * @param {Object} params - Query parameters (date range, filters)
   * @returns {Promise} - Response from API
   */
  getDashboardAnalytics: async (params = {}) => {
    try {
      const response = await api.get('/analytics/dashboard/', { params });
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  /**
   * Get booking analytics
   * @param {Object} params - Query parameters
   * @returns {Promise} - Response from API
   */
  getBookingAnalytics: async (params = {}) => {
    try {
      const response = await api.get('/analytics/bookings/', { params });
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  /**
   * Get space utilization analytics
   * @param {Object} params - Query parameters
   * @returns {Promise} - Response from API
   */
  getSpaceUtilization: async (params = {}) => {
    try {
      const response = await api.get('/analytics/spaces/utilization/', { params });
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  /**
   * Get user activity analytics
   * @param {Object} params - Query parameters
   * @returns {Promise} - Response from API
   */
  getUserActivity: async (params = {}) => {
    try {
      const response = await api.get('/analytics/users/activity/', { params });
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  /**
   * Get revenue analytics
   * @param {Object} params - Query parameters
   * @returns {Promise} - Response from API
   */
  getRevenueAnalytics: async (params = {}) => {
    try {
      const response = await api.get('/analytics/revenue/', { params });
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  /**
   * Get peak hours analytics
   * @param {Object} params - Query parameters
   * @returns {Promise} - Response from API
   */
  getPeakHours: async (params = {}) => {
    try {
      const response = await api.get('/analytics/peak-hours/', { params });
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  /**
   * Get popular spaces analytics
   * @param {Object} params - Query parameters
   * @returns {Promise} - Response from API
   */
  getPopularSpaces: async (params = {}) => {
    try {
      const response = await api.get('/analytics/spaces/popular/', { params });
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  /**
   * Get organization analytics
   * @param {Object} params - Query parameters
   * @returns {Promise} - Response from API
   */
  getOrganizationAnalytics: async (params = {}) => {
    try {
      const response = await api.get('/analytics/organization/', { params });
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  /**
   * Export analytics report
   * @param {Object} params - Export parameters
   * @returns {Promise} - Response from API
   */
  exportReport: async (params = {}) => {
    try {
      const response = await api.post('/analytics/export/', params, {
        responseType: 'blob'
      });
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  /**
   * Get custom analytics
   * @param {Object} query - Custom query parameters
   * @returns {Promise} - Response from API
   */
  getCustomAnalytics: async (query) => {
    try {
      const response = await api.post('/analytics/custom/', query);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },
};

export default analyticsService;
