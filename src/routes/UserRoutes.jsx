// user routes configuration
import { Routes, Route } from "react-router-dom";

import BookingModal from "../components/dashboard/user/BookingModal";
import FindAndBookSpace from "../components/dashboard/user/FindAndBookSpace";

import UserDashboardLayout from "../components/dashboard/user/UserDashboardLayout";
import UserDashboardPage from "../components/dashboard/user/UserDashboardPage";
import EnhancedDashboardPage from "../pages/EnhancedDashboardPage";
import EnhancedBookingPage from "../pages/EnhancedBookingPage";
import EnhancedUserProfilePage from "../pages/EnhancedUserProfilePage";
import EnhancedAIAssistantPage from "../pages/EnhancedAIAssistantPage";
import AnalyticsDashboardPage from "../pages/AnalyticsDashboardPage";

import AIAssistantPage from "../components/dashboard/user/AIAssistantPage";

import NotFound from "../pages/NotFound";

import "../App.css";

function UserRoutes() {
  return (
    <Routes>
      {/* route for user */}

      <Route path="dashboard" element={<UserDashboardLayout />}>
        <Route index element={<EnhancedDashboardPage />} />
        <Route path="dashboardPage" element={<EnhancedDashboardPage />} />
        <Route path="analytics" element={<AnalyticsDashboardPage />} />

        <Route path="assistant" element={<EnhancedAIAssistantPage />} />
        <Route path="findSpace" element={<FindAndBookSpace />} />
        <Route path="booking" element={<EnhancedBookingPage />} />
        <Route path="myBookings" element={<EnhancedBookingPage />} />
        <Route path="profile" element={<EnhancedUserProfilePage />} />
      </Route>

      {/* 404 page when page is not found */}
      <Route path="*" element={<NotFound />} />
    </Routes>
  );
}
export default UserRoutes;
