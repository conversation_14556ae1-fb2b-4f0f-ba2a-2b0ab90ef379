# JechSpace Frontend - Production Environment Configuration

# API Configuration
VITE_API_BASE_URL=https://api-staging.jechspace.com/api/v1/
VITE_ENVIRONMENT=production

# WebSocket Configuration
VITE_WS_URL=wss://api-staging.jechspace.com/ws/

# Feature Flags
VITE_ENABLE_LOGGING=false
VITE_ENABLE_DEBUG_MODE=false
VITE_ENABLE_MOCK_DATA=false

# Application Configuration
VITE_APP_NAME=JechSpace
VITE_APP_VERSION=2.0.0

# Session Configuration (in milliseconds)
VITE_SESSION_TIMEOUT=1800000
VITE_SESSION_WARNING_TIME=300000

# File Upload Configuration
VITE_MAX_FILE_UPLOAD_SIZE=10485760
VITE_ALLOWED_IMAGE_TYPES=image/jpeg,image/png,image/gif,image/webp

# Pagination
VITE_DEFAULT_PAGE_SIZE=20
VITE_MAX_PAGE_SIZE=100

# Production specific
VITE_ENABLE_SERVICE_WORKER=true
VITE_ENABLE_ANALYTICS=true
