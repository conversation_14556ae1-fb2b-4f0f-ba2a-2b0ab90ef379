/**
 * API Utilities for JechSpace Frontend
 *
 * This file contains utility functions for API operations,
 * URL construction, and environment-specific configurations.
 */

import {
  getApiConfig,
  API_ENDPOINTS,
  buildEndpointUrl,
} from "../config/environment";

// Re-export commonly used functions from environment config
export { getApiConfig } from "../config/environment";

/**
 * Get the full API URL for a given endpoint
 * @param {string} endpoint - The endpoint path
 * @param {Object} params - Parameters to replace in the endpoint
 * @returns {string} - Full API URL
 */
export const getApiUrl = (endpoint, params = {}) => {
  const config = getApiConfig();
  const processedEndpoint = buildEndpointUrl(endpoint, params);
  return `${config.baseURL.replace(/\/$/, "")}${processedEndpoint}`;
};

/**
 * Get endpoint URL from the predefined endpoints
 * @param {string} category - Endpoint category (e.g., 'auth', 'users')
 * @param {string} action - Endpoint action (e.g., 'login', 'profile')
 * @param {Object} params - Parameters to replace in the endpoint
 * @returns {string} - Endpoint URL
 */
export const getEndpointUrl = (category, action, params = {}) => {
  const endpoint = API_ENDPOINTS[category]?.[action];
  if (!endpoint) {
    throw new Error(`Endpoint not found: ${category}.${action}`);
  }
  return buildEndpointUrl(endpoint, params);
};

/**
 * Get full API URL from predefined endpoints
 * @param {string} category - Endpoint category
 * @param {string} action - Endpoint action
 * @param {Object} params - Parameters to replace in the endpoint
 * @returns {string} - Full API URL
 */
export const getApiEndpointUrl = (category, action, params = {}) => {
  const endpoint = getEndpointUrl(category, action, params);
  return getApiUrl(endpoint);
};

/**
 * Check if the current environment is development
 * @returns {boolean} - True if development environment
 */
export const isDevelopment = () => {
  return (
    window.location.hostname === "localhost" ||
    window.location.hostname === "127.0.0.1" ||
    window.location.hostname.startsWith("192.168.")
  );
};

/**
 * Check if the current environment is production
 * @returns {boolean} - True if production environment
 */
export const isProduction = () => {
  return !isDevelopment();
};

/**
 * Get the backend server status URL
 * @returns {string} - Backend health check URL
 */
export const getHealthCheckUrl = () => {
  const config = getApiConfig();
  return `${config.baseURL.replace(/\/api\/v1\/$/, "")}/health/`;
};

/**
 * Format API error messages for user display
 * @param {Object} error - Axios error object
 * @returns {string} - Formatted error message
 */
export const formatApiError = (error) => {
  if (error.response) {
    // Server responded with error status
    const { status, data } = error.response;

    switch (status) {
      case 400:
        return (
          data.message ||
          data.detail ||
          "Invalid request. Please check your input."
        );
      case 401:
        return "Authentication required. Please log in again.";
      case 403:
        return "You do not have permission to perform this action.";
      case 404:
        return "The requested resource was not found.";
      case 409:
        return (
          data.message || "A conflict occurred. The resource may already exist."
        );
      case 422:
        return data.message || "Validation error. Please check your input.";
      case 429:
        return "Too many requests. Please wait a moment and try again.";
      case 500:
        return "Server error. Please try again later.";
      case 502:
        return "Bad gateway. The server is temporarily unavailable.";
      case 503:
        return "Service unavailable. Please try again later.";
      default:
        return (
          data.message ||
          data.detail ||
          `Server error (${status}). Please try again.`
        );
    }
  } else if (error.request) {
    // Network error
    return "Network error. Please check your connection and try again.";
  } else {
    // Other error
    return error.message || "An unexpected error occurred.";
  }
};

/**
 * Create query string from parameters object
 * @param {Object} params - Parameters object
 * @returns {string} - Query string
 */
export const createQueryString = (params) => {
  const searchParams = new URLSearchParams();

  Object.keys(params).forEach((key) => {
    const value = params[key];
    if (value !== null && value !== undefined && value !== "") {
      if (Array.isArray(value)) {
        value.forEach((item) => searchParams.append(key, item));
      } else {
        searchParams.append(key, value);
      }
    }
  });

  const queryString = searchParams.toString();
  return queryString ? `?${queryString}` : "";
};

/**
 * Validate API response structure
 * @param {Object} response - API response
 * @param {Array} requiredFields - Required fields in response data
 * @returns {boolean} - True if valid
 */
export const validateApiResponse = (response, requiredFields = []) => {
  if (!response || !response.data) {
    return false;
  }

  if (requiredFields.length === 0) {
    return true;
  }

  return requiredFields.every((field) => {
    return response.data.hasOwnProperty(field);
  });
};

/**
 * Retry API call with exponential backoff
 * @param {Function} apiCall - Function that returns a Promise
 * @param {number} maxRetries - Maximum number of retries
 * @param {number} baseDelay - Base delay in milliseconds
 * @returns {Promise} - API call result
 */
export const retryApiCall = async (
  apiCall,
  maxRetries = 3,
  baseDelay = 1000
) => {
  let lastError;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await apiCall();
    } catch (error) {
      lastError = error;

      // Don't retry on client errors (4xx) except 429 (rate limit)
      if (
        error.response &&
        error.response.status >= 400 &&
        error.response.status < 500 &&
        error.response.status !== 429
      ) {
        throw error;
      }

      // Don't retry on last attempt
      if (attempt === maxRetries) {
        break;
      }

      // Calculate delay with exponential backoff
      const delay = baseDelay * Math.pow(2, attempt);
      await new Promise((resolve) => setTimeout(resolve, delay));
    }
  }

  throw lastError;
};

/**
 * Check if backend server is reachable
 * @returns {Promise<boolean>} - True if server is reachable
 */
export const checkServerHealth = async () => {
  try {
    const healthUrl = getHealthCheckUrl();
    const response = await fetch(healthUrl, {
      method: "GET",
      timeout: 5000,
    });
    return response.ok;
  } catch (error) {
    console.warn("Backend server health check failed:", error);
    return false;
  }
};

/**
 * Get appropriate error message based on server availability
 * @param {Object} error - Original error
 * @returns {Promise<string>} - Appropriate error message
 */
export const getContextualErrorMessage = async (error) => {
  const isServerHealthy = await checkServerHealth();

  if (!isServerHealthy) {
    if (isDevelopment()) {
      return "Backend server is not running. Please start the Django server at http://localhost:8000/";
    } else {
      return "Service is temporarily unavailable. Please try again later.";
    }
  }

  return formatApiError(error);
};

/**
 * Log API configuration for debugging
 */
export const logApiConfiguration = () => {
  if (isDevelopment()) {
    const config = getApiConfig();
    console.group("🔧 API Configuration");
    console.log("Environment:", isDevelopment() ? "Development" : "Production");
    console.log("Base URL:", config.baseURL);
    console.log("Timeout:", config.timeout);
    console.log("Health Check URL:", getHealthCheckUrl());
    console.groupEnd();
  }
};

// Log configuration on module load in development
if (isDevelopment()) {
  logApiConfiguration();
}

export default {
  getApiConfig,
  getApiUrl,
  getEndpointUrl,
  getApiEndpointUrl,
  isDevelopment,
  isProduction,
  getHealthCheckUrl,
  formatApiError,
  createQueryString,
  validateApiResponse,
  retryApiCall,
  checkServerHealth,
  getContextualErrorMessage,
  logApiConfiguration,
};
