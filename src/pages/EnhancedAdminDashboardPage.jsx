import React, { useState, useEffect } from 'react';
import { useApp } from '../context/AppContext';
import { analyticsService, userService, organizationService } from '../services';
import { useToast } from '../components/common/NotificationContainer';
import { 
  Users, 
  Building, 
  Calendar, 
  TrendingUp, 
  DollarSign,
  MapPin,
  AlertTriangle,
  CheckCircle,
  Clock,
  Activity,
  Settings,
  Plus,
  Filter,
  Download,
  RefreshCw
} from 'lucide-react';
import { InlineLoader, ButtonLoader } from '../components/common/LoadingSpinner';

const EnhancedAdminDashboardPage = () => {
  const { state: appState } = useApp();
  const toast = useToast();
  
  const [dashboardData, setDashboardData] = useState({
    overview: {
      totalUsers: 0,
      totalSpaces: 0,
      totalBookings: 0,
      totalRevenue: 0,
      activeUsers: 0,
      utilizationRate: 0,
    },
    recentActivity: [],
    topSpaces: [],
    userGrowth: [],
    revenueData: [],
    alerts: [],
  });
  
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [dateRange, setDateRange] = useState({
    start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    end: new Date().toISOString().split('T')[0],
  });
  const [selectedMetric, setSelectedMetric] = useState('revenue');

  useEffect(() => {
    loadDashboardData();
  }, [dateRange]);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      
      const [
        analyticsResponse,
        organizationResponse,
        usersResponse,
      ] = await Promise.all([
        analyticsService.getDashboardAnalytics({
          start_date: dateRange.start,
          end_date: dateRange.end,
        }),
        organizationService.getOrganizationAnalytics({
          start_date: dateRange.start,
          end_date: dateRange.end,
        }),
        userService.getAllUsers({ limit: 10, active: true }),
      ]);

      setDashboardData({
        overview: {
          totalUsers: analyticsResponse.data.total_users || 0,
          totalSpaces: analyticsResponse.data.total_spaces || 0,
          totalBookings: analyticsResponse.data.total_bookings || 0,
          totalRevenue: analyticsResponse.data.total_revenue || 0,
          activeUsers: analyticsResponse.data.active_users || 0,
          utilizationRate: analyticsResponse.data.utilization_rate || 0,
        },
        recentActivity: analyticsResponse.data.recent_activity || [],
        topSpaces: analyticsResponse.data.popular_spaces || [],
        userGrowth: analyticsResponse.data.user_growth || [],
        revenueData: analyticsResponse.data.revenue_trends || [],
        alerts: analyticsResponse.data.alerts || [],
      });
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
      toast.error('Dashboard Error', 'Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadDashboardData();
    setRefreshing(false);
    toast.success('Data Refreshed', 'Dashboard data has been updated');
  };

  const handleExportReport = async () => {
    try {
      const blob = await analyticsService.exportReport({
        start_date: dateRange.start,
        end_date: dateRange.end,
        type: 'admin_dashboard',
        format: 'pdf',
      });
      
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `admin-dashboard-${dateRange.start}-to-${dateRange.end}.pdf`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      
      toast.success('Export Complete', 'Dashboard report has been downloaded');
    } catch (error) {
      toast.error('Export Failed', 'Failed to export dashboard report');
    }
  };

  const MetricCard = ({ title, value, change, icon: Icon, color = 'blue', onClick }) => {
    const colorClasses = {
      blue: 'bg-blue-50 text-blue-600 border-blue-200',
      green: 'bg-green-50 text-green-600 border-green-200',
      yellow: 'bg-yellow-50 text-yellow-600 border-yellow-200',
      red: 'bg-red-50 text-red-600 border-red-200',
      purple: 'bg-purple-50 text-purple-600 border-purple-200',
    };

    return (
      <div 
        className={`bg-white rounded-lg shadow-sm border border-gray-200 p-6 ${onClick ? 'cursor-pointer hover:shadow-md transition-shadow' : ''}`}
        onClick={onClick}
      >
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600">{title}</p>
            <p className="text-2xl font-bold text-gray-900 mt-1">
              {loading ? <InlineLoader size="sm" text="" /> : value}
            </p>
            {change !== undefined && (
              <div className="flex items-center mt-1">
                {change >= 0 ? (
                  <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
                ) : (
                  <TrendingUp className="h-4 w-4 text-red-500 mr-1 transform rotate-180" />
                )}
                <span className={`text-sm ${change >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {Math.abs(change)}% from last period
                </span>
              </div>
            )}
          </div>
          <div className={`p-3 rounded-lg border ${colorClasses[color]}`}>
            <Icon className="h-6 w-6" />
          </div>
        </div>
      </div>
    );
  };

  const AlertsPanel = () => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">System Alerts</h3>
        <button className="text-sm text-blue-600 hover:text-blue-800">
          View All
        </button>
      </div>
      
      {loading ? (
        <InlineLoader />
      ) : dashboardData.alerts.length > 0 ? (
        <div className="space-y-3">
          {dashboardData.alerts.slice(0, 5).map((alert, index) => (
            <div key={index} className={`flex items-start p-3 rounded-lg ${
              alert.severity === 'high' ? 'bg-red-50 border border-red-200' :
              alert.severity === 'medium' ? 'bg-yellow-50 border border-yellow-200' :
              'bg-blue-50 border border-blue-200'
            }`}>
              <div className="flex-shrink-0 mr-3">
                {alert.severity === 'high' ? (
                  <AlertTriangle className="h-5 w-5 text-red-500" />
                ) : alert.severity === 'medium' ? (
                  <Clock className="h-5 w-5 text-yellow-500" />
                ) : (
                  <CheckCircle className="h-5 w-5 text-blue-500" />
                )}
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900">{alert.title}</p>
                <p className="text-sm text-gray-600 mt-1">{alert.message}</p>
                <p className="text-xs text-gray-500 mt-1">
                  {new Date(alert.timestamp).toLocaleString()}
                </p>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-8">
          <CheckCircle className="h-12 w-12 text-green-400 mx-auto mb-3" />
          <p className="text-gray-600">All systems running smoothly</p>
        </div>
      )}
    </div>
  );

  const RecentActivityPanel = () => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">Recent Activity</h3>
        <button className="text-sm text-blue-600 hover:text-blue-800">
          View All
        </button>
      </div>
      
      {loading ? (
        <InlineLoader />
      ) : dashboardData.recentActivity.length > 0 ? (
        <div className="space-y-3">
          {dashboardData.recentActivity.slice(0, 8).map((activity, index) => (
            <div key={index} className="flex items-center justify-between py-2">
              <div className="flex items-center">
                <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center mr-3">
                  <Activity className="h-4 w-4 text-gray-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900">{activity.description}</p>
                  <p className="text-xs text-gray-500">{activity.user_name}</p>
                </div>
              </div>
              <span className="text-xs text-gray-500">
                {new Date(activity.timestamp).toLocaleTimeString([], { 
                  hour: '2-digit', 
                  minute: '2-digit' 
                })}
              </span>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-8">
          <Activity className="h-12 w-12 text-gray-400 mx-auto mb-3" />
          <p className="text-gray-600">No recent activity</p>
        </div>
      )}
    </div>
  );

  const TopSpacesPanel = () => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">Top Performing Spaces</h3>
        <button className="text-sm text-blue-600 hover:text-blue-800">
          Manage Spaces
        </button>
      </div>
      
      {loading ? (
        <InlineLoader />
      ) : dashboardData.topSpaces.length > 0 ? (
        <div className="space-y-3">
          {dashboardData.topSpaces.slice(0, 5).map((space, index) => (
            <div key={index} className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium mr-3">
                  {index + 1}
                </div>
                <div>
                  <p className="font-medium text-gray-900">{space.name}</p>
                  <p className="text-sm text-gray-600">{space.type}</p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-sm font-medium text-gray-900">
                  ${space.revenue || 0}
                </p>
                <p className="text-xs text-gray-600">
                  {space.booking_count} bookings
                </p>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-8">
          <MapPin className="h-12 w-12 text-gray-400 mx-auto mb-3" />
          <p className="text-gray-600">No space data available</p>
        </div>
      )}
    </div>
  );

  const QuickActions = () => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
      <div className="grid grid-cols-2 gap-3">
        <button 
          onClick={() => window.location.href = '/admin/dashboard/space'}
          className="flex items-center justify-center p-3 bg-blue-50 text-blue-700 rounded-lg hover:bg-blue-100 transition-colors"
        >
          <Plus className="h-5 w-5 mr-2" />
          Add Space
        </button>
        <button 
          onClick={() => window.location.href = '/admin/dashboard/user-management'}
          className="flex items-center justify-center p-3 bg-green-50 text-green-700 rounded-lg hover:bg-green-100 transition-colors"
        >
          <Users className="h-5 w-5 mr-2" />
          Manage Users
        </button>
        <button 
          onClick={() => window.location.href = '/admin/dashboard/analytics'}
          className="flex items-center justify-center p-3 bg-purple-50 text-purple-700 rounded-lg hover:bg-purple-100 transition-colors"
        >
          <TrendingUp className="h-5 w-5 mr-2" />
          View Analytics
        </button>
        <button 
          onClick={() => window.location.href = '/admin/dashboard/settings'}
          className="flex items-center justify-center p-3 bg-orange-50 text-orange-700 rounded-lg hover:bg-orange-100 transition-colors"
        >
          <Settings className="h-5 w-5 mr-2" />
          Settings
        </button>
      </div>
    </div>
  );

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">
            Admin Dashboard
          </h1>
          <p className="text-gray-600 mt-1">
            Welcome back, {appState.user?.first_name}! Here's your workspace overview.
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <ButtonLoader
            loading={refreshing}
            onClick={handleRefresh}
            className="flex items-center px-3 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </ButtonLoader>
          <button
            onClick={handleExportReport}
            className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            <Download className="h-4 w-4 mr-2" />
            Export Report
          </button>
        </div>
      </div>

      {/* Date Range Selector */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                From
              </label>
              <input
                type="date"
                value={dateRange.start}
                onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}
                className="border border-gray-300 rounded-md px-3 py-2 text-sm"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                To
              </label>
              <input
                type="date"
                value={dateRange.end}
                onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}
                className="border border-gray-300 rounded-md px-3 py-2 text-sm"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Focus Metric
              </label>
              <select
                value={selectedMetric}
                onChange={(e) => setSelectedMetric(e.target.value)}
                className="border border-gray-300 rounded-md px-3 py-2 text-sm"
              >
                <option value="revenue">Revenue</option>
                <option value="bookings">Bookings</option>
                <option value="users">Users</option>
                <option value="utilization">Utilization</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* Overview Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6 mb-8">
        <MetricCard
          title="Total Users"
          value={dashboardData.overview.totalUsers}
          change={12}
          icon={Users}
          color="blue"
          onClick={() => window.location.href = '/admin/dashboard/user-management'}
        />
        <MetricCard
          title="Total Spaces"
          value={dashboardData.overview.totalSpaces}
          change={5}
          icon={Building}
          color="green"
          onClick={() => window.location.href = '/admin/dashboard/space'}
        />
        <MetricCard
          title="Total Bookings"
          value={dashboardData.overview.totalBookings}
          change={18}
          icon={Calendar}
          color="purple"
        />
        <MetricCard
          title="Revenue"
          value={`$${dashboardData.overview.totalRevenue?.toLocaleString() || 0}`}
          change={22}
          icon={DollarSign}
          color="green"
        />
        <MetricCard
          title="Active Users"
          value={dashboardData.overview.activeUsers}
          change={8}
          icon={Activity}
          color="blue"
        />
        <MetricCard
          title="Utilization"
          value={`${dashboardData.overview.utilizationRate}%`}
          change={-3}
          icon={TrendingUp}
          color="yellow"
        />
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column */}
        <div className="lg:col-span-2 space-y-6">
          <RecentActivityPanel />
          <TopSpacesPanel />
        </div>

        {/* Right Column */}
        <div className="space-y-6">
          <QuickActions />
          <AlertsPanel />
        </div>
      </div>
    </div>
  );
};

export default EnhancedAdminDashboardPage;
