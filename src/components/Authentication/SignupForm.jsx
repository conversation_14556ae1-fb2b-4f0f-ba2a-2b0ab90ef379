import React from "react";

const SignupForm = () => {
  return (
    <div className="flex h-screen w-full">
      {/* Left side - Signup Form */}
      <div className="w-full md:w-1/2 p-8 flex flex-col">
        <div className="mb-10">
          {/* Logo Image - You'll add the path */}
          <img
            src="/images/logo-blue.png"
            alt="JechSpace Logo"
            className="h-8"
          />
        </div>

        <div className="max-w-sm mx-auto w-full mt-8">
          <h1 className="text-xl font-semibold mb-8">
            Sign Up to <span className="text-blue-500">Get Started</span>
          </h1>

          <form>
            <div className="mb-4">
              <label className="block text-sm mb-1">Full Name</label>
              <input
                type="text"
                className="w-full px-3 py-2 bg-gray-100 rounded border border-gray-200"
                placeholder="<PERSON>"
              />
            </div>

            <div className="mb-4">
              <label className="block text-sm mb-1">Email</label>
              <input
                type="email"
                className="w-full px-3 py-2 bg-gray-100 rounded border border-gray-200"
                placeholder="Enter Email"
              />
            </div>

            <div className="mb-4">
              <label className="block text-sm mb-1">Password</label>
              <input
                type="password"
                className="w-full px-3 py-2 bg-gray-100 rounded border border-gray-200"
                placeholder="Enter Password"
              />
            </div>

            <div className="mb-4">
              <label className="block text-sm mb-1">Confirm password</label>
              <input
                type="password"
                className="w-full px-3 py-2 bg-gray-100 rounded border border-gray-200"
                placeholder="Confirm Password"
              />
            </div>

            <div className="flex items-center mb-4">
              <input type="checkbox" id="agree-terms" className="mr-2" />
              <label htmlFor="agree-terms" className="text-sm">
                I agree to the{" "}
                <a href="#" className="text-blue-500">
                  terms
                </a>{" "}
                and{" "}
                <a href="#" className="text-blue-500">
                  private policy
                </a>
              </label>
            </div>

            <button
              type="submit"
              className="w-full bg-blue-500 text-white py-2 rounded font-medium mb-3" onclick
            >
              Sign up
            </button>

            <div className="text-center mb-4">Or</div>

            <button
              onClick={() => {}}
              type="button"
              className="w-full border text-black border-gray-300 py-2 rounded flex items-center justify-center font-medium p-2"
            >
              <svg viewBox="0 0 24 24" width="18" height="18" className="mr-2">
                <path
                  fill="#4285F4"
                  d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                />
                <path
                  fill="#34A853"
                  d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                />
                <path
                  fill="#FBBC05"
                  d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                />
                <path
                  fill="#EA4335"
                  d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                />
              </svg>
              Register with Google
            </button>
          </form>
        </div>
      </div>

      {/* Right side - Blue background and illustration */}
      <div className="hidden md:flex md:w-1/2 bg-blue-500 text-white flex-col items-center justify-center p-8 h-full">
        <div className="max-w-md h-full ">
          <h2 className="text-2xl font-semibold mb-4 text-white">
            Create Your Space, Your Way.
          </h2>
          <p className="mb-16 text-white">
            Join Techies to access smart workspace bookings, manage your
            schedule, and stay productive - wherever you are.
          </p>

          {/* Illustration - You'll add the path */}
          <img
            src="/images/DashboardPreview.png"
            alt="Registration Illustration"
            className="w-full shadow-2xl rounded-lg h-auto"
          />
        </div>
      </div>
    </div>
  );
};

export default SignupForm;
