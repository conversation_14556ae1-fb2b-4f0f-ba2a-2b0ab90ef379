import api from './api';

/**
 * Service for handling organization operations
 */
const organizationService = {
  /**
   * Get organization details
   * @param {string} id - Organization ID (optional, defaults to user's organization)
   * @returns {Promise} - Response from API
   */
  getOrganization: async (id = '') => {
    try {
      const endpoint = id ? `/organizations/${id}/` : '/organizations/me/';
      const response = await api.get(endpoint);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  /**
   * Update organization details
   * @param {Object} data - Organization data
   * @returns {Promise} - Response from API
   */
  updateOrganization: async (data) => {
    try {
      const response = await api.put('/organizations/me/', data);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  /**
   * Get organization members
   * @param {Object} params - Query parameters
   * @returns {Promise} - Response from API
   */
  getMembers: async (params = {}) => {
    try {
      const response = await api.get('/organizations/members/', { params });
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  /**
   * Invite a user to the organization
   * @param {Object} data - Invitation data
   * @param {string} data.email - Invitee email
   * @param {string} data.role - Role to assign
   * @returns {Promise} - Response from API
   */
  inviteUser: async (data) => {
    try {
      const response = await api.post('/organizations/invitations/', data);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  /**
   * Remove a member from the organization
   * @param {string} userId - User ID to remove
   * @returns {Promise} - Response from API
   */
  removeMember: async (userId) => {
    try {
      const response = await api.delete(`/organizations/members/${userId}/`);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  /**
   * Update member role
   * @param {string} userId - User ID
   * @param {Object} data - Role data
   * @param {string} data.role - New role
   * @returns {Promise} - Response from API
   */
  updateMemberRole: async (userId, data) => {
    try {
      const response = await api.put(`/organizations/members/${userId}/`, data);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },
};

export default organizationService;
