import { useState } from "react";
import { ArrowRight } from "lucide-react";
import PropTypes from "prop-types";
import PageIndicator from "./PageIndicator";
import { useNavigate } from "react-router-dom";

export default function OnboardingAdmin2({ nextStep }) {
  // State to track which workspace types are selected
  const [selectedWorkspaces, setSelectedWorkspaces] = useState({
    desks: false,
    meetingRooms: false,
    eventSpaces: false,
    breakoutAreas: false,
  });
  const navigate = useNavigate();

  // Function to handle the next step by calling the nextStep prop
  const handleNext = () => {
    navigate("/admin/onboarding/step3");
  };

  // Toggle selection state for a workspace type
  const toggleWorkspace = (type) => {
    setSelectedWorkspaces((prev) => ({
      ...prev,
      [type]: !prev[type],
    }));
  };

  const logoSrc = "/images/logo-blue.png"; // Placeholder for logo
  const onboardingImgSrc = "/images/onboarding2.png"; // Placeholder for onboarding image
  const illustrationSrc = "/api/placeholder/300/320"; // Placeholder for illustration

  return (
    <div className="flex items-center justify-center w-full min-h-screen bg-gray-50 p-4 md:p-16">
      <div className="w-full max-w-6xl bg-white rounded-lg shadow-lg flex flex-col md:flex-row">
        <div className="w-full md:w-3/5 p-6 md:p-10 flex flex-col">
          {/* Logo - Image placeholder */}
          <div className="mb-8">
            <img src={logoSrc} alt="JechSpace Logo" className="h-8" />
          </div>

          {/* title */}
          <div className="mb-8">
            <h1 className="text-2xl md:text-3xl font-bold mb-3">
              {" "}
              What Types of Workspaces Do You Manage?
            </h1>

            <p className="text-gray-600 mb-8 text-sm">
              Select the types of workspaces you want to manage — from desks and
              private offices to meeting rooms and event spaces. JechSpace makes
              it easy to organize and customize each one.
            </p>
          </div>

          {/* Workspace Options */}
          <div className="space-y-3 mt-6">
            {/* Desks */}
            <div
              className="flex items-center justify-between p-4 shadow-sm rounded-lg cursor-pointer"
              onClick={() => toggleWorkspace("desks")}
            >
              <div className="flex items-center">
                <div className="bg-blue-50 p-2 rounded-lg mr-3">
                  <svg
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    className="text-blue-400"
                  >
                    <rect
                      x="4"
                      y="8"
                      width="16"
                      height="10"
                      rx="1"
                      stroke="currentColor"
                      strokeWidth="2"
                    />
                    <path
                      d="M6 8V6C6 5.44772 6.44772 5 7 5H17C17.5523 5 18 5.44772 18 6V8"
                      stroke="currentColor"
                      strokeWidth="2"
                    />
                  </svg>
                </div>
                <div>
                  <h3 className="font-semibold">Desks</h3>
                  <p className="text-xs text-gray-500">
                    Ideal for individuals or hot-desking setups.
                  </p>
                </div>
              </div>
              <div
                className={`w-6 h-6 rounded-full flex items-center justify-center ${
                  selectedWorkspaces.desks ? "bg-blue-500" : "border"
                }`}
              >
                {selectedWorkspaces.desks && (
                  <svg
                    width="14"
                    height="14"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="white"
                    strokeWidth="2"
                  >
                    <polyline points="20 6 9 17 4 12" />
                  </svg>
                )}
              </div>
            </div>
            {/* Meeting Rooms */}
            <div
              className="flex items-center justify-between p-4 shadow-sm rounded-lg cursor-pointer"
              onClick={() => toggleWorkspace("meetingRooms")}
            >
              <div className="flex items-center">
                <div className="bg-blue-50 p-2 rounded-lg mr-3">
                  <svg
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    className="text-blue-400"
                  >
                    <rect
                      x="3"
                      y="5"
                      width="18"
                      height="14"
                      rx="1"
                      stroke="currentColor"
                      strokeWidth="2"
                    />
                    <path
                      d="M7 12H17M7 8H13"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                    />
                  </svg>
                </div>
                <div>
                  <h3 className="font-semibold">Meeting Rooms</h3>
                  <p className="text-xs text-gray-500">
                    Great for team collaboration and scheduled discussions.
                  </p>
                </div>
              </div>
              <div
                className={`w-6 h-6 rounded-full flex items-center justify-center ${
                  selectedWorkspaces.meetingRooms ? "bg-blue-500" : "border"
                }`}
              >
                {selectedWorkspaces.meetingRooms && (
                  <svg
                    width="14"
                    height="14"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="white"
                    strokeWidth="2"
                  >
                    <polyline points="20 6 9 17 4 12" />
                  </svg>
                )}
              </div>
            </div>
            {/* Event Spaces */}
            <div
              className="flex items-center justify-between p-4 shadow-sm rounded-lg cursor-pointer"
              onClick={() => toggleWorkspace("eventSpaces")}
            >
              <div className="flex items-center">
                <div className="bg-blue-50 p-2 rounded-lg mr-3">
                  <svg
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    className="text-blue-400"
                  >
                    <rect
                      x="3"
                      y="5"
                      width="18"
                      height="14"
                      rx="1"
                      stroke="currentColor"
                      strokeWidth="2"
                    />
                    <path
                      d="M8 12H16M12 8V16"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                    />
                  </svg>
                </div>
                <div>
                  <h3 className="font-semibold">Event Spaces</h3>
                  <p className="text-xs text-gray-500">
                    Host training sessions, workshops, or corporate events.
                  </p>
                </div>
              </div>
              <div
                className={`w-6 h-6 rounded-full flex items-center justify-center ${
                  selectedWorkspaces.eventSpaces ? "bg-blue-500" : "border"
                }`}
              >
                {selectedWorkspaces.eventSpaces && (
                  <svg
                    width="14"
                    height="14"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="white"
                    strokeWidth="2"
                  >
                    <polyline points="20 6 9 17 4 12" />
                  </svg>
                )}
              </div>
            </div>
            {/* Breakout Areas */}
            <div
              className="flex items-center justify-between p-4 shadow-sm rounded-lg cursor-pointer"
              onClick={() => toggleWorkspace("breakoutAreas")}
            >
              <div className="flex items-center">
                <div className="bg-blue-50 p-2 rounded-lg mr-3">
                  <svg
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    className="text-blue-400"
                  >
                    <path
                      d="M17 8h2a2 2 0 012 2v8a2 2 0 01-2 2h-2v-2m0-10v2m0 8v2m0-12v8"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                    />
                    <path
                      d="M9 8H7a2 2 0 00-2 2v8a2 2 0 002 2h2v-2m0-10v2m0 8v2m0-12v8"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                    />
                  </svg>
                </div>
                <div>
                  <h3 className="font-semibold">Breakout Areas</h3>
                  <p className="text-xs text-gray-500">
                    Relax, recharge, or have casual conversations.
                  </p>
                </div>
              </div>
              <div
                className={`w-6 h-6 rounded-full flex items-center justify-center ${
                  selectedWorkspaces.breakoutAreas ? "bg-blue-500" : "border"
                }`}
              >
                {selectedWorkspaces.breakoutAreas && (
                  <svg
                    width="14"
                    height="14"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="white"
                    strokeWidth="2"
                  >
                    <polyline points="20 6 9 17 4 12" />
                  </svg>
                )}
              </div>
            </div>
          </div>

          <div className="mt-16 flex items-center justify-between">
            <PageIndicator currentStep={2} totalSteps={5} />
            <button
              type="button"
              onClick={handleNext}
              className="bg-blue-500 text-white px-5 py-2 rounded-md flex items-center"
            >
              Next <ArrowRight size={16} className="ml-1" />
            </button>
          </div>
        </div>
        <div className="hidden md:flex w-full md:w-2/5 bg-gray-50 items-center justify-center p-4">
          {/* Replace the src with your actual welcome image path */}
          <img
            src={onboardingImgSrc}
            alt="Welcome illustration"
            className="max-w-full max-h-full object-contain"
          />
        </div>
      </div>
    </div>
  );
}

OnboardingAdmin2.propTypes = {
  nextStep: PropTypes.func.isRequired,
};
