import api from './api';

/**
 * Service for handling workspace operations
 */
const spaceService = {
  /**
   * Get all spaces
   * @param {Object} params - Query parameters
   * @returns {Promise} - Response from API
   */
  getAllSpaces: async (params = {}) => {
    try {
      const response = await api.get('/spaces/', { params });
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  /**
   * Get space by ID
   * @param {string} id - Space ID
   * @returns {Promise} - Response from API
   */
  getSpaceById: async (id) => {
    try {
      const response = await api.get(`/spaces/${id}/`);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  /**
   * Create a new space
   * @param {Object} spaceData - Space data
   * @returns {Promise} - Response from API
   */
  createSpace: async (spaceData) => {
    try {
      const response = await api.post('/spaces/', spaceData);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  /**
   * Update a space
   * @param {string} id - Space ID
   * @param {Object} spaceData - Updated space data
   * @returns {Promise} - Response from API
   */
  updateSpace: async (id, spaceData) => {
    try {
      const response = await api.put(`/spaces/${id}/`, spaceData);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  /**
   * Delete a space
   * @param {string} id - Space ID
   * @returns {Promise} - Response from API
   */
  deleteSpace: async (id) => {
    try {
      const response = await api.delete(`/spaces/${id}/`);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },

  /**
   * Get available spaces for booking
   * @param {Object} params - Query parameters (date, time, etc.)
   * @returns {Promise} - Response from API
   */
  getAvailableSpaces: async (params = {}) => {
    try {
      const response = await api.get('/spaces/available/', { params });
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : error;
    }
  },
};

export default spaceService;
