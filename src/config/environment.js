/**
 * Environment Configuration for JechSpace Frontend
 *
 * This file manages API endpoints and environment-specific settings
 * for both local development and production environments.
 */

// Environment detection
export const getEnvironment = () => {
  const hostname = window.location.hostname;

  if (
    hostname === "localhost" ||
    hostname === "127.0.0.1" ||
    hostname.startsWith("192.168.")
  ) {
    return "development";
  } else {
    return "production";
  }
};

// API Configuration
export const API_CONFIG = {
  development: {
    baseURL:
      import.meta.env.VITE_API_BASE_URL || "http://localhost:8000/api/v1/",
    timeout: parseInt(import.meta.env.VITE_API_TIMEOUT) || 30000,
    retries: parseInt(import.meta.env.VITE_API_RETRIES) || 3,
  },
  production: {
    baseURL:
      import.meta.env.VITE_API_BASE_URL ||
      "https://api-staging.jechspace.com/api/v1/",
    timeout: parseInt(import.meta.env.VITE_API_TIMEOUT) || 30000,
    retries: parseInt(import.meta.env.VITE_API_RETRIES) || 3,
  },
};

// Get current API configuration
export const getApiConfig = () => {
  const env = getEnvironment();

  // Environment variable takes precedence
  if (import.meta.env.VITE_API_BASE_URL) {
    return {
      ...API_CONFIG[env],
      baseURL: import.meta.env.VITE_API_BASE_URL,
    };
  }

  return API_CONFIG[env];
};

// Feature flags for different environments
export const FEATURES = {
  development: {
    enableLogging: import.meta.env.VITE_ENABLE_LOGGING === "true" || true,
    enableDebugMode: import.meta.env.VITE_ENABLE_DEBUG_MODE === "true" || true,
    enableMockData: import.meta.env.VITE_ENABLE_MOCK_DATA === "true" || false,
    enableServiceWorker:
      import.meta.env.VITE_ENABLE_SERVICE_WORKER === "true" || false,
  },
  production: {
    enableLogging: import.meta.env.VITE_ENABLE_LOGGING === "true" || false,
    enableDebugMode: import.meta.env.VITE_ENABLE_DEBUG_MODE === "true" || false,
    enableMockData: import.meta.env.VITE_ENABLE_MOCK_DATA === "true" || false,
    enableServiceWorker:
      import.meta.env.VITE_ENABLE_SERVICE_WORKER === "true" || true,
  },
};

// Get current feature flags
export const getFeatures = () => {
  const env = getEnvironment();
  return FEATURES[env];
};

// WebSocket Configuration
export const WEBSOCKET_CONFIG = {
  development: {
    url: import.meta.env.VITE_WS_URL || "ws://localhost:8000/ws/",
    reconnectInterval:
      parseInt(import.meta.env.VITE_WS_RECONNECT_INTERVAL) || 5000,
    maxReconnectAttempts:
      parseInt(import.meta.env.VITE_WS_MAX_RECONNECT_ATTEMPTS) || 5,
  },
  production: {
    url: import.meta.env.VITE_WS_URL || "wss://api-staging.jechspace.com/ws/",
    reconnectInterval:
      parseInt(import.meta.env.VITE_WS_RECONNECT_INTERVAL) || 5000,
    maxReconnectAttempts:
      parseInt(import.meta.env.VITE_WS_MAX_RECONNECT_ATTEMPTS) || 10,
  },
};

// Get WebSocket configuration
export const getWebSocketConfig = () => {
  const env = getEnvironment();
  return WEBSOCKET_CONFIG[env];
};

// Application Configuration
export const APP_CONFIG = {
  name: import.meta.env.VITE_APP_NAME || "JechSpace",
  version: import.meta.env.VITE_APP_VERSION || "2.0.0",
  description: "Modern Workspace Management Platform",
  supportEmail: import.meta.env.VITE_SUPPORT_EMAIL || "<EMAIL>",
  maxFileUploadSize:
    parseInt(import.meta.env.VITE_MAX_FILE_UPLOAD_SIZE) || 10 * 1024 * 1024, // 10MB
  allowedImageTypes: import.meta.env.VITE_ALLOWED_IMAGE_TYPES?.split(",") || [
    "image/jpeg",
    "image/png",
    "image/gif",
    "image/webp",
  ],
  pagination: {
    defaultPageSize: parseInt(import.meta.env.VITE_DEFAULT_PAGE_SIZE) || 20,
    maxPageSize: parseInt(import.meta.env.VITE_MAX_PAGE_SIZE) || 100,
  },
  session: {
    timeout: parseInt(import.meta.env.VITE_SESSION_TIMEOUT) || 30 * 60 * 1000, // 30 minutes
    warningTime:
      parseInt(import.meta.env.VITE_SESSION_WARNING_TIME) || 5 * 60 * 1000, // 5 minutes before timeout
  },
};

// Environment-specific overrides
export const getAppConfig = () => {
  const env = getEnvironment();
  const baseConfig = { ...APP_CONFIG };

  if (env === "development") {
    baseConfig.session.timeout = 60 * 60 * 1000; // 1 hour in development
  }

  return baseConfig;
};

// API Endpoints mapping
export const API_ENDPOINTS = {
  // Authentication
  auth: {
    login: "/auth/login/",
    logout: "/auth/logout/",
    register: "/auth/register/",
    registerIndividual: "/auth/register/individual/",
    registerOrganization: "/auth/register/organization/",
    refresh: "/auth/refresh/",
    forgotPassword: "/auth/forgot-password/",
    resetPassword: "/auth/reset-password/",
    verifyEmail: "/auth/verify-email/",
  },

  // User Management
  users: {
    profile: "/users/profile/",
    current: "/users/me/",
    list: "/users/",
    update: "/users/profile/",
    changePassword: "/users/change-password/",
    uploadAvatar: "/users/avatar/",
  },

  // Organization Management
  organizations: {
    list: "/organizations/",
    create: "/organizations/",
    detail: "/organizations/{id}/",
    update: "/organizations/{id}/",
    delete: "/organizations/{id}/",
    members: "/organizations/{id}/members/",
    analytics: "/organizations/analytics/",
  },

  // Space Management
  spaces: {
    list: "/spaces/",
    create: "/spaces/",
    detail: "/spaces/{id}/",
    update: "/spaces/{id}/",
    delete: "/spaces/{id}/",
    available: "/spaces/available/",
    search: "/spaces/search/",
  },

  // Booking Management
  bookings: {
    list: "/bookings/",
    create: "/bookings/",
    detail: "/bookings/{id}/",
    update: "/bookings/{id}/",
    cancel: "/bookings/{id}/cancel/",
    extend: "/bookings/{id}/extend/",
    userBookings: "/bookings/user/",
  },

  // Amenities
  amenities: {
    list: "/amenities/",
    create: "/amenities/",
    detail: "/amenities/{id}/",
    update: "/amenities/{id}/",
    delete: "/amenities/{id}/",
    popular: "/amenities/popular/",
  },

  // Permissions & Roles
  permissions: {
    list: "/permissions/",
    userPermissions: "/permissions/me/",
    check: "/permissions/check/me/",
    roles: "/roles/",
    assign: "/permissions/assign/",
    remove: "/permissions/remove/",
  },

  // Analytics
  analytics: {
    dashboard: "/analytics/dashboard/",
    bookings: "/analytics/bookings/",
    spaces: "/analytics/spaces/utilization/",
    users: "/analytics/users/activity/",
    revenue: "/analytics/revenue/",
    peakHours: "/analytics/peak-hours/",
    export: "/analytics/export/",
    custom: "/analytics/custom/",
  },

  // Notifications
  notifications: {
    list: "/notifications/",
    unreadCount: "/notifications/unread/count/",
    markRead: "/notifications/{id}/read/",
    markAllRead: "/notifications/read-all/",
    delete: "/notifications/{id}/",
    deleteAll: "/notifications/delete-all/",
    preferences: "/notifications/preferences/",
    send: "/notifications/send/",
    sendBulk: "/notifications/send-bulk/",
    pushSubscribe: "/notifications/push/subscribe/",
    pushUnsubscribe: "/notifications/push/unsubscribe/",
  },
};

// Utility function to build endpoint URLs with parameters
export const buildEndpointUrl = (endpoint, params = {}) => {
  let url = endpoint;

  // Replace path parameters
  Object.keys(params).forEach((key) => {
    url = url.replace(`{${key}}`, params[key]);
  });

  return url;
};

// Environment info for debugging
export const getEnvironmentInfo = () => {
  const env = getEnvironment();
  const apiConfig = getApiConfig();
  const features = getFeatures();

  return {
    environment: env,
    hostname: window.location.hostname,
    apiBaseURL: apiConfig.baseURL,
    features,
    timestamp: new Date().toISOString(),
  };
};

// Log environment configuration on load
if (getFeatures().enableLogging) {
  console.log("🌍 JechSpace Environment Configuration:", getEnvironmentInfo());
}

export default {
  getEnvironment,
  getApiConfig,
  getFeatures,
  getWebSocketConfig,
  getAppConfig,
  API_ENDPOINTS,
  buildEndpointUrl,
  getEnvironmentInfo,
};
