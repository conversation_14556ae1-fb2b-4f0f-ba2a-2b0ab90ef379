import { useState } from "react";
import { ArrowRight } from "lucide-react";
import propTypes from "prop-types";
import PageIndicator from "./PageIndicator";
import { useNavigate } from "react-router-dom";

export default function OnboardingAdmin1({ nextStep }) {
  const [workspaceName, setWorkspaceName] = useState("");
  const [address, setAddress] = useState("");
  const [city, setCity] = useState("");
  const [zipCode, setZipCode] = useState("");
  const [country, setCountry] = useState("");
  const [capacity, setCapacity] = useState("");
  const navigate = useNavigate();

  const handleSubmit = (e) => {
    e.preventDefault();
    navigate("/admin/onboarding/step2");
  };

  const blurStyle = {
    color: "rgba(107, 114, 128, 0.7)",
    filter: "blur(0.5px)",
    fontWeight: "400",
  };

  const logoSrc = "/images/logo-blue.png";
  const onboardingImgSrc = "/images/onboarding1.png"; // Placeholder for onboarding image

  return (
    <div className="flex items-center justify-center w-full min-h-screen bg-gray-50 p-4 sm:p-6 md:p-16">
      <div className="w-full max-w-6xl bg-white rounded-lg shadow-lg flex flex-col md:flex-row">
        {/*left section */}
        <div className="w-full md:w-3/5 p-10 flex flex-col">
          {/* Logo - Image placeholder */}
          <div className="mb-8">
            <img src={logoSrc} alt="JechSpace Logo" className="h-8" />
          </div>

          {/* Form title */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900">Set up your</h1>
            <h1 className="text-3xl font-bold text-gray-900 mb-4">
              first workspace
            </h1>
            <p className="text-gray-600 text-base">
              Let's start by setting up your primary workspace location. You can
              add more workspaces later.
            </p>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit} className="space-y-5">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Workspace Name
              </label>
              <input
                type="text"
                value={workspaceName}
                onChange={(e) => setWorkspaceName(e.target.value)}
                placeholder="Headquarters, Downtown Office, etc."
                className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-100"
                style={!workspaceName ? blurStyle : {}}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Street Address
              </label>
              <input
                type="text"
                value={address}
                onChange={(e) => setAddress(e.target.value)}
                placeholder="123 Business Avenue"
                className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-100"
                style={!address ? blurStyle : {}}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  City
                </label>
                <input
                  type="text"
                  value={city}
                  onChange={(e) => setCity(e.target.value)}
                  placeholder="New York"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-100"
                  style={!city ? blurStyle : {}}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Postal/ZIP Code
                </label>
                <input
                  type="text"
                  value={zipCode}
                  onChange={(e) => setZipCode(e.target.value)}
                  placeholder="10001"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-100"
                  style={!zipCode ? blurStyle : {}}
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Country
              </label>
              <select
                value={country}
                onChange={(e) => setCountry(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-100 appearance-none"
                style={!country ? blurStyle : {}}
              >
                <option value="" style={blurStyle}>
                  Select a country
                </option>
                <option value="ng">Nigeria</option>
                <option value="us">United States</option>
                <option value="ca">Canada</option>
                <option value="uk">United Kingdom</option>
                <option value="au">Australia</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Total Capacity (seats)
              </label>
              <select
                value={capacity}
                onChange={(e) => setCapacity(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-100 appearance-none"
                style={!capacity ? blurStyle : {}}
              >
                <option value="" style={blurStyle}>
                  Select capacity
                </option>
                <option value="1-10">1-10 seats</option>
                <option value="11-25">11-25 seats</option>
                <option value="26-50">26-50 seats</option>
                <option value="51-100">51-100 seats</option>
                <option value="101+">101+ seats</option>
              </select>
            </div>

            <div className="pt-4 flex items-center justify-between">
              <PageIndicator currentStep={1} totalSteps={5} />
              <button
                type="submit"
                className="bg-blue-500 text-white px-5 py-2 rounded-md flex items-center"
              >
                Next <ArrowRight size={16} className="ml-1" />
              </button>
            </div>
          </form>
        </div>
        <div className="hidden md:flex w-full md:w-2/5 bg-gray-50 items-center justify-center p-4">
          {/* Replace the src with your actual welcome image path */}
          <img
            src={onboardingImgSrc}
            alt="Welcome illustration"
            className="max-w-full max-h-full object-contain"
          />
        </div>
      </div>
    </div>
  );
}

OnboardingAdmin1.propTypes = {
  nextStep: propTypes.func.isRequired,
};
