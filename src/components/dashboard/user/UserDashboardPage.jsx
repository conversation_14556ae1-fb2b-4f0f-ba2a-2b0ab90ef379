import BookingModal from "./BookingModal";
import React, { useState } from "react";
import { Plus<PERSON>ir<PERSON>, CheckCircle } from "lucide-react";
import AIAssistantWidget from "./AIAssistantWidget";
import CheckinModal from "./CheckinModal";
import {
  Calendar,
  Clock,
  Building,
  Star,
  TrendingUp,
  AlertTriangle,
  BookOpen,
  CreditCard,
} from "lucide-react";

// add a quick action card with two buttons (book a space and checkin)
// when the user clicks on the book a space button, it should open the booking modal
function QuickActions() {
  const [isBookingModalOpen, setIsBookingModalOpen] = useState(false);
  const [isCheckinModalOpen, setIsCheckinModalOpen] = useState(false);

  return (
    <>
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h2 className="text-lg font-medium mb-4">Quick Actions</h2>
        <div className="flex space-x-4">
          <button
            onClick={() => setIsBookingModalOpen(true)}
            className="flex items-center justify-center py-2 px-4 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex-1"
          >
            <PlusCircle className="mr-2" size={20} />
            Book a Space
          </button>
          <button
            onClick={() => setIsCheckinModalOpen(true)}
            className="flex items-center justify-center py-2 px-4 bg-green-600 text-white rounded-md hover:bg-green-700 flex-1"
          >
            <CheckCircle className="mr-2" size={20} />
            Check In
          </button>
        </div>
      </div>
      <BookingModal
        isOpen={isBookingModalOpen}
        onClose={() => setIsBookingModalOpen(false)}
      />
      <CheckinModal
        isOpen={isCheckinModalOpen}
        onClose={() => setIsCheckinModalOpen(false)}
      />
    </>
  );

  const handleBookSpace = () => {
    setIsBookingModalOpen(true);
  };

  const handleCheckin = () => {
    setIsCheckinModalOpen(true);
  };

  return (
    <>
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h2 className="text-lg font-medium mb-4">Quick Actions</h2>
        <div className="flex space-x-4">
          <button
            onClick={handleBookSpace}
            className="flex items-center justify-center py-2 px-4 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex-1"
          >
            <PlusCircle className="mr-2" size={20} />
            Book a Space
          </button>
          <button
            onClick={handleCheckin}
            className="flex items-center justify-center py-2 px-4 bg-green-600 text-white rounded-md hover:bg-green-700 flex-1"
          >
            <CheckCircle className="mr-2" size={20} />
            Check In
          </button>
        </div>
      </div>
      <CheckinModal
        isOpen={isCheckinModalOpen}
        onClose={() => setIsCheckinModalOpen(false)}
      />
    </>
  );
}

function UserDashboardPage() {
  // Sample data - replace with real data from your API
  const stats = [
    {
      title: "My Bookings",
      value: "12",
      icon: <Calendar />,
      color: "bg-blue-500",
    },
    {
      title: "Hours Reserved",
      value: "45",
      icon: <Clock />,
      color: "bg-green-500",
    },
    {
      title: "Favorite Spaces",
      value: "8",
      icon: <Star />,
      color: "bg-purple-500",
    },
    {
      title: "Upcoming Events",
      value: "3",
      icon: <BookOpen />,
      color: "bg-orange-500",
    },
  ];

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-800">My Dashboard</h1>
        <div>
          <select className="bg-white border border-gray-300 rounded-md shadow-sm px-4 py-2">
            <option>Last 7 days</option>
            <option>Last 30 days</option>
            <option>Last 90 days</option>
          </select>
        </div>
      </div>

      {/* Quick Actions */}
      <QuickActions />

      {/* ai assistant widget */}
      <AIAssistantWidget />
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <div key={index} className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">
                  {stat.title}
                </p>
                <p className="text-2xl font-bold">{stat.value}</p>
              </div>
              <div className={`p-3 rounded-full ${stat.color} text-white`}>
                {stat.icon}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-medium">My Booking History</h2>
            <TrendingUp className="text-green-500" />
          </div>
          <div className="h-64 w-full bg-gray-100 rounded flex items-center justify-center">
            {/* Replace with actual chart component */}
            <div className="w-full h-full relative">
              <p className="text-gray-500">Booking History Chart</p>
              {/* <iframe
                title="ALX Booking Analysis"
                className="absolute top-0 left-0 w-full h-full border-0"
                src="https://app.powerbi.com/view?r=eyJrIjoiOWY2YTQ3MGItYTEwNi00ODU3LThjMmYtZmRmNTAxZjQ2ZDhjIiwidCI6ImQwNmVjYjYzLTkyY2YtNDNkYy05YmNjLTE0ZWE2MTBmZmZjMiJ9"
                allowFullScreen={true}
              /> */}
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-medium">Space Preferences</h2>
            <Building className="text-blue-500" />
          </div>
          <div className="h-64 w-full bg-gray-100 rounded flex items-center justify-center">
            {/* Replace with actual chart component */}
            <div className="w-full h-full relative">
              <p className="text-gray-500">Space Usage By Type Chart</p>
              {/* <iframe
                title="ALX Booking Analysis"
                className="absolute top-0 left-0 w-full h-full border-0"
                src="https://app.powerbi.com/view?r=eyJrIjoiOWY2YTQ3MGItYTEwNi00ODU3LThjMmYtZmRmNTAxZjQ2ZDhjIiwidCI6ImQwNmVjYjYzLTkyY2YtNDNkYy05YmNjLTE0ZWE2MTBmZmZjMiJ9"
                allowFullScreen={true}
              /> */}
            </div>
          </div>
        </div>
      </div>

      {/* Recent Activities */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h2 className="text-lg font-medium mb-4">Recent Activities</h2>

        <div className="space-y-4">
          <div className="flex items-center p-3 hover:bg-gray-50 rounded-md">
            <div className="bg-green-100 p-2 rounded-md mr-3">
              <Calendar className="text-green-500" size={20} />
            </div>
            <div className="flex-1">
              <p className="font-medium">Meeting Room Booked</p>
              <p className="text-sm text-gray-500">
                Room 305, 2 hours on Jan 15
              </p>
            </div>
            <p className="text-sm text-gray-500">2 hours ago</p>
          </div>

          <div className="flex items-center p-3 hover:bg-gray-50 rounded-md">
            <div className="bg-blue-100 p-2 rounded-md mr-3">
              <Star className="text-blue-500" size={20} />
            </div>
            <div className="flex-1">
              <p className="font-medium">Added to favorites</p>
              <p className="text-sm text-gray-500">
                Creative Studio added to favorites
              </p>
            </div>
            <p className="text-sm text-gray-500">Yesterday</p>
          </div>

          <div className="flex items-center p-3 hover:bg-gray-50 rounded-md">
            <div className="bg-purple-100 p-2 rounded-md mr-3">
              <CreditCard className="text-purple-500" size={20} />
            </div>
            <div className="flex-1">
              <p className="font-medium">Payment Completed</p>
              <p className="text-sm text-gray-500">
                $45.00 for Conference Room Booking
              </p>
            </div>
            <p className="text-sm text-gray-500">3 days ago</p>
          </div>

          <div className="flex items-center p-3 hover:bg-gray-50 rounded-md">
            <div className="bg-orange-100 p-2 rounded-md mr-3">
              <Clock className="text-orange-500" size={20} />
            </div>
            <div className="flex-1">
              <p className="font-medium">Booking Extended</p>
              <p className="text-sm text-gray-500">
                Extended Meeting Room 101 by 1 hour
              </p>
            </div>
            <p className="text-sm text-gray-500">5 days ago</p>
          </div>
        </div>
      </div>

      {/* Notifications */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-medium">Notifications</h2>
          <AlertTriangle className="text-yellow-500" />
        </div>

        <div className="space-y-3">
          <div className="bg-blue-50 border-l-4 border-blue-500 p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <Calendar className="text-blue-500" />
              </div>
              <div className="ml-3">
                <p className="text-sm text-blue-700">
                  Your booking for Conference Room B is in 2 hours.
                </p>
              </div>
            </div>
          </div>

          <div className="bg-yellow-50 border-l-4 border-yellow-500 p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <AlertTriangle className="text-yellow-500" />
              </div>
              <div className="ml-3">
                <p className="text-sm text-yellow-700">
                  Your subscription will expire in 5 days. Consider renewal.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
export default UserDashboardPage;
