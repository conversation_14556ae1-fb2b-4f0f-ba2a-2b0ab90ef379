import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Eye, EyeOff } from "lucide-react";
import { authService } from "../../services";

const SignupToggle = () => {
  const [userType, setUserType] = useState("individual");
  const navigate = useNavigate();

  // Individual form state
  const [individualFormData, setIndividualFormData] = useState({
    email: "",
    password: "",
    confirmPassword: "",
    fullName: "",
    workType: "freelancer",
    spacePreference: "desk",
    agreedToTerms: false,
  });

  // Organization form state
  const [organizationFormData, setOrganizationFormData] = useState({
    email: "",
    first_name: "",
    last_name: "",
    phone_number: "",
    password: "",
    confirm_password: "",
    profession: "",
    agreedToTerms: false,
  });

  const [errors, setErrors] = useState({});
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Handle input changes for individual form
  const handleIndividualChange = (e) => {
    const { name, value, type, checked } = e.target;
    setIndividualFormData({
      ...individualFormData,
      [name]: type === "checkbox" ? checked : value,
    });
  };

  // Handle input changes for organization form
  const handleOrganizationChange = (e) => {
    const { name, value, type, checked } = e.target;
    setOrganizationFormData({
      ...organizationFormData,
      [name]: type === "checkbox" ? checked : value,
    });
  };

  // Validate individual form
  const validateIndividualForm = () => {
    const newErrors = {};
    if (!individualFormData.firstName.trim()) {
      newErrors.firstName = "First name is required";
    }
    if (!individualFormData.lastName.trim()) {
      newErrors.lastName = "Last name is required";
    }
    if (!individualFormData.email) {
      newErrors.email = "Email is required";
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(individualFormData.email)) {
      newErrors.email = "Email is invalid";
    }
    if (!individualFormData.password) {
      newErrors.password = "Password is required";
    } else if (individualFormData.password.length < 8) {
      newErrors.password = "Password must be at least 8 characters";
    }
    if (individualFormData.password !== individualFormData.confirmPassword) {
      newErrors.confirmPassword = "Passwords don't match";
    }
    if (!individualFormData.agreedToTerms) {
      newErrors.agreedToTerms =
        "You must agree to the terms and privacy policy";
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Validate organization form
  const validateOrganizationForm = () => {
    const newErrors = {};

    if (!organizationFormData.first_name) {
      newErrors.first_name = "First name is required";
    }

    if (!organizationFormData.last_name) {
      newErrors.last_name = "Last name is required";
    }

    if (!organizationFormData.phone_number) {
      newErrors.phone_number = "Phone number is required";
    } else if (
      !/^\+?[0-9\s-()]{8,20}$/.test(organizationFormData.phone_number)
    ) {
      newErrors.phone_number = "Please enter a valid phone number";
    }

    if (!organizationFormData.profession) {
      newErrors.profession = "Profession is required";
    }

    if (!organizationFormData.email) {
      newErrors.email = "Email is required";
    } else if (
      !/^[\w.-]+@([\w-]+\.)+[a-zA-Z]{2,}$/.test(organizationFormData.email)
    ) {
      newErrors.email = "Please use a valid email address";
    }

    if (!organizationFormData.password) {
      newErrors.password = "Password is required";
    } else if (
      !/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[^A-Za-z\d]).{8,}$/.test(
        organizationFormData.password
      )
    ) {
      newErrors.password =
        "Password must be at least 8 characters and include uppercase, lowercase, number, and symbol";
    }

    if (
      organizationFormData.password !== organizationFormData.confirm_password
    ) {
      newErrors.confirm_password = "Passwords do not match";
    }

    if (!organizationFormData.agreedToTerms) {
      newErrors.agreedToTerms =
        "You must agree to the terms and privacy policy";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle individual form submission
  const handleIndividualSubmit = async (e) => {
    e.preventDefault();
    if (validateIndividualForm()) {
      try {
        setIsSubmitting(true);
        const apiData = {
          email: individualFormData.email,
          password: individualFormData.password,
          confirm_password: individualFormData.confirmPassword,
          first_name: individualFormData.firstName,
          last_name: individualFormData.lastName,
          work_type: individualFormData.workType,
          space_preference: individualFormData.spacePreference,
          // Required fields for the backend
          profession: individualFormData.workType || "Not specified", // Use workType as profession if available
          phone_number: "", // Optional field
          user_type: "individual",
        };

        console.log("Individual signup data:", apiData);
        const data = await authService.registerIndividual(apiData);
        console.log("Registration successful:", data);
        navigate("/confirmation/individual");
      } catch (error) {
        console.error("Registration error:", error);

        // Log detailed error information for debugging
        if (error.errors) {
          console.log(
            "Validation errors:",
            JSON.stringify(error.errors, null, 2)
          );

          // Extract and display specific error messages
          let errorMessages = "";

          // Handle different error formats
          Object.entries(error.errors).forEach(([field, messages]) => {
            if (typeof messages === "object" && messages !== null) {
              // If messages is an object with message property
              const messageText = messages.message || JSON.stringify(messages);
              errorMessages += `${field}: ${messageText}\n`;

              // Provide specific guidance based on the field
              if (field === "password") {
                errorMessages +=
                  "Password must be at least 8 characters and include letters, numbers, and special characters.\n";
              } else if (field === "email") {
                // Check if this is a uniqueness error
                if (messages.code === "unique") {
                  errorMessages +=
                    "This email is already registered. Please use a different email address or try logging in.\n";
                } else {
                  errorMessages += "Please provide a valid email address.\n";
                }
              } else if (field === "profession") {
                errorMessages +=
                  "Profession is required. Please provide your occupation.\n";
              }
            } else if (Array.isArray(messages)) {
              // If messages is an array
              errorMessages += `${field}: ${messages.join(", ")}\n`;
            } else {
              // If messages is a string or other type
              errorMessages += `${field}: ${messages}\n`;
            }
          });

          alert(`Registration failed:\n${errorMessages}`);
        } else {
          alert(error.message || "An error occurred during registration");
        }
      } finally {
        setIsSubmitting(false);
      }
    }
  };

  // Handle organization form submission
  const handleOrganizationSubmit = async (e) => {
    e.preventDefault();
    if (validateOrganizationForm()) {
      try {
        setIsSubmitting(true);

        const apiData = {
          email: organizationFormData.email,
          first_name: organizationFormData.first_name,
          last_name: organizationFormData.last_name,
          phone_number: organizationFormData.phone_number,
          password: organizationFormData.password,
          confirm_password: organizationFormData.confirm_password,
          profession: organizationFormData.profession,
          // Make sure to use the exact value expected by the backend
          user_type: "organization_owner",
        };

        console.log("Organization signup data:", apiData);
        const data = await authService.registerOrganization(apiData);
        console.log("Registration successful:", data);
        navigate("/confirmation/organization");
      } catch (error) {
        console.error("Registration error:", error);

        // Log detailed error information for debugging
        if (error.errors) {
          console.log(
            "Validation errors:",
            JSON.stringify(error.errors, null, 2)
          );

          // Extract and display specific error messages
          let errorMessages = "";

          // Handle different error formats
          Object.entries(error.errors).forEach(([field, messages]) => {
            if (typeof messages === "object" && messages !== null) {
              // If messages is an object with message property
              const messageText = messages.message || JSON.stringify(messages);
              errorMessages += `${field}: ${messageText}\n`;

              // Provide specific guidance based on the field
              if (field === "password") {
                errorMessages +=
                  "Password must be at least 8 characters and include letters, numbers, and special characters.\n";
              } else if (field === "email") {
                // Check if this is a uniqueness error
                if (messages.code === "unique") {
                  errorMessages +=
                    "This email is already registered. Please use a different email address or try logging in.\n";
                } else {
                  errorMessages += "Please provide a valid email address.\n";
                }
              } else if (field === "profession") {
                errorMessages +=
                  "Profession is required. Please provide your occupation.\n";
              }
            } else if (Array.isArray(messages)) {
              // If messages is an array
              errorMessages += `${field}: ${messages.join(", ")}\n`;
            } else {
              // If messages is a string or other type
              errorMessages += `${field}: ${messages}\n`;
            }
          });

          alert(`Registration failed:\n${errorMessages}`);
        } else {
          alert(error.message || "An error occurred during registration");
        }
      } finally {
        setIsSubmitting(false);
      }
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const toggleConfirmPasswordVisibility = () => {
    setShowConfirmPassword(!showConfirmPassword);
  };

  // Clear errors when switching between forms
  const handleUserTypeChange = (type) => {
    setUserType(type);
    setErrors({});
  };

  return (
    <div className="flex h-screen w-full">
      {/* Left side - Form */}
      <div className="w-full md:w-1/2 p-8 flex flex-col overflow-y-auto">
        <div className="mb-6 flex justify-center md:justify-start">
          <img
            src="/images/logo-blue.png"
            alt="JechSpace Logo"
            className="h-8"
          />
        </div>

        <div className="max-w-md mx-auto w-full pb-8">
          <h1 className="text-xl font-semibold mb-2">
            Join <span className="text-blue-500">JechSpace</span>
          </h1>
          <p className="text-gray-600 mb-6">
            Select how you'll be using JechSpace to get a personalized
            experience.
          </p>

          {/* Toggle Selection */}
          <div className="mb-8">
            <div className="flex p-1 bg-gray-100 rounded-full">
              <button
                type="button"
                className={`flex-1 py-3 px-4 rounded-full transition-all duration-300 ${
                  userType === "individual"
                    ? "bg-blue-500 text-white shadow-md"
                    : "text-gray-700 hover:bg-gray-200"
                }`}
                onClick={() => handleUserTypeChange("individual")}
              >
                Individual
              </button>
              <button
                type="button"
                className={`flex-1 py-3 px-4 rounded-full transition-all duration-300 ${
                  userType === "organization"
                    ? "bg-blue-500 text-white shadow-md"
                    : "text-gray-700 hover:bg-gray-200"
                }`}
                onClick={() => handleUserTypeChange("organization")}
              >
                Organization
              </button>
            </div>
          </div>

          {/* Description based on selection */}
          <div className="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-100">
            {userType === "individual" ? (
              <div>
                <h3 className="font-medium text-blue-800 mb-2">
                  Perfect for freelancers and individuals
                </h3>
                <p className="text-blue-700 text-sm">
                  Find and book workspace on-demand, keep track of your
                  bookings, and enjoy a productive environment tailored to your
                  needs.
                </p>
              </div>
            ) : (
              <div>
                <h3 className="font-medium text-blue-800 mb-2">
                  Ideal for organizations of all sizes
                </h3>
                <p className="text-blue-700 text-sm">
                  Manage your company's workspace, organize team bookings, and
                  optimize your office space utilization with comprehensive
                  analytics.
                </p>
              </div>
            )}
          </div>

          {/* Individual Form */}
          {userType === "individual" && (
            <form onSubmit={handleIndividualSubmit}>
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  First Name
                </label>
                <input
                  type="text"
                  name="firstName"
                  value={individualFormData.firstName}
                  onChange={handleIndividualChange}
                  className={`w-full px-3 py-2 bg-gray-100 rounded border ${
                    errors.firstName ? "border-red-500" : "border-gray-200"
                  }`}
                  placeholder="John"
                />
                {errors.firstName && (
                  <p className="text-red-500 text-xs mt-1">
                    {errors.firstName}
                  </p>
                )}
              </div>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Last Name
                </label>
                <input
                  type="text"
                  name="lastName"
                  value={individualFormData.lastName}
                  onChange={handleIndividualChange}
                  className={`w-full px-3 py-2 bg-gray-100 rounded border ${
                    errors.lastName ? "border-red-500" : "border-gray-200"
                  }`}
                  placeholder="Doe"
                />
                {errors.lastName && (
                  <p className="text-red-500 text-xs mt-1">{errors.lastName}</p>
                )}
              </div>
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Email
                </label>
                <input
                  type="email"
                  name="email"
                  value={individualFormData.email}
                  onChange={handleIndividualChange}
                  className={`w-full px-3 py-2 bg-gray-100 rounded border ${
                    errors.email ? "border-red-500" : "border-gray-200"
                  }`}
                  placeholder="<EMAIL>"
                />
                {errors.email && (
                  <p className="text-red-500 text-xs mt-1">{errors.email}</p>
                )}
              </div>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Password
                </label>
                <input
                  type="password"
                  name="password"
                  value={individualFormData.password}
                  onChange={handleIndividualChange}
                  className={`w-full px-3 py-2 bg-gray-100 rounded border ${
                    errors.password ? "border-red-500" : "border-gray-200"
                  }`}
                  placeholder="Create a password"
                />
                {errors.password && (
                  <p className="text-red-500 text-xs mt-1">{errors.password}</p>
                )}
              </div>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Confirm Password
                </label>
                <input
                  type="password"
                  name="confirmPassword"
                  value={individualFormData.confirmPassword}
                  onChange={handleIndividualChange}
                  className={`w-full px-3 py-2 bg-gray-100 rounded border ${
                    errors.confirmPassword
                      ? "border-red-500"
                      : "border-gray-200"
                  }`}
                  placeholder="Confirm your password"
                />
                {errors.confirmPassword && (
                  <p className="text-red-500 text-xs mt-1">
                    {errors.confirmPassword}
                  </p>
                )}
              </div>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Occupation
                </label>
                <select
                  name="workType"
                  value={individualFormData.workType}
                  onChange={handleIndividualChange}
                  className="w-full px-3 py-2 bg-gray-100 rounded border border-gray-200"
                >
                  <option value="freelancer">Freelancer</option>
                  <option value="remote-worker">Remote Worker</option>
                  <option value="entrepreneur">Entrepreneur</option>
                  <option value="student">Student</option>
                  <option value="other">Other</option>
                </select>
              </div>

              <div className="mb-5">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Preferred Workspace Type
                </label>
                <select
                  name="spacePreference"
                  value={individualFormData.spacePreference}
                  onChange={handleIndividualChange}
                  className="w-full px-3 py-2 bg-gray-100 rounded border border-gray-200"
                >
                  <option value="desk">Hot Desk</option>
                  <option value="dedicated-desk">Dedicated Desk</option>
                  <option value="private-office">Private Office</option>
                  <option value="meeting-room">Meeting Room</option>
                  <option value="any">No Preference</option>
                </select>
              </div>

              <div className="mb-6">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="individual-terms"
                    name="agreedToTerms"
                    checked={individualFormData.agreedToTerms}
                    onChange={handleIndividualChange}
                    className="mr-2"
                  />
                  <label
                    htmlFor="individual-terms"
                    className="text-sm text-gray-700"
                  >
                    I agree to the{" "}
                    <a href="#" className="text-blue-500">
                      Terms of Service
                    </a>{" "}
                    and{" "}
                    <a href="#" className="text-blue-500">
                      Privacy Policy
                    </a>
                  </label>
                </div>
                {errors.agreedToTerms && (
                  <p className="text-red-500 text-xs mt-1">
                    {errors.agreedToTerms}
                  </p>
                )}
              </div>

              <button
                type="submit"
                disabled={isSubmitting}
                className={`w-full bg-blue-500 hover:bg-blue-600 text-white py-2 rounded-lg font-medium mb-4 ${
                  isSubmitting ? "opacity-70 cursor-not-allowed" : ""
                }`}
              >
                {isSubmitting ? "Creating Account..." : "Create Account"}
              </button>
            </form>
          )}

          {/* Organization Form */}
          {userType === "organization" && (
            <form onSubmit={handleOrganizationSubmit}>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4">
                <div>
                  <label
                    htmlFor="first_name"
                    className="block text-sm font-medium text-gray-700 mb-1"
                  >
                    First Name
                  </label>
                  <input
                    type="text"
                    id="first_name"
                    name="first_name"
                    value={organizationFormData.first_name}
                    onChange={handleOrganizationChange}
                    placeholder="Joseph"
                    className={`w-full px-3 py-2 bg-gray-100 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      errors.first_name ? "border-red-500" : "border-gray-200"
                    }`}
                  />
                  {errors.first_name && (
                    <p className="text-red-500 text-xs mt-1">
                      {errors.first_name}
                    </p>
                  )}
                </div>

                <div>
                  <label
                    htmlFor="last_name"
                    className="block text-sm font-medium text-gray-700 mb-1"
                  >
                    Last Name
                  </label>
                  <input
                    type="text"
                    id="last_name"
                    name="last_name"
                    value={organizationFormData.last_name}
                    onChange={handleOrganizationChange}
                    placeholder="Doe"
                    className={`w-full px-3 py-2 bg-gray-100 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      errors.last_name ? "border-red-500" : "border-gray-200"
                    }`}
                  />
                  {errors.last_name && (
                    <p className="text-red-500 text-xs mt-1">
                      {errors.last_name}
                    </p>
                  )}
                </div>
              </div>

              <div className="mb-4">
                <label
                  htmlFor="email"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Work Email
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={organizationFormData.email}
                  onChange={handleOrganizationChange}
                  placeholder="<EMAIL>"
                  className={`w-full px-3 py-2 bg-gray-100 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.email ? "border-red-500" : "border-gray-200"
                  }`}
                />
                {errors.email && (
                  <p className="text-red-500 text-xs mt-1">{errors.email}</p>
                )}
              </div>

              <div className="mb-4">
                <label
                  htmlFor="phone_number"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Phone Number
                </label>
                <input
                  type="tel"
                  id="phone_number"
                  name="phone_number"
                  value={organizationFormData.phone_number}
                  onChange={handleOrganizationChange}
                  placeholder="+****************"
                  className={`w-full px-3 py-2 bg-gray-100 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.phone_number ? "border-red-500" : "border-gray-200"
                  }`}
                />
                {errors.phone_number && (
                  <p className="text-red-500 text-xs mt-1">
                    {errors.phone_number}
                  </p>
                )}
              </div>

              <div className="mb-4">
                <label
                  htmlFor="profession"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Profession
                </label>
                <input
                  type="text"
                  id="profession"
                  name="profession"
                  value={organizationFormData.profession}
                  onChange={handleOrganizationChange}
                  placeholder="Software Developer"
                  className={`w-full px-3 py-2 bg-gray-100 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    errors.profession ? "border-red-500" : "border-gray-200"
                  }`}
                />
                {errors.profession && (
                  <p className="text-red-500 text-xs mt-1">
                    {errors.profession}
                  </p>
                )}
              </div>

              <div className="mb-4">
                <label
                  htmlFor="password"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Create Password
                </label>
                <div className="relative">
                  <input
                    type={showPassword ? "text" : "password"}
                    id="password"
                    name="password"
                    value={organizationFormData.password}
                    onChange={handleOrganizationChange}
                    placeholder="••••••••••••"
                    className={`w-full px-3 py-2 bg-gray-100 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      errors.password ? "border-red-500" : "border-gray-200"
                    }`}
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-500 hover:text-gray-700"
                    onClick={togglePasswordVisibility}
                    aria-label={
                      showPassword ? "Hide password" : "Show password"
                    }
                  >
                    {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                  </button>
                </div>
                {errors.password && (
                  <p className="text-red-500 text-xs mt-1">{errors.password}</p>
                )}
              </div>

              <div className="mb-4">
                <label
                  htmlFor="confirm_password"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Confirm Password
                </label>
                <div className="relative">
                  <input
                    type={showConfirmPassword ? "text" : "password"}
                    id="confirm_password"
                    name="confirm_password"
                    value={organizationFormData.confirm_password}
                    onChange={handleOrganizationChange}
                    placeholder="••••••••••••"
                    className={`w-full px-3 py-2 bg-gray-100 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      errors.confirm_password
                        ? "border-red-500"
                        : "border-gray-200"
                    }`}
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-500 hover:text-gray-700"
                    onClick={toggleConfirmPasswordVisibility}
                    aria-label={
                      showConfirmPassword ? "Hide password" : "Show password"
                    }
                  >
                    {showConfirmPassword ? (
                      <EyeOff size={18} />
                    ) : (
                      <Eye size={18} />
                    )}
                  </button>
                </div>
                {errors.confirm_password && (
                  <p className="text-red-500 text-xs mt-1">
                    {errors.confirm_password}
                  </p>
                )}
              </div>

              <div className="mb-6">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="organization-terms"
                    name="agreedToTerms"
                    checked={organizationFormData.agreedToTerms}
                    onChange={handleOrganizationChange}
                    className="mr-2"
                  />
                  <label
                    htmlFor="organization-terms"
                    className="text-sm text-gray-700"
                  >
                    I agree to the{" "}
                    <a href="#" className="text-blue-500">
                      Terms of Service
                    </a>{" "}
                    and{" "}
                    <a href="#" className="text-blue-500">
                      Privacy Policy
                    </a>
                  </label>
                </div>
                {errors.agreedToTerms && (
                  <p className="text-red-500 text-xs mt-1">
                    {errors.agreedToTerms}
                  </p>
                )}
              </div>

              <button
                type="submit"
                disabled={isSubmitting}
                className={`w-full bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                  isSubmitting ? "opacity-70 cursor-not-allowed" : ""
                }`}
              >
                {isSubmitting ? "Creating Account..." : "Create Account"}
              </button>
            </form>
          )}

          <div className="text-center text-sm mt-6">
            <span className="text-gray-600">Already have an account? </span>
            <a href="/login" className="text-blue-500">
              Sign in
            </a>
          </div>
        </div>
      </div>

      {/* Right side - Blue background and illustration */}
      <div className="hidden md:flex md:w-1/2 bg-blue-500 text-white flex-col items-center justify-center p-8 sticky top-0 h-screen">
        <div className="max-w-md">
          <h2 className="text-2xl text-white font-semibold mb-4">
            Find Your Perfect Workspace
          </h2>
          <p className="mb-16 text-white">
            {userType === "individual"
              ? "Access on-demand workspace, meeting rooms, and more. Book when and where you need it."
              : "Manage your company's workspace, organize team bookings, and optimize your office space utilization with comprehensive analytics."}
          </p>

          <div className="flex justify-center">
            <img
              src={
                userType === "individual"
                  ? "/images/signuptoggle.jpg"
                  : "/images/employer-registration.png"
              }
              alt="JechSpace Illustration"
              className="w-3/4 h-auto rounded-lg shadow-2xl object-contain"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default SignupToggle;
