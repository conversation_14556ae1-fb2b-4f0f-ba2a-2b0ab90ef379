# JechSpace Frontend - Environment Configuration Template
# Copy this file to .env.local and customize for your local development

# API Configuration
# For local development: http://localhost:8000/api/v1/
# For production: https://api-staging.jechspace.com/api/v1/
VITE_API_BASE_URL=http://localhost:8000/api/v1/
VITE_ENVIRONMENT=development

# WebSocket Configuration
VITE_WS_URL=ws://localhost:8000/ws/

# Feature Flags
VITE_ENABLE_LOGGING=true
VITE_ENABLE_DEBUG_MODE=true
VITE_ENABLE_MOCK_DATA=false

# Application Configuration
VITE_APP_NAME=JechSpace
VITE_APP_VERSION=2.0.0

# Session Configuration (in milliseconds)
# Development: 1 hour, Production: 30 minutes
VITE_SESSION_TIMEOUT=3600000
VITE_SESSION_WARNING_TIME=300000

# File Upload Configuration
VITE_MAX_FILE_UPLOAD_SIZE=10485760
VITE_ALLOWED_IMAGE_TYPES=image/jpeg,image/png,image/gif,image/webp

# Pagination
VITE_DEFAULT_PAGE_SIZE=20
VITE_MAX_PAGE_SIZE=100

# Optional: Override backend port if different
# VITE_BACKEND_PORT=8000

# Legacy API endpoints (for backward compatibility)
# These will be deprecated in favor of the unified VITE_API_BASE_URL
VITE_AUTH_SIGNUP_URL=http://localhost:8000/api/v1/auth/signup/
VITE_AUTH_SIGNIN_URL=http://localhost:8000/api/v1/auth/signin/
VITE_AUTH_SIGNOUT_URL=http://localhost:8000/api/v1/auth/signout/
INDREG_API=http://localhost:8000/api/v1/auth/signup/
USER_REG_API=http://localhost:8000/api/v1/auth/signup/
ORGREG_API=http://localhost:8000/api/v1/auth/signup/
