// Implement the AdminOnboarding page to guide new users through the onboarding process and connect other pages to it. when next button is clicked it should go to the next step(page) with smooth animation. The page should be responsive and should work on all devices.

// Import necessary libraries and components
import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import OnboardingAdmin1 from "./OnboardingAdmin1";
import OnboardingAdmin2 from "./OnboardingAdmin2";
import OnboardingAdmin3 from "./OnboardingAdmin3";
import OnboardingAdmin4 from "./OnboardingAdmin4";
// Removed duplicate import of OnboardingAdmin5
import { useNavigate } from "react-router-dom";
import { Component } from "lucide-react";
import OnboardingAdmin5 from "./OnboardingAdmin5";

// Define the interface for onboarding props
export interface OnboardingProps {
  nextStep: () => void;
}

const AdminOnboarding = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const navigate = useNavigate();

  // Function to go to the next step
  const nextStep = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      navigate("/admin-dashboard"); // Redirect to admin dashboard after the last step
    }
  };

  // Array of onboarding steps and last step to redirect to admin dashboard
  const steps = [
    { component: OnboardingAdmin1, props: { nextStep } },
    { component: OnboardingAdmin2, props: { nextStep } },
    { component: OnboardingAdmin3, props: { nextStep } },
    { component: OnboardingAdmin4, props: { nextStep } },
    { component: OnboardingAdmin5, props: { nextStep } }, // Last step to redirect to admin dashboard
  ];

  // Animation variants
  const slideVariants = {
    enter: { x: "100%", opacity: 0 },
    center: { x: 0, opacity: 1 },
    exit: { x: "-100%", opacity: 0 },
  };

  return (
    <div className="onboarding-container">
      <AnimatePresence mode="wait">
        <motion.div
          key={currentStep}
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          transition={{ duration: 0.4, ease: "easeInOut" }}
        >
          {React.createElement(
            steps[currentStep].component,
            steps[currentStep].props
          )}
        </motion.div>
      </AnimatePresence>
    </div>
  );
};

export default AdminOnboarding;
