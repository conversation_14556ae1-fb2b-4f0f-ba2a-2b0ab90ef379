import { useState } from "react";
import {
  Shield,
  Save,
  Plus,
  Trash2,
  X,
  CheckCircle,
  AlertTriangle,
} from "lucide-react";

export default function RolesPermissions() {
  // Sample initial data for roles and permissions
  const [roles, setRoles] = useState([
    {
      id: 1,
      name: "Admin",
      description: "Full system access and management",
      permissions: [
        { id: 1, name: "User Management", granted: true },
        { id: 2, name: "Space Management", granted: true },
        { id: 3, name: "Reports & Analytics", granted: true },
        { id: 4, name: "System Configuration", granted: true },
        { id: 5, name: "Maintenance Tasks", granted: true },
      ],
    },
    {
      id: 2,
      name: "Editor",
      description: "Can edit content but cannot manage users",
      permissions: [
        { id: 1, name: "User Management", granted: false },
        { id: 2, name: "Space Management", granted: true },
        { id: 3, name: "Reports & Analytics", granted: true },
        { id: 4, name: "System Configuration", granted: false },
        { id: 5, name: "Maintenance Tasks", granted: false },
      ],
    },
    {
      id: 3,
      name: "Viewer",
      description: "Read-only access to data",
      permissions: [
        { id: 1, name: "User Management", granted: false },
        { id: 2, name: "Space Management", granted: false },
        { id: 3, name: "Reports & Analytics", granted: true },
        { id: 4, name: "System Configuration", granted: false },
        { id: 5, name: "Maintenance Tasks", granted: false },
      ],
    },
  ]);

  const [selectedRole, setSelectedRole] = useState(roles[0]);
  const [isAddRoleModalOpen, setIsAddRoleModalOpen] = useState(false);
  const [isDeleteRoleModalOpen, setIsDeleteRoleModalOpen] = useState(false);
  const [newRoleName, setNewRoleName] = useState("");
  const [newRoleDescription, setNewRoleDescription] = useState("");
  const [notification, setNotification] = useState(null);

  // Toggle permission for a role
  const togglePermission = (permissionId) => {
    const updatedPermissions = selectedRole.permissions.map((permission) => {
      if (permission.id === permissionId) {
        return { ...permission, granted: !permission.granted };
      }
      return permission;
    });

    const updatedRole = { ...selectedRole, permissions: updatedPermissions };
    setSelectedRole(updatedRole);

    // Update the role in the roles array
    const updatedRoles = roles.map((role) => {
      if (role.id === selectedRole.id) {
        return updatedRole;
      }
      return role;
    });

    setRoles(updatedRoles);
  };

  // Save role changes
  const saveRoleChanges = () => {
    const updatedRoles = roles.map((role) => {
      if (role.id === selectedRole.id) {
        return selectedRole;
      }
      return role;
    });

    setRoles(updatedRoles);
    showNotification("Role saved successfully", "success");
  };

  // Add new role
  const handleAddRole = () => {
    if (!newRoleName) {
      showNotification("Role name is required", "error");
      return;
    }

    const newRole = {
      id: roles.length + 1,
      name: newRoleName,
      description: newRoleDescription || "No description provided",
      permissions: [
        { id: 1, name: "User Management", granted: false },
        { id: 2, name: "Space Management", granted: false },
        { id: 3, name: "Reports & Analytics", granted: false },
        { id: 4, name: "System Configuration", granted: false },
        { id: 5, name: "Maintenance Tasks", granted: false },
      ],
    };

    setRoles([...roles, newRole]);
    setSelectedRole(newRole);
    setIsAddRoleModalOpen(false);
    setNewRoleName("");
    setNewRoleDescription("");
    showNotification("New role added successfully", "success");
  };

  // Delete role
  const handleDeleteRole = () => {
    if (roles.length <= 1) {
      showNotification("You must have at least one role", "error");
      setIsDeleteRoleModalOpen(false);
      return;
    }

    const updatedRoles = roles.filter((role) => role.id !== selectedRole.id);
    setRoles(updatedRoles);
    setSelectedRole(updatedRoles[0]);
    setIsDeleteRoleModalOpen(false);
    showNotification("Role deleted successfully", "success");
  };

  // Show notification
  const showNotification = (message, type) => {
    setNotification({ message, type });
    setTimeout(() => setNotification(null), 3000);
  };

  return (
    <div className="p-4">
      {/* Header Section */}
      <div className="mb-6">
        <h1 className="text-2xl text-black font-bold">Roles & Permissions</h1>
        <p className="text-gray-600">
          Configure user roles and assign permissions
        </p>
      </div>

      {/* Notification */}
      {notification && (
        <div
          className={`flex items-center p-4 mb-4 rounded-lg ${
            notification.type === "success"
              ? "bg-green-50 text-green-800"
              : "bg-red-50 text-red-800"
          }`}
        >
          {notification.type === "success" ? (
            <CheckCircle className="w-5 h-5 mr-2" />
          ) : (
            <AlertTriangle className="w-5 h-5 mr-2" />
          )}
          <span>{notification.message}</span>
          <button
            className="ml-auto text-gray-500 hover:text-gray-900"
            onClick={() => setNotification(null)}
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {/* Role List Sidebar */}
        <div className="md:col-span-1 bg-white rounded-lg shadow p-4">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-semibold text-gray-800">Roles</h2>
            <button
              className="text-blue-600 hover:text-blue-800"
              onClick={() => setIsAddRoleModalOpen(true)}
            >
              <Plus className="w-5 h-5" />
            </button>
          </div>
          <ul className="space-y-2">
            {roles.map((role) => (
              <li key={role.id}>
                <button
                  className={`w-full text-left px-4 py-2 rounded-lg flex items-center ${
                    selectedRole.id === role.id
                      ? "bg-blue-100 text-blue-700"
                      : "hover:bg-gray-100"
                  }`}
                  onClick={() => setSelectedRole(role)}
                >
                  <Shield className="w-4 h-4 mr-2" />
                  {role.name}
                </button>
              </li>
            ))}
          </ul>
        </div>

        {/* Role Details and Permissions */}
        <div className="md:col-span-3 bg-white rounded-lg shadow p-6">
          <div className="flex justify-between items-start mb-6">
            <div>
              <h2 className="text-xl font-semibold text-gray-800">
                {selectedRole.name}
              </h2>
              <p className="text-gray-600">{selectedRole.description}</p>
            </div>
            <div className="flex space-x-2">
              <button
                className="flex items-center text-white bg-blue-600 hover:bg-blue-700 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-4 py-2"
                onClick={saveRoleChanges}
              >
                <Save className="w-4 h-4 mr-2" />
                Save
              </button>
              <button
                className="flex items-center text-white bg-red-600 hover:bg-red-700 focus:ring-4 focus:ring-red-300 font-medium rounded-lg text-sm px-4 py-2"
                onClick={() => setIsDeleteRoleModalOpen(true)}
                disabled={roles.length <= 1}
              >
                <Trash2 className="w-4 h-4 mr-2" />
                Delete
              </button>
            </div>
          </div>

          {/* Permissions List */}
          <div className="mt-6">
            <h3 className="text-lg font-medium text-gray-800 mb-4">
              Permissions
            </h3>
            <div className="bg-gray-50 rounded-lg p-4">
              <table className="w-full">
                <thead>
                  <tr className="text-left text-gray-600 border-b">
                    <th className="pb-2">Permission</th>
                    <th className="pb-2 text-center">Access</th>
                  </tr>
                </thead>
                <tbody>
                  {selectedRole.permissions.map((permission) => (
                    <tr key={permission.id} className="border-b last:border-0">
                      <td className="py-3">{permission.name}</td>
                      <td className="py-3 text-center">
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            className="sr-only peer"
                            checked={permission.granted}
                            onChange={() => togglePermission(permission.id)}
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Permission Details Section */}
          <div className="mt-6">
            <h3 className="text-lg font-medium text-gray-800 mb-4">
              Permission Details
            </h3>
            <div className="bg-gray-50 rounded-lg p-4 space-y-4">
              <div>
                <h4 className="font-medium text-gray-800">User Management</h4>
                <p className="text-gray-600 text-sm">
                  Access to view, add, edit, and delete user accounts.
                </p>
              </div>
              <div>
                <h4 className="font-medium text-gray-800">Space Management</h4>
                <p className="text-gray-600 text-sm">
                  Ability to create, modify, and manage spaces and floor plans.
                </p>
              </div>
              <div>
                <h4 className="font-medium text-gray-800">
                  Reports & Analytics
                </h4>
                <p className="text-gray-600 text-sm">
                  Access to view and generate system reports and analytics.
                </p>
              </div>
              <div>
                <h4 className="font-medium text-gray-800">
                  System Configuration
                </h4>
                <p className="text-gray-600 text-sm">
                  Ability to modify system-wide settings and configurations.
                </p>
              </div>
              <div>
                <h4 className="font-medium text-gray-800">Maintenance Tasks</h4>
                <p className="text-gray-600 text-sm">
                  Access to create and manage maintenance schedules and tasks.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Add Role Modal */}
      {isAddRoleModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="relative p-4 w-full max-w-md max-h-full">
            <div className="relative bg-white rounded-lg shadow">
              <div className="flex items-center justify-between p-4 border-b rounded-t">
                <h3 className="text-xl font-semibold text-gray-900">
                  Add New Role
                </h3>
                <button
                  type="button"
                  className="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm p-1.5 ml-auto inline-flex items-center"
                  onClick={() => setIsAddRoleModalOpen(false)}
                >
                  <X className="w-5 h-5" />
                </button>
              </div>
              <div className="p-6 space-y-6">
                <div>
                  <label className="block mb-2 text-sm font-medium text-gray-900">
                    Role Name
                  </label>
                  <input
                    type="text"
                    className="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                    placeholder="Enter role name"
                    value={newRoleName}
                    onChange={(e) => setNewRoleName(e.target.value)}
                    required
                  />
                </div>
                <div>
                  <label className="block mb-2 text-sm font-medium text-gray-900">
                    Description
                  </label>
                  <textarea
                    className="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                    placeholder="Role description"
                    rows="3"
                    value={newRoleDescription}
                    onChange={(e) => setNewRoleDescription(e.target.value)}
                  ></textarea>
                </div>
              </div>
              <div className="flex items-center p-6 space-x-2 border-t border-gray-200 rounded-b">
                <button
                  type="button"
                  className="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center"
                  onClick={handleAddRole}
                >
                  Add Role
                </button>
                <button
                  type="button"
                  className="text-gray-500 bg-white hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-blue-300 rounded-lg border border-gray-200 text-sm font-medium px-5 py-2.5 hover:text-gray-900 focus:z-10"
                  onClick={() => setIsAddRoleModalOpen(false)}
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Delete Role Confirmation Modal */}
      {isDeleteRoleModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="relative p-4 w-full max-w-md max-h-full">
            <div className="relative bg-white rounded-lg shadow">
              <div className="p-6 text-center">
                <div className="flex justify-center mb-4">
                  <AlertTriangle className="w-12 h-12 text-red-500" />
                </div>
                <h3 className="mb-5 text-lg font-normal text-gray-500">
                  Are you sure you want to delete the {selectedRole.name} role?
                </h3>
                <div className="flex justify-center">
                  <button
                    type="button"
                    className="text-white bg-red-600 hover:bg-red-800 focus:ring-4 focus:outline-none focus:ring-red-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center mr-2"
                    onClick={handleDeleteRole}
                  >
                    Yes, delete it
                  </button>
                  <button
                    type="button"
                    className="text-gray-500 bg-white hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200 rounded-lg border border-gray-200 text-sm font-medium px-5 py-2.5 hover:text-gray-900 focus:z-10"
                    onClick={() => setIsDeleteRoleModalOpen(false)}
                  >
                    No, cancel
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
