import React, { createContext, useContext, useReducer, useCallback } from 'react';
import { bookingService, spaceService } from '../services';
import { useApp } from './AppContext';

// Initial state
const initialState = {
  bookings: [],
  userBookings: [],
  availableSpaces: [],
  selectedBooking: null,
  selectedSpace: null,
  bookingForm: {
    spaceId: '',
    startTime: '',
    endTime: '',
    date: '',
    purpose: '',
    attendees: 1,
    notes: '',
  },
  filters: {
    dateRange: {
      start: new Date().toISOString().split('T')[0],
      end: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    },
    status: 'all',
    spaceType: 'all',
  },
  loading: {
    bookings: false,
    spaces: false,
    creating: false,
    updating: false,
    canceling: false,
  },
  error: null,
};

// Action types
const actionTypes = {
  SET_BOOKINGS: 'SET_BOOKINGS',
  SET_USER_BOOKINGS: 'SET_USER_BOOKINGS',
  SET_AVAILABLE_SPACES: 'SET_AVAILABLE_SPACES',
  SET_SELECTED_BOOKING: 'SET_SELECTED_BOOKING',
  SET_SELECTED_SPACE: 'SET_SELECTED_SPACE',
  UPDATE_BOOKING_FORM: 'UPDATE_BOOKING_FORM',
  RESET_BOOKING_FORM: 'RESET_BOOKING_FORM',
  SET_FILTERS: 'SET_FILTERS',
  SET_LOADING: 'SET_LOADING',
  SET_ERROR: 'SET_ERROR',
  CLEAR_ERROR: 'CLEAR_ERROR',
  ADD_BOOKING: 'ADD_BOOKING',
  UPDATE_BOOKING: 'UPDATE_BOOKING',
  REMOVE_BOOKING: 'REMOVE_BOOKING',
};

// Reducer
const bookingReducer = (state, action) => {
  switch (action.type) {
    case actionTypes.SET_BOOKINGS:
      return { ...state, bookings: action.payload };
      
    case actionTypes.SET_USER_BOOKINGS:
      return { ...state, userBookings: action.payload };
      
    case actionTypes.SET_AVAILABLE_SPACES:
      return { ...state, availableSpaces: action.payload };
      
    case actionTypes.SET_SELECTED_BOOKING:
      return { ...state, selectedBooking: action.payload };
      
    case actionTypes.SET_SELECTED_SPACE:
      return { ...state, selectedSpace: action.payload };
      
    case actionTypes.UPDATE_BOOKING_FORM:
      return {
        ...state,
        bookingForm: { ...state.bookingForm, ...action.payload },
      };
      
    case actionTypes.RESET_BOOKING_FORM:
      return { ...state, bookingForm: initialState.bookingForm };
      
    case actionTypes.SET_FILTERS:
      return { ...state, filters: { ...state.filters, ...action.payload } };
      
    case actionTypes.SET_LOADING:
      return {
        ...state,
        loading: { ...state.loading, [action.payload.key]: action.payload.value },
      };
      
    case actionTypes.SET_ERROR:
      return { ...state, error: action.payload };
      
    case actionTypes.CLEAR_ERROR:
      return { ...state, error: null };
      
    case actionTypes.ADD_BOOKING:
      return {
        ...state,
        bookings: [action.payload, ...state.bookings],
        userBookings: [action.payload, ...state.userBookings],
      };
      
    case actionTypes.UPDATE_BOOKING:
      return {
        ...state,
        bookings: state.bookings.map(b =>
          b.id === action.payload.id ? action.payload : b
        ),
        userBookings: state.userBookings.map(b =>
          b.id === action.payload.id ? action.payload : b
        ),
      };
      
    case actionTypes.REMOVE_BOOKING:
      return {
        ...state,
        bookings: state.bookings.filter(b => b.id !== action.payload),
        userBookings: state.userBookings.filter(b => b.id !== action.payload),
      };
      
    default:
      return state;
  }
};

// Create context
const BookingContext = createContext();

// Provider component
export const BookingProvider = ({ children }) => {
  const [state, dispatch] = useReducer(bookingReducer, initialState);
  const { actions: appActions } = useApp();

  // Helper function to handle errors
  const handleError = useCallback((error, operation) => {
    console.error(`Booking ${operation} error:`, error);
    const errorMessage = error.response?.data?.message || error.message || `Failed to ${operation}`;
    dispatch({ type: actionTypes.SET_ERROR, payload: errorMessage });
    appActions.setError(errorMessage);
  }, [appActions]);

  // Action creators
  const actions = {
    // Load all bookings
    loadBookings: useCallback(async (params = {}) => {
      try {
        dispatch({ type: actionTypes.SET_LOADING, payload: { key: 'bookings', value: true } });
        dispatch({ type: actionTypes.CLEAR_ERROR });
        
        const response = await bookingService.getAllBookings(params);
        dispatch({ type: actionTypes.SET_BOOKINGS, payload: response.data });
      } catch (error) {
        handleError(error, 'load bookings');
      } finally {
        dispatch({ type: actionTypes.SET_LOADING, payload: { key: 'bookings', value: false } });
      }
    }, [handleError]),

    // Load user bookings
    loadUserBookings: useCallback(async (params = {}) => {
      try {
        dispatch({ type: actionTypes.SET_LOADING, payload: { key: 'bookings', value: true } });
        dispatch({ type: actionTypes.CLEAR_ERROR });
        
        const response = await bookingService.getUserBookings(params);
        dispatch({ type: actionTypes.SET_USER_BOOKINGS, payload: response.data });
      } catch (error) {
        handleError(error, 'load user bookings');
      } finally {
        dispatch({ type: actionTypes.SET_LOADING, payload: { key: 'bookings', value: false } });
      }
    }, [handleError]),

    // Load available spaces
    loadAvailableSpaces: useCallback(async (params = {}) => {
      try {
        dispatch({ type: actionTypes.SET_LOADING, payload: { key: 'spaces', value: true } });
        dispatch({ type: actionTypes.CLEAR_ERROR });
        
        const response = await spaceService.getAvailableSpaces(params);
        dispatch({ type: actionTypes.SET_AVAILABLE_SPACES, payload: response.data });
      } catch (error) {
        handleError(error, 'load available spaces');
      } finally {
        dispatch({ type: actionTypes.SET_LOADING, payload: { key: 'spaces', value: false } });
      }
    }, [handleError]),

    // Create booking
    createBooking: useCallback(async (bookingData) => {
      try {
        dispatch({ type: actionTypes.SET_LOADING, payload: { key: 'creating', value: true } });
        dispatch({ type: actionTypes.CLEAR_ERROR });
        
        const response = await bookingService.createBooking(bookingData);
        dispatch({ type: actionTypes.ADD_BOOKING, payload: response.data });
        dispatch({ type: actionTypes.RESET_BOOKING_FORM });
        
        appActions.addNotification({
          id: Date.now(),
          type: 'success',
          title: 'Booking Created',
          message: 'Your booking has been created successfully.',
          timestamp: new Date().toISOString(),
        });
        
        return response.data;
      } catch (error) {
        handleError(error, 'create booking');
        throw error;
      } finally {
        dispatch({ type: actionTypes.SET_LOADING, payload: { key: 'creating', value: false } });
      }
    }, [handleError, appActions]),

    // Update booking
    updateBooking: useCallback(async (bookingId, bookingData) => {
      try {
        dispatch({ type: actionTypes.SET_LOADING, payload: { key: 'updating', value: true } });
        dispatch({ type: actionTypes.CLEAR_ERROR });
        
        const response = await bookingService.updateBooking(bookingId, bookingData);
        dispatch({ type: actionTypes.UPDATE_BOOKING, payload: response.data });
        
        appActions.addNotification({
          id: Date.now(),
          type: 'success',
          title: 'Booking Updated',
          message: 'Your booking has been updated successfully.',
          timestamp: new Date().toISOString(),
        });
        
        return response.data;
      } catch (error) {
        handleError(error, 'update booking');
        throw error;
      } finally {
        dispatch({ type: actionTypes.SET_LOADING, payload: { key: 'updating', value: false } });
      }
    }, [handleError, appActions]),

    // Cancel booking
    cancelBooking: useCallback(async (bookingId) => {
      try {
        dispatch({ type: actionTypes.SET_LOADING, payload: { key: 'canceling', value: true } });
        dispatch({ type: actionTypes.CLEAR_ERROR });
        
        await bookingService.cancelBooking(bookingId);
        dispatch({ type: actionTypes.REMOVE_BOOKING, payload: bookingId });
        
        appActions.addNotification({
          id: Date.now(),
          type: 'info',
          title: 'Booking Cancelled',
          message: 'Your booking has been cancelled.',
          timestamp: new Date().toISOString(),
        });
      } catch (error) {
        handleError(error, 'cancel booking');
        throw error;
      } finally {
        dispatch({ type: actionTypes.SET_LOADING, payload: { key: 'canceling', value: false } });
      }
    }, [handleError, appActions]),

    // Extend booking
    extendBooking: useCallback(async (bookingId, extensionData) => {
      try {
        dispatch({ type: actionTypes.SET_LOADING, payload: { key: 'updating', value: true } });
        dispatch({ type: actionTypes.CLEAR_ERROR });
        
        const response = await bookingService.extendBooking(bookingId, extensionData);
        dispatch({ type: actionTypes.UPDATE_BOOKING, payload: response.data });
        
        appActions.addNotification({
          id: Date.now(),
          type: 'success',
          title: 'Booking Extended',
          message: 'Your booking has been extended successfully.',
          timestamp: new Date().toISOString(),
        });
        
        return response.data;
      } catch (error) {
        handleError(error, 'extend booking');
        throw error;
      } finally {
        dispatch({ type: actionTypes.SET_LOADING, payload: { key: 'updating', value: false } });
      }
    }, [handleError, appActions]),

    // UI actions
    setSelectedBooking: (booking) => dispatch({ type: actionTypes.SET_SELECTED_BOOKING, payload: booking }),
    setSelectedSpace: (space) => dispatch({ type: actionTypes.SET_SELECTED_SPACE, payload: space }),
    updateBookingForm: (formData) => dispatch({ type: actionTypes.UPDATE_BOOKING_FORM, payload: formData }),
    resetBookingForm: () => dispatch({ type: actionTypes.RESET_BOOKING_FORM }),
    setFilters: (filters) => dispatch({ type: actionTypes.SET_FILTERS, payload: filters }),
    clearError: () => dispatch({ type: actionTypes.CLEAR_ERROR }),
  };

  return (
    <BookingContext.Provider value={{ state, actions }}>
      {children}
    </BookingContext.Provider>
  );
};

// Custom hook to use the context
export const useBooking = () => {
  const context = useContext(BookingContext);
  if (!context) {
    throw new Error('useBooking must be used within a BookingProvider');
  }
  return context;
};

export default BookingContext;
