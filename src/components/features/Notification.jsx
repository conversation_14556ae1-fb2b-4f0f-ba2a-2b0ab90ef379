import { useState, useEffect } from 'react';
import { 
  Bell, 
  <PERSON>ting<PERSON>, 
  Trash2, 
  CheckCircle, 
  AlertCircle, 
  Info, 
  Calendar, 
  Users, 
  MessageSquare,
  Clock,
  Filter,
  Search,
  MoreVertical,
  X,
  Eye,
  EyeOff,
  RefreshCw,
  Star,
  StarOff
} from 'lucide-react';

const Notifications = () => {
  const [notifications, setNotifications] = useState([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('all');
  const [filter, setFilter] = useState('');
  const [showFilterPanel, setShowFilterPanel] = useState(false);
  const [selectedNotifications, setSelectedNotifications] = useState([]);
  const [selectedDateRange, setSelectedDateRange] = useState('all');
  const [selectedTypes, setSelectedTypes] = useState([]);
  
  // Notification preferences
  const [preferences, setPreferences] = useState({
    email: true,
    push: true,
    inApp: true,
    bookingCreated: true,
    bookingCancelled: true,
    bookingModified: true,
    systemUpdates: true,
    userMentions: true
  });
  
  // Sample notification data
  useEffect(() => {
    // Simulate API fetch
    setTimeout(() => {
      setNotifications([
        {
          id: 1,
          type: 'booking',
          subtype: 'created',
          title: 'New Booking Request',
          message: 'Conference Room A has been booked by Alex Johnson for tomorrow at 2:00 PM',
          timestamp: new Date(Date.now() - 25 * 60000).toISOString(),
          isRead: false,
          isStarred: true,
          priority: 'high',
          user: {
            id: 1,
            name: 'Alex Johnson',
            avatar: 'AJ'
          }
        },
        {
          id: 2,
          type: 'system',
          subtype: 'update',
          title: 'System Maintenance',
          message: 'The workspace booking system will be undergoing maintenance this Saturday from 2:00 AM to 4:00 AM EST.',
          timestamp: new Date(Date.now() - 2 * 3600000).toISOString(),
          isRead: true,
          isStarred: false,
          priority: 'medium',
          user: {
            id: 0,
            name: 'System',
            avatar: 'SYS'
          }
        },
        {
          id: 3,
          type: 'alert',
          subtype: 'space',
          title: 'Space Availability Alert',
          message: 'Design Studio is now available for booking on Friday after a cancellation.',
          timestamp: new Date(Date.now() - 1 * 86400000).toISOString(),
          isRead: false,
          isStarred: false,
          priority: 'medium',
          user: {
            id: 0,
            name: 'System',
            avatar: 'SYS'
          }
        },
        {
          id: 4,
          type: 'booking',
          subtype: 'cancelled',
          title: 'Booking Cancelled',
          message: 'Your booking for Meeting Room B on Friday at 10:00 AM has been cancelled.',
          timestamp: new Date(Date.now() - 2 * 86400000).toISOString(),
          isRead: true,
          isStarred: false,
          priority: 'low',
          user: {
            id: 2,
            name: 'Sarah Parker',
            avatar: 'SP'
          }
        },
        {
          id: 5,
          type: 'mention',
          subtype: 'comment',
          title: 'You were mentioned in a comment',
          message: 'Sarah Parker mentioned you in a comment: "Can you confirm this booking time works for your team?"',
          timestamp: new Date(Date.now() - 3 * 86400000).toISOString(),
          isRead: true,
          isStarred: true,
          priority: 'high',
          user: {
            id: 2,
            name: 'Sarah Parker',
            avatar: 'SP'
          }
        },
        {
          id: 6,
          type: 'booking',
          subtype: 'modified',
          title: 'Booking Modified',
          message: 'Your booking for Conference Room C has been rescheduled to Monday at 1:00 PM',
          timestamp: new Date(Date.now() - 4 * 86400000).toISOString(),
          isRead: true,
          isStarred: false,
          priority: 'medium',
          user: {
            id: 3,
            name: 'Michael Chen',
            avatar: 'MC'
          }
        },
        {
          id: 7,
          type: 'alert',
          subtype: 'capacity',
          title: 'Capacity Alert',
          message: 'Main Conference Room is approaching capacity limits for tomorrow.',
          timestamp: new Date(Date.now() - 5 * 86400000).toISOString(),
          isRead: true,
          isStarred: false,
          priority: 'medium',
          user: {
            id: 0,
            name: 'System',
            avatar: 'SYS'
          }
        }
      ]);
      setLoading(false);
    }, 800);
  }, []);

  // Toggle notification read status
  const toggleReadStatus = (id) => {
    setNotifications(notifications.map(notification => 
      notification.id === id ? { ...notification, isRead: !notification.isRead } : notification
    ));
  };

  // Toggle notification star status
  const toggleStarStatus = (id) => {
    setNotifications(notifications.map(notification => 
      notification.id === id ? { ...notification, isStarred: !notification.isStarred } : notification
    ));
  };

  // Delete notification
  const deleteNotification = (id) => {
    setNotifications(notifications.filter(notification => notification.id !== id));
    setSelectedNotifications(selectedNotifications.filter(notifId => notifId !== id));
  };

  // Delete selected notifications
  const deleteSelected = () => {
    setNotifications(notifications.filter(notification => !selectedNotifications.includes(notification.id)));
    setSelectedNotifications([]);
  };

  // Mark all as read
  const markAllAsRead = () => {
    setNotifications(notifications.map(notif => ({ ...notif, isRead: true })));
  };

  // Mark selected as read
  const markSelectedAsRead = () => {
    setNotifications(notifications.map(notification => 
      selectedNotifications.includes(notification.id) 
        ? { ...notification, isRead: true } 
        : notification
    ));
    setSelectedNotifications([]);
  };

  // Toggle notification selection
  const toggleSelection = (id) => {
    if (selectedNotifications.includes(id)) {
      setSelectedNotifications(selectedNotifications.filter(notifId => notifId !== id));
    } else {
      setSelectedNotifications([...selectedNotifications, id]);
    }
  };

  // Select/Deselect all visible notifications
  const toggleSelectAll = () => {
    if (selectedNotifications.length === filteredNotifications.length) {
      setSelectedNotifications([]);
    } else {
      setSelectedNotifications(filteredNotifications.map(notification => notification.id));
    }
  };

  // Toggle filter panel
  const toggleFilterPanel = () => {
    setShowFilterPanel(!showFilterPanel);
  };

  // Toggle notification type filter
  const toggleTypeFilter = (type) => {
    if (selectedTypes.includes(type)) {
      setSelectedTypes(selectedTypes.filter(t => t !== type));
    } else {
      setSelectedTypes([...selectedTypes, type]);
    }
  };

  // Format timestamp to relative time
  const formatTimeAgo = (timestamp) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);
    
    if (diffInSeconds < 60) {
      return 'Just now';
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60);
      return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
    } else if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600);
      return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    } else {
      const days = Math.floor(diffInSeconds / 86400);
      if (days === 1) return 'Yesterday';
      if (days < 7) return `${days} days ago`;
      
      // For older notifications, show the actual date
      return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
    }
  };

  // Filter notifications based on active tab and search
  const getFilteredNotifications = () => {
    let filtered = [...notifications];
    
    if (activeTab === 'unread') {
      filtered = filtered.filter(notification => !notification.isRead);
    } else if (activeTab === 'starred') {
      filtered = filtered.filter(notification => notification.isStarred);
    }
    
    if (filter) {
      const searchTerm = filter.toLowerCase();
      filtered = filtered.filter(notification => 
        notification.title.toLowerCase().includes(searchTerm) || 
        notification.message.toLowerCase().includes(searchTerm)
      );
    }
    
    if (selectedDateRange !== 'all') {
      const now = new Date();
      let cutoff;
      
      switch (selectedDateRange) {
        case 'today':
          cutoff = new Date(now.setHours(0, 0, 0, 0));
          break;
        case 'week':
          cutoff = new Date(now.setDate(now.getDate() - 7));
          break;
        case 'month':
          cutoff = new Date(now.setMonth(now.getMonth() - 1));
          break;
        default:
          cutoff = null;
      }
      
      if (cutoff) {
        filtered = filtered.filter(notification => new Date(notification.timestamp) >= cutoff);
      }
    }
    
    if (selectedTypes.length > 0) {
      filtered = filtered.filter(notification => selectedTypes.includes(notification.type));
    }
    
    return filtered;
  };
  
  const filteredNotifications = getFilteredNotifications();
  
  // Determine the icon for notification type
  const getNotificationIcon = (type, priority) => {
    const color = priority === 'high' ? 'text-red-500' : priority === 'medium' ? 'text-amber-500' : 'text-blue-500';
    
    switch (type) {
      case 'booking':
        return <Calendar size={18} className={color} />;
      case 'system':
        return <Settings size={18} className={color} />;
      case 'alert':
        return <AlertCircle size={18} className={color} />;
      case 'mention':
        return <MessageSquare size={18} className={color} />;
      default:
        return <Info size={18} className={color} />;
    }
  };

  // Get badge color for notification type
  const getTypeBadgeColor = (type) => {
    switch (type) {
      case 'booking':
        return 'bg-blue-100 text-blue-700';
      case 'system':
        return 'bg-purple-100 text-purple-700';
      case 'alert':
        return 'bg-red-100 text-red-700';
      case 'mention':
        return 'bg-green-100 text-green-700';
      default:
        return 'bg-gray-100 text-gray-700';
    }
  };

  // Get badge color for notification subtype
  const getSubtypeBadgeColor = (subtype) => {
    switch (subtype) {
      case 'created':
        return 'bg-green-100 text-green-700';
      case 'cancelled':
        return 'bg-red-100 text-red-700';
      case 'modified':
        return 'bg-amber-100 text-amber-700';
      case 'update':
        return 'bg-purple-100 text-purple-700';
      case 'space':
      case 'capacity':
        return 'bg-orange-100 text-orange-700';
      case 'comment':
        return 'bg-teal-100 text-teal-700';
      default:
        return 'bg-gray-100 text-gray-700';
    }
  };

  // Display avatar for user
  const UserAvatar = ({ user }) => (
    <div className="h-8 w-8 rounded-full bg-blue-100 text-blue-700 flex items-center justify-center font-medium text-sm">
      {user.avatar}
    </div>
  );

  // Render notification tabs
  const renderTabs = () => (
    <div className="flex border-b border-gray-200 mb-4">
      <button
        className={`px-4 py-2 font-medium text-sm ${
          activeTab === 'all' 
            ? 'text-blue-700 border-b-2 border-blue-500' 
            : 'text-gray-600 hover:text-blue-700'
        }`}
        onClick={() => setActiveTab('all')}
      >
        All
      </button>
      <button
        className={`px-4 py-2 font-medium text-sm ${
          activeTab === 'unread' 
            ? 'text-blue-700 border-b-2 border-blue-500' 
            : 'text-gray-600 hover:text-blue-700'
        }`}
        onClick={() => setActiveTab('unread')}
      >
        Unread {notifications.filter(n => !n.isRead).length > 0 && (
          <span className="ml-1 px-2 py-0.5 text-xs bg-blue-100 text-blue-700 rounded-full">
            {notifications.filter(n => !n.isRead).length}
          </span>
        )}
      </button>
      <button
        className={`px-4 py-2 font-medium text-sm ${
          activeTab === 'starred' 
            ? 'text-blue-700 border-b-2 border-blue-500' 
            : 'text-gray-600 hover:text-blue-700'
        }`}
        onClick={() => setActiveTab('starred')}
      >
        Starred
      </button>
    </div>
  );

  // Render filter options
  const renderFilterPanel = () => {
    if (!showFilterPanel) return null;
    
    return (
      <div className="bg-white p-4 mb-4 rounded-lg shadow-md border border-blue-100 animate-fadeIn">
        <div className="flex justify-between items-center mb-3">
          <h3 className="font-medium text-blue-700">Filter Notifications</h3>
          <button 
            className="text-gray-500 hover:text-gray-700"
            onClick={toggleFilterPanel}
          >
            <X size={18} />
          </button>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-blue-700 mb-2">Date Range</label>
            <div className="space-y-2">
              {['all', 'today', 'week', 'month'].map(range => (
                <div key={range} className="flex items-center">
                  <input
                    type="radio"
                    id={`date-${range}`}
                    name="dateRange"
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    checked={selectedDateRange === range}
                    onChange={() => setSelectedDateRange(range)}
                  />
                  <label htmlFor={`date-${range}`} className="ml-2 block text-sm text-gray-700 capitalize">
                    {range === 'all' ? 'All Time' : range === 'week' ? 'Last 7 Days' : range === 'month' ? 'Last 30 Days' : 'Today'}
                  </label>
                </div>
              ))}
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-blue-700 mb-2">Notification Type</label>
            <div className="space-y-2">
              {['booking', 'system', 'alert', 'mention'].map(type => (
                <div key={type} className="flex items-center">
                  <input
                    type="checkbox"
                    id={`type-${type}`}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    checked={selectedTypes.includes(type)}
                    onChange={() => toggleTypeFilter(type)}
                  />
                  <label htmlFor={`type-${type}`} className="ml-2 block text-sm text-gray-700 capitalize">
                    {type}
                  </label>
                </div>
              ))}
            </div>
          </div>
        </div>
        
        <div className="flex justify-end mt-4">
          <button
            className="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors"
            onClick={() => {
              setSelectedDateRange('all');
              setSelectedTypes([]);
            }}
          >
            Clear Filters
          </button>
        </div>
      </div>
    );
  };

  // Render notification list
  const renderNotifications = () => {
    if (loading) {
      return (
        <div className="flex flex-col items-center justify-center py-12">
          <div className="animate-spin mb-3">
            <RefreshCw size={24} className="text-blue-600" />
          </div>
          <p className="text-gray-600">Loading notifications...</p>
        </div>
      );
    }
    
    if (filteredNotifications.length === 0) {
      return (
        <div className="flex flex-col items-center justify-center py-12 bg-white rounded-lg">
          <div className="mb-3 p-3 bg-blue-100 rounded-full">
            <Bell size={24} className="text-blue-600" />
          </div>
          <h3 className="text-lg font-medium text-blue-700 mb-1">No notifications found</h3>
          <p className="text-gray-600 text-center max-w-md">
            {activeTab === 'all' && filter 
              ? 'No notifications match your search criteria.' 
              : activeTab === 'unread' 
                ? 'You have no unread notifications.' 
                : activeTab === 'starred'
                  ? 'You have no starred notifications.'
                  : 'You have no notifications at this time.'}
          </p>
        </div>
      );
    }
    
    return (
      <div className="space-y-2">
        {filteredNotifications.map(notification => (
          <div 
            key={notification.id} 
            className={`bg-white rounded-lg border-l-4 p-4 shadow-sm hover:shadow-md transition-shadow ${
              notification.isRead ? 'border-l-gray-300' : 'border-l-blue-500'
            }`}
          >
            <div className="flex items-start">
              <div className="mr-3">
                <input
                  type="checkbox"
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  checked={selectedNotifications.includes(notification.id)}
                  onChange={() => toggleSelection(notification.id)}
                />
              </div>
              
              <div className="mr-3 pt-2">
                {getNotificationIcon(notification.type, notification.priority)}
              </div>
              
              <div className="flex-1">
                <div className="flex justify-between items-start">
                  <h3 className={`font-medium ${notification.isRead ? 'text-gray-700' : 'text-blue-700'}`}>
                    {notification.title}
                  </h3>
                  <div className="text-xs text-gray-500">
                    {formatTimeAgo(notification.timestamp)}
                  </div>
                </div>
                
                <p className={`text-sm ${notification.isRead ? 'text-gray-600' : 'text-gray-800'}`}>
                  {notification.message}
                </p>
                
                <div className="flex items-center justify-between mt-2">
                  <div className="flex items-center space-x-2">
                    <span className={`text-xs px-2 py-0.5 rounded-full ${getTypeBadgeColor(notification.type)}`}>
                      {notification.type.charAt(0).toUpperCase() + notification.type.slice(1)}
                    </span>
                    <span className={`text-xs px-2 py-0.5 rounded-full ${getSubtypeBadgeColor(notification.subtype)}`}>
                      {notification.subtype.charAt(0).toUpperCase() + notification.subtype.slice(1)}
                    </span>
                  </div>
                  
                  <div className="flex items-center">
                    {notification.user.id !== 0 && (
                      <div className="mr-2" title={notification.user.name}>
                        <UserAvatar user={notification.user} />
                      </div>
                    )}
                    
                    <div className="flex space-x-1">
                      <button 
                        className="p-1 rounded-full hover:bg-gray-100"
                        onClick={() => toggleStarStatus(notification.id)}
                        title={notification.isStarred ? "Unstar" : "Star"}
                      >
                        {notification.isStarred ? (
                          <Star size={16} className="text-amber-500" fill="#f59e0b" />
                        ) : (
                          <StarOff size={16} className="text-gray-400" />
                        )}
                      </button>
                      
                      <button 
                        className="p-1 rounded-full hover:bg-gray-100"
                        onClick={() => toggleReadStatus(notification.id)}
                        title={notification.isRead ? "Mark as unread" : "Mark as read"}
                      >
                        {notification.isRead ? (
                          <EyeOff size={16} className="text-gray-400" />
                        ) : (
                          <Eye size={16} className="text-blue-600" />
                        )}
                      </button>
                      
                      <button 
                        className="p-1 rounded-full hover:bg-gray-100 hover:text-red-600"
                        onClick={() => deleteNotification(notification.id)}
                        title="Delete"
                      >
                        <Trash2 size={16} className="text-gray-400 hover:text-red-600" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  };

  // Render notification preferences
  const renderPreferences = () => (
    <div className="bg-white rounded-lg shadow-md p-6 mb-6">
      <h3 className="text-lg font-medium text-blue-700 mb-4 flex items-center">
        <Settings size={18} className="mr-2" />
        Notification Preferences
      </h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <h4 className="font-medium text-blue-700 mb-3 text-sm">Delivery Methods</h4>
          <div className="space-y-3">
            {[
              { id: 'email', label: 'Email Notifications' },
              { id: 'push', label: 'Push Notifications' },
              { id: 'inApp', label: 'In-App Notifications' }
            ].map(option => (
              <div key={option.id} className="flex items-center justify-between">
                <span className="text-sm text-gray-700">{option.label}</span>
                <div className="relative inline-block w-10 align-middle select-none">
                  <input 
                    type="checkbox" 
                    id={option.id} 
                    className="sr-only"
                    checked={preferences[option.id]}
                    onChange={() => setPreferences({
                      ...preferences,
                      [option.id]: !preferences[option.id]
                    })}
                  />
                  <label 
                    htmlFor={option.id}
                    className={`block overflow-hidden h-6 rounded-full cursor-pointer transition-colors ${
                      preferences[option.id] ? 'bg-blue-600' : 'bg-gray-300'
                    }`}
                  >
                    <span 
                      className={`block h-6 w-6 rounded-full bg-white shadow transform transition-transform ${
                        preferences[option.id] ? 'translate-x-4' : 'translate-x-0'
                      }`} 
                    />
                  </label>
                </div>
              </div>
            ))}
          </div>
        </div>
        
        <div>
          <h4 className="font-medium text-blue-700 mb-3 text-sm">Notification Types</h4>
          <div className="space-y-3">
            {[
              { id: 'bookingCreated', label: 'Booking Created' },
              { id: 'bookingCancelled', label: 'Booking Cancelled' },
              { id: 'bookingModified', label: 'Booking Modified' },
              { id: 'systemUpdates', label: 'System Updates' },
              { id: 'userMentions', label: 'User Mentions' }
            ].map(option => (
              <div key={option.id} className="flex items-center justify-between">
                <span className="text-sm text-gray-700">{option.label}</span>
                <div className="relative inline-block w-10 align-middle select-none">
                  <input 
                    type="checkbox" 
                    id={option.id} 
                    className="sr-only"
                    checked={preferences[option.id]}
                    onChange={() => setPreferences({
                      ...preferences,
                      [option.id]: !preferences[option.id]
                    })}
                  />
                  <label 
                    htmlFor={option.id}
                    className={`block overflow-hidden h-6 rounded-full cursor-pointer transition-colors ${
                      preferences[option.id] ? 'bg-blue-600' : 'bg-gray-300'
                    }`}
                  >
                    <span 
                      className={`block h-6 w-6 rounded-full bg-white shadow transform transition-transform ${
                        preferences[option.id] ? 'translate-x-4' : 'translate-x-0'
                      }`} 
                    />
                  </label>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-blue-50 p-6">
      <div className="max-w-5xl mx-auto">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-blue-700 flex items-center">
            <Bell className="mr-2" /> 
            Notifications
          </h1>
          
          <div className="flex items-center space-x-2">
            {selectedNotifications.length > 0 && (
              <div className="flex items-center bg-white rounded-md shadow-sm mr-2">
                <button
                  className="px-3 py-2 text-sm text-blue-700 hover:bg-blue-50 rounded-l-md border-r border-gray-200"
                  onClick={markSelectedAsRead}
                  title="Mark selected as read"
                >
                  <CheckCircle size={16} />
                </button>
                <button
                  className="px-3 py-2 text-sm text-red-600 hover:bg-red-50 rounded-r-md"
                  onClick={deleteSelected}
                  title="Delete selected"
                >
                  <Trash2 size={16} />
                </button>
              </div>
            )}
            
            <button
              className="px-3 py-2 text-sm bg-white text-blue-700 rounded-md shadow-sm hover:bg-blue-50 flex items-center"
              onClick={toggleFilterPanel}
            >
              <Filter size={16} className="mr-1" />
              Filter
            </button>
            
            <button
              className="px-3 py-2 text-sm bg-white text-blue-700 rounded-md shadow-sm hover:bg-blue-50"
              onClick={markAllAsRead}
            >
              Mark all as read
            </button>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-md p-4 mb-6">
          <div className="flex items-start space-x-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Bell size={24} className="text-blue-600" />
            </div>
            <div>
              <h2 className="text-lg font-medium text-blue-800">Notification Center</h2>
              <p className="text-sm text-gray-600 mt-1">
                Stay updated with booking changes, system alerts, and mentions from your team.
              </p>
            </div>
          </div>
        </div>
        
        {renderPreferences()}
        
        <div className="bg-white rounded-lg shadow-md p-4 mb-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center">
              <input
                type="checkbox"
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                checked={selectedNotifications.length === filteredNotifications.length && filteredNotifications.length > 0}
                onChange={toggleSelectAll}
              />
              <label className="ml-2 text-sm text-gray-600">
                {selectedNotifications.length > 0 ? `Selected (${selectedNotifications.length})` : 'Select all'}
              </label>
            </div>
            
            <div className="flex items-center border border-gray-200 rounded-md overflow-hidden">
              <span className="flex items-center pl-3 pr-1 text-gray-500">
                <Search size={16} />
              </span>
              <input
                type="text"
                placeholder="Search notifications..."
                className="px-2 py-1.5 text-sm border-none focus:ring-0 focus:outline-none"
                value={filter}
                onChange={(e) => setFilter(e.target.value)}
              />
              {filter && (
                <button 
                  className="px-2 text-gray-400 hover:text-gray-600"
                  onClick={() => setFilter('')}
                >
                  <X size={16} />
                </button>
              )}
            </div>
          </div>
          
          {renderTabs()}
          {renderFilterPanel()}
          {renderNotifications()}
        </div>
      </div>
    </div>
  );
};

export default Notifications;